# Project Structure

## Root Directory
```
openfitv2/
├── lib/                    # Main application code
├── test/                   # Unit and widget tests
├── ios/                    # iOS platform-specific code
├── android/                # Android platform-specific code
├── web/                    # Web platform assets
├── macos/                  # macOS platform-specific code
├── linux/                  # Linux platform-specific code
├── windows/                # Windows platform-specific code
├── pubspec.yaml            # Dependencies and project config
├── analysis_options.yaml   # Dart linting configuration
└── .cursorrules           # Development guidelines
```

## Core Application Structure (`lib/`)

### Architecture Layers
```
lib/
├── main.dart              # App entry point and initialization
├── components/            # Reusable UI components
├── config/               # App configuration (Supabase, etc.)
├── design_system/        # Design tokens and theme system
├── models/               # Data models and entities
├── providers/            # State management (Provider pattern)
├── screens/              # Full-screen UI pages
├── services/             # Business logic and API integration
├── utils/                # Helper functions and utilities
└── widgets/              # Small reusable UI widgets
```

### Key Directories

#### `/lib/components/`
Reusable UI components with consistent styling:
- `app_app_bar.dart` - Custom app bar
- `app_button.dart` - Styled buttons
- `app_card.dart` - Card components
- `app_scaffold.dart` - Base scaffold wrapper
- `auth_scaffold.dart` - Authentication screen wrapper

#### `/lib/design_system/`
Centralized design tokens and theming:
- `design_system.dart` - Main export file
- `app_colors.dart` - Color palette
- `app_typography.dart` - Text styles
- `app_spacing.dart` - Spacing constants
- `app_shadows.dart` - Shadow definitions

#### `/lib/screens/`
Full-screen pages organized by feature:
- `auth/` - Authentication screens
- `onboarding/` - User onboarding flow
- `dev/` - Development/debug screens
- Main app screens (home, workouts, profile, etc.)

#### `/lib/services/`
Business logic and external integrations:
- `auth_service.dart` - Authentication logic
- `profile_service.dart` - User profile management
- `workout_service.dart` - Workout data management
- `theme_service.dart` - Theme persistence

#### `/lib/models/`
Data structures and entities:
- `exercise.dart` - Exercise data model
- `workout.dart` - Workout data model
- `onboarding_data.dart` - Onboarding state model

## Naming Conventions
- **Files**: `snake_case.dart`
- **Classes**: `PascalCase`
- **Variables/Functions**: `camelCase`
- **Constants**: `SCREAMING_SNAKE_CASE`
- **Private members**: `_leadingUnderscore`

## Import Organization
1. Dart/Flutter imports first
2. Third-party package imports
3. Local project imports
4. Relative imports last

## State Management Pattern
- Use Provider pattern throughout
- Services extend `ChangeNotifier`
- UI consumes via `Consumer<T>` or `context.watch<T>()`
- Separate business logic from UI components