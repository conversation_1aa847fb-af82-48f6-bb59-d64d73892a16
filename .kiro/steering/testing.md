---
inclusion: always
---

# Testing & Development Guidelines

## Critical Rules for AI Assistant

### App Execution Policy
- **NEVER** run `flutter run` automatically - user controls app execution
- Always run `flutter analyze` to check for syntax issues before code changes
- Use `dart format .` to ensure consistent code formatting
- Ask user to manually refresh/restart app to avoid interrupting development flow

### Required Code Quality Checks
```bash
# Always run after making changes
flutter analyze

# Format code consistently  
dart format .

# Check dependencies
flutter pub outdated
```



## Testing Architecture

### Test Organization Structure
```
test/
├── unit/              # Services, models, utils business logic
├── widget/            # UI components and screens
├── integration/       # Complete user flows
└── mocks/            # Shared test helpers and mocks
```

### Testing Patterns by Layer

#### Widget Tests (`testWidgets()`)
- Test UI components in `lib/components/` and `lib/widgets/`
- Verify user interactions and state changes
- Test accessibility properties (semantics, contrast)
- Mock Provider dependencies and Supabase calls

#### Unit Tests
- Test business logic in `lib/services/` 
- Test data models in `lib/models/`
- Mock external dependencies (Supabase, storage)
- Test error handling and edge cases

#### Integration Tests
- Test complete user flows (auth, onboarding, workout tracking)
- Verify Provider state management behavior
- Test navigation between screens

### OpenFit-Specific Testing Requirements

#### Authentication Testing
- Mock Supabase Auth calls in `AuthService`
- Test login, signup, password reset flows
- Verify secure storage integration

#### Workout Testing  
- Mock workout data from Supabase
- Test workout creation, tracking, completion
- Verify progress calculations

#### Theme Testing
- Test light/dark mode switching
- Verify theme persistence via `ThemeService`
- Test design system color compliance

### Development Workflow for AI
1. Run `flutter analyze` before any code changes
2. Write failing tests first (TDD approach)
3. Implement minimal code to pass tests
4. Run `dart format .` for consistent formatting
5. Ensure all tests pass before completing task

### Debugging Guidelines
- Use `debugPrint()` instead of `print()` for debug output
- Leverage Flutter Inspector for UI debugging
- Test accessibility compliance in widget tests
- Focus on iOS simulator testing (primary platform)