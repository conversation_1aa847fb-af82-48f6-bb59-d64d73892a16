# Technology Stack

## Framework & Language
- **Flutter**: 3.32+
- **Dart**: 3.8+
- **Target iOS**: 12.0+

## Backend & Services
- **Backend**: Supabase
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage

## Key Dependencies
- `supabase_flutter: ^2.8.1` - Backend integration
- `provider: ^6.1.2` - State management
- `flutter_secure_storage: ^9.2.2` - Secure local storage
- `email_validator: ^3.0.0` - Form validation
- `intl: ^0.19.0` - Date formatting
- `shared_preferences: ^2.5.3` - Local preferences

## Development Tools
- `flutter_lints: ^5.0.0` - Code linting
- `flutter_test` - Testing framework

## Common Commands

### Development
```bash
# Setup
flutter pub get
cd ios && pod install && cd ..

# Run app
flutter run

# Testing
flutter test
flutter analyze
dart format .
```

### iOS Build & Deploy
```bash
# Clean build
flutter clean
flutter pub get
cd ios && pod install && cd ..

# Build for release
flutter build ios --release
flutter build ipa --release

# Set PATH for Homebrew (if needed)
export PATH="/opt/homebrew/bin:$PATH"
```

### Code Quality
```bash
# Analysis
flutter analyze
dart format .
flutter test --coverage
```

## Build System
- **iOS**: Xcode with CocoaPods for dependency management
- **Android**: Gradle build system
- **Code Signing**: Automatic (iOS)

## Environment Setup
- Requires `.env` file with Supabase credentials
- Uses `analysis_options.yaml` for linting configuration
- CocoaPods required for iOS dependencies