# Product Overview

OpenFit v2 is a comprehensive Flutter-based fitness application designed for cross-platform deployment with a primary focus on iOS distribution through TestFlight.

## Core Features
- User authentication and profile management
- Onboarding flow for new users
- Workout tracking and management
- Progress monitoring
- Theme customization (light/dark mode)

## Target Platforms
- **Primary**: iOS (12.0+)
- **Secondary**: Android, Web, macOS, Linux, Windows

## Backend & Infrastructure
- **Backend**: Supabase (PostgreSQL database)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **State Management**: Provider pattern

## App Identity
- **Bundle ID**: `com.abenezernuro.agenticfit`
- **Team ID**: `HP284BJ924`
- **Version**: 1.1.0+3

## Key User Flows
1. Authentication (login/signup/password reset)
2. Onboarding for new users
3. Main app navigation with bottom tabs
4. Workout creation and tracking
5. Progress monitoring and analytics