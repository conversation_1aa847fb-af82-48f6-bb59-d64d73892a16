import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../lib/widgets/personal_record_display.dart';
import '../../lib/models/session_analytics.dart';

void main() {
  group('PersonalRecordDisplay Widget Tests', () {
    testWidgets('displays empty state when no records',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: PersonalRecordDisplay(personalRecords: []),
          ),
        ),
      );

      expect(find.byType(PersonalRecordDisplay), findsOneWidget);
      expect(find.text('Personal Records'), findsNothing);
    });

    testWidgets('displays personal records correctly',
        (WidgetTester tester) async {
      final records = [
        PersonalRecord(
          exerciseId: '1',
          exerciseName: 'Bench Press',
          type: PersonalRecordType.maxWeight,
          value: 225.0,
          achievedDate: DateTime.now(),
          previousValue: 200.0,
        ),
        PersonalRecord(
          exerciseId: '2',
          exerciseName: 'Squat',
          type: PersonalRecordType.maxReps,
          value: 15.0,
          achievedDate: DateTime.now(),
          previousValue: 12.0,
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: PersonalRecordDisplay(personalRecords: records),
          ),
        ),
      );

      expect(find.text('Personal Records'), findsOneWidget);
      expect(find.text('2 new records!'), findsOneWidget);
      expect(find.text('Bench Press'), findsOneWidget);
      expect(find.text('Squat'), findsOneWidget);
      expect(find.text('225.0 lbs'), findsOneWidget);
      expect(find.text('15 reps'), findsOneWidget);
    });

    testWidgets('displays improvement percentages',
        (WidgetTester tester) async {
      final record = PersonalRecord(
        exerciseId: '1',
        exerciseName: 'Deadlift',
        type: PersonalRecordType.maxWeight,
        value: 315.0,
        achievedDate: DateTime.now(),
        previousValue: 300.0,
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: PersonalRecordDisplay(personalRecords: [record]),
          ),
        ),
      );

      expect(find.text('Deadlift'), findsOneWidget);
      expect(find.text('315.0 lbs'), findsOneWidget);
      expect(find.text('+5.0%'), findsOneWidget);
    });

    testWidgets('shows correct icons for different record types',
        (WidgetTester tester) async {
      final records = [
        PersonalRecord(
          exerciseId: '1',
          exerciseName: 'Bench Press',
          type: PersonalRecordType.maxWeight,
          value: 225.0,
          achievedDate: DateTime.now(),
        ),
        PersonalRecord(
          exerciseId: '2',
          exerciseName: 'Push-ups',
          type: PersonalRecordType.maxReps,
          value: 50.0,
          achievedDate: DateTime.now(),
        ),
        PersonalRecord(
          exerciseId: '3',
          exerciseName: 'Squat',
          type: PersonalRecordType.maxVolume,
          value: 5000.0,
          achievedDate: DateTime.now(),
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: PersonalRecordDisplay(personalRecords: records),
          ),
        ),
      );

      expect(find.byIcon(Icons.fitness_center), findsOneWidget);
      expect(find.byIcon(Icons.repeat), findsOneWidget);
      expect(find.byIcon(Icons.trending_up), findsOneWidget);
    });

    testWidgets('displays animation when enabled', (WidgetTester tester) async {
      final record = PersonalRecord(
        exerciseId: '1',
        exerciseName: 'Bench Press',
        type: PersonalRecordType.maxWeight,
        value: 225.0,
        achievedDate: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: PersonalRecordDisplay(
              personalRecords: [record],
              showAnimation: true,
            ),
          ),
        ),
      );

      expect(find.byType(TweenAnimationBuilder<double>), findsOneWidget);
      expect(find.byIcon(Icons.celebration), findsOneWidget);
    });

    testWidgets('hides animation when disabled', (WidgetTester tester) async {
      final record = PersonalRecord(
        exerciseId: '1',
        exerciseName: 'Bench Press',
        type: PersonalRecordType.maxWeight,
        value: 225.0,
        achievedDate: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: PersonalRecordDisplay(
              personalRecords: [record],
              showAnimation: false,
            ),
          ),
        ),
      );

      expect(find.byType(TweenAnimationBuilder<double>), findsNothing);
      expect(find.byIcon(Icons.celebration), findsNothing);
    });
  });
}
