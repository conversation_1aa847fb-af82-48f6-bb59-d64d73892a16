import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/models/exercise.dart';
import 'package:openfitv2/widgets/exercise_instruction_card.dart';

void main() {
  group('ExerciseInstructionCard Widget Tests', () {
    late Exercise testExercise;

    setUp(() {
      testExercise = Exercise(
        id: '1',
        name: 'Push-ups',
        description: 'A basic upper body exercise',
        instructions:
            '1. Start in plank position\n2. Lower your body\n3. Push back up\n4. Repeat',
        primaryMuscle: 'Chest',
        category: 'Strength',
        equipment: 'None',
      );
    });

    testWidgets('displays exercise information correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseInstructionCard(exercise: testExercise),
          ),
        ),
      );

      // Should show header
      expect(find.text('Instructions'), findsOneWidget);
      expect(find.text('Chest'), findsOneWidget);
      expect(find.text('Strength'), findsOneWidget);
    });

    testWidgets('displays exercise description when available',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseInstructionCard(exercise: testExercise),
          ),
        ),
      );

      expect(find.text('A basic upper body exercise'), findsOneWidget);
      expect(find.byIcon(Icons.info_outline), findsOneWidget);
    });

    testWidgets('displays instruction steps correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseInstructionCard(exercise: testExercise),
          ),
        ),
      );

      expect(find.text('Step-by-Step Guide'), findsOneWidget);
      expect(find.text('Start in plank position'), findsOneWidget);
      expect(find.text('Lower your body'), findsOneWidget);
    });

    testWidgets('shows limited steps when not expanded',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseInstructionCard(exercise: testExercise),
          ),
        ),
      );

      // Should show first 2 steps
      expect(find.text('Start in plank position'), findsOneWidget);
      expect(find.text('Lower your body'), findsOneWidget);

      // Should show "Show more" button
      expect(find.textContaining('Show'), findsOneWidget);
      expect(find.textContaining('more steps'), findsOneWidget);
    });

    testWidgets('expands to show all steps when expand button is tapped',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseInstructionCard(exercise: testExercise),
          ),
        ),
      );

      // Tap expand button in header
      await tester.tap(find.byType(IconButton));
      await tester.pump();

      // Should show all steps
      expect(find.text('Start in plank position'), findsOneWidget);
      expect(find.text('Lower your body'), findsOneWidget);
      expect(find.text('Push back up'), findsOneWidget);
      expect(find.text('Repeat'), findsOneWidget);
    });

    testWidgets('shows form tips when expanded and showFormTips is true',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseInstructionCard(
              exercise: testExercise,
              showFormTips: true,
            ),
          ),
        ),
      );

      // Expand the card
      await tester.tap(find.byType(IconButton));
      await tester.pump();

      // Should show form tips
      expect(find.text('Form Tips'), findsOneWidget);
      expect(find.textContaining('shoulder blades'), findsOneWidget);
    });

    testWidgets('does not show form tips when showFormTips is false',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseInstructionCard(
              exercise: testExercise,
              showFormTips: false,
            ),
          ),
        ),
      );

      // Expand the card
      await tester.tap(find.byType(IconButton));
      await tester.pump();

      // Should not show form tips
      expect(find.text('Form Tips'), findsNothing);
    });

    testWidgets('handles exercise without instructions gracefully',
        (WidgetTester tester) async {
      final exerciseWithoutInstructions = Exercise(
        id: '2',
        name: 'Test Exercise',
        primaryMuscle: 'Arms',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body:
                ExerciseInstructionCard(exercise: exerciseWithoutInstructions),
          ),
        ),
      );

      expect(find.text('Instructions'), findsOneWidget);
      expect(find.text('Arms'), findsOneWidget);
      // Should not crash and should not show step-by-step guide
      expect(find.text('Step-by-Step Guide'), findsNothing);
    });

    testWidgets('displays muscle group and category chips correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseInstructionCard(exercise: testExercise),
          ),
        ),
      );

      // Should show muscle group chip
      expect(find.text('Chest'), findsOneWidget);
      expect(find.byIcon(Icons.fitness_center), findsOneWidget);

      // Should show category chip
      expect(find.text('Strength'), findsOneWidget);
      expect(find.byIcon(Icons.category_outlined), findsOneWidget);
    });

    testWidgets('toggles expansion state correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseInstructionCard(exercise: testExercise),
          ),
        ),
      );

      // Initially should show expand_more icon
      expect(find.byIcon(Icons.expand_more), findsAtLeastNWidgets(1));

      // Tap to expand
      await tester.tap(find.byType(IconButton));
      await tester.pump();

      // Should now show expand_less icon
      expect(find.byIcon(Icons.expand_less), findsOneWidget);

      // Tap to collapse
      await tester.tap(find.byType(IconButton));
      await tester.pump();

      // Should show expand_more icon again
      expect(find.byIcon(Icons.expand_more), findsAtLeastNWidgets(1));
    });
  });
}
