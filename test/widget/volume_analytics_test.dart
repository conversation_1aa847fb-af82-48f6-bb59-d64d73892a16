import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/volume_analytics.dart';
import 'package:openfitv2/models/session_analytics.dart';

void main() {
  group('VolumeAnalytics Widget Tests', () {
    late List<SessionAnalytics> mockSessionData;

    setUp(() {
      mockSessionData = [
        SessionAnalytics(
          sessionId: 'session1',
          totalDuration: const Duration(minutes: 45),
          setsCompleted: 12,
          totalReps: 120,
          totalVolume: 2400.0,
          estimatedCalories: 300,
          exercisePerformance: {},
          improvementScore: 85.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 30)),
        ),
        SessionAnalytics(
          sessionId: 'session2',
          totalDuration: const Duration(minutes: 50),
          setsCompleted: 15,
          totalReps: 150,
          totalVolume: 3000.0,
          estimatedCalories: 350,
          exercisePerformance: {},
          improvementScore: 90.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 23)),
        ),
        SessionAnalytics(
          sessionId: 'session3',
          totalDuration: const Duration(minutes: 55),
          setsCompleted: 16,
          totalReps: 160,
          totalVolume: 3200.0,
          estimatedCalories: 380,
          exercisePerformance: {},
          improvementScore: 95.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 16)),
        ),
        SessionAnalytics(
          sessionId: 'session4',
          totalDuration: const Duration(minutes: 48),
          setsCompleted: 14,
          totalReps: 140,
          totalVolume: 2800.0,
          estimatedCalories: 320,
          exercisePerformance: {},
          improvementScore: 88.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 9)),
        ),
        SessionAnalytics(
          sessionId: 'session5',
          totalDuration: const Duration(minutes: 52),
          setsCompleted: 18,
          totalReps: 180,
          totalVolume: 3600.0,
          estimatedCalories: 400,
          exercisePerformance: {},
          improvementScore: 92.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 2)),
        ),
      ];
    });

    testWidgets('displays volume analytics with session data',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
            ),
          ),
        ),
      );

      // Verify the widget displays the correct headers
      expect(find.text('Volume Analytics'), findsOneWidget);
      expect(find.text('Training volume trends and patterns'), findsOneWidget);

      // Verify analytics type selector is present
      expect(find.text('Total Volume'), findsOneWidget);
      expect(find.text('Avg Volume'), findsOneWidget);
      expect(find.text('Total Sets'), findsOneWidget);
      expect(find.text('Total Reps'), findsOneWidget);

      // Verify timeframe selector is present
      expect(find.text('Daily'), findsOneWidget);
      expect(find.text('Weekly'), findsOneWidget);
      expect(find.text('Monthly'), findsOneWidget);

      // Verify time range chip is displayed
      expect(find.text('3M'), findsOneWidget); // 90 days = ~3 months
    });

    testWidgets('displays empty state when no data available',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: [],
            ),
          ),
        ),
      );

      // Verify empty state is displayed
      expect(find.text('No volume data available'), findsOneWidget);
      expect(find.text('Complete more workouts to see volume trends'),
          findsOneWidget);
      expect(find.byIcon(Icons.bar_chart_outlined), findsOneWidget);
    });

    testWidgets('switches analytics types correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
            ),
          ),
        ),
      );

      // Initially Total Volume should be selected
      final totalVolumeChip = find.widgetWithText(FilterChip, 'Total Volume');
      expect(totalVolumeChip, findsOneWidget);

      // Tap on Total Sets chip
      await tester.tap(find.widgetWithText(FilterChip, 'Total Sets'));
      await tester.pumpAndSettle();

      // Verify the analytics type changed
      expect(find.widgetWithText(FilterChip, 'Total Sets'), findsOneWidget);
    });

    testWidgets('switches timeframes correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
            ),
          ),
        ),
      );

      // Initially Weekly should be selected
      final weeklyChip = find.widgetWithText(FilterChip, 'Weekly');
      expect(weeklyChip, findsOneWidget);

      // Tap on Daily chip
      await tester.tap(find.widgetWithText(FilterChip, 'Daily'));
      await tester.pumpAndSettle();

      // Verify the timeframe changed
      expect(find.widgetWithText(FilterChip, 'Daily'), findsOneWidget);
    });

    testWidgets('displays volume statistics correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
            ),
          ),
        ),
      );

      // Verify volume stats section is present
      expect(find.text('Total Volume'),
          findsAtLeastNWidgets(2)); // One in selector, one in stats
      expect(find.text('Average'), findsOneWidget);
      expect(find.text('Peak'), findsOneWidget);

      // Verify icons are present
      expect(find.byIcon(Icons.fitness_center_outlined), findsOneWidget);
      expect(find.byIcon(Icons.analytics_outlined), findsOneWidget);
      expect(find.byIcon(Icons.star_outline), findsOneWidget);
    });

    testWidgets('displays trend analysis correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
            ),
          ),
        ),
      );

      // Verify trend analysis is displayed
      expect(find.textContaining('trend'), findsOneWidget);

      // Should show either trending up, down, or flat icon
      final trendIcons = [
        Icons.trending_up,
        Icons.trending_down,
        Icons.trending_flat,
      ];

      bool foundTrendIcon = false;
      for (final icon in trendIcons) {
        if (tester.widgetList(find.byIcon(icon)).isNotEmpty) {
          foundTrendIcon = true;
          break;
        }
      }
      expect(foundTrendIcon, isTrue);
    });

    testWidgets('filters data by time range correctly',
        (WidgetTester tester) async {
      // Create data with some sessions outside the time range
      final extendedSessionData = [
        ...mockSessionData,
        SessionAnalytics(
          sessionId: 'old_session',
          totalDuration: const Duration(minutes: 40),
          setsCompleted: 10,
          totalReps: 100,
          totalVolume: 2000.0,
          estimatedCalories: 250,
          exercisePerformance: {},
          improvementScore: 80.0,
          sessionDate: DateTime.now()
              .subtract(const Duration(days: 200)), // Outside 90-day range
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: extendedSessionData,
              timeRange: const Duration(days: 90),
            ),
          ),
        ),
      );

      // Should still display the chart (old session should be filtered out)
      expect(find.text('Volume Analytics'), findsOneWidget);
      expect(find.text('Training volume trends and patterns'), findsOneWidget);
    });

    testWidgets('displays time range chip with correct format',
        (WidgetTester tester) async {
      // Test different time ranges
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
              timeRange: const Duration(days: 30),
            ),
          ),
        ),
      );

      expect(find.text('30D'), findsOneWidget);

      // Test yearly range
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
              timeRange: const Duration(days: 365),
            ),
          ),
        ),
      );

      expect(find.text('1Y'), findsOneWidget);
    });

    testWidgets('handles different analytics types correctly',
        (WidgetTester tester) async {
      // Test with Total Sets type
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
              analyticsType: VolumeAnalyticsType.sets,
            ),
          ),
        ),
      );

      // Verify Total Sets is initially selected
      final setsChip = find.widgetWithText(FilterChip, 'Total Sets');
      expect(setsChip, findsOneWidget);

      final FilterChip chip = tester.widget(setsChip);
      expect(chip.selected, isTrue);
    });

    testWidgets('formats volume values correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
            ),
          ),
        ),
      );

      // Should display volume values with proper formatting
      expect(find.textContaining('lbs'), findsAtLeastNWidgets(1));
    });

    testWidgets('handles weekly data aggregation correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
            ),
          ),
        ),
      );

      // Weekly should be selected by default
      final weeklyChip = find.widgetWithText(FilterChip, 'Weekly');
      expect(weeklyChip, findsOneWidget);

      final FilterChip chip = tester.widget(weeklyChip);
      expect(chip.selected, isTrue);
    });

    testWidgets('handles monthly data aggregation correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
            ),
          ),
        ),
      );

      // Switch to monthly view
      await tester.tap(find.widgetWithText(FilterChip, 'Monthly'));
      await tester.pumpAndSettle();

      // Verify monthly is selected
      final monthlyChip = find.widgetWithText(FilterChip, 'Monthly');
      expect(monthlyChip, findsOneWidget);

      final FilterChip chip = tester.widget(monthlyChip);
      expect(chip.selected, isTrue);
    });

    testWidgets('calculates statistics correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: mockSessionData,
            ),
          ),
        ),
      );

      // Verify that statistics are calculated and displayed
      expect(find.text('Total Volume'), findsAtLeastNWidgets(2));
      expect(find.text('Average'), findsOneWidget);
      expect(find.text('Peak'), findsOneWidget);
    });

    testWidgets('handles single data point correctly',
        (WidgetTester tester) async {
      final singleSessionData = [mockSessionData.first];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VolumeAnalytics(
              sessionData: singleSessionData,
            ),
          ),
        ),
      );

      // Should still display the chart with single data point
      expect(find.text('Volume Analytics'), findsOneWidget);
      expect(find.text('Training volume trends and patterns'), findsOneWidget);
    });
  });
}
