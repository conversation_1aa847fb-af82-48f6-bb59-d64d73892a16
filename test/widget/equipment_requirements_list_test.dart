import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/equipment_requirements_list.dart';

void main() {
  group('EquipmentRequirementsList Widget Tests', () {
    testWidgets('displays no equipment needed message when list is empty',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: [],
            ),
          ),
        ),
      );

      expect(find.text('No Equipment Needed'), findsOneWidget);
      expect(find.text('This exercise uses bodyweight only'), findsOneWidget);
      expect(find.byIcon(Icons.self_improvement), findsOneWidget);
    });

    testWidgets('displays equipment list correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: ['Barbell', 'Bench'],
            ),
          ),
        ),
      );

      expect(find.text('Equipment Needed'), findsOneWidget);
      expect(find.text('2 items'), findsOneWidget);
      expect(find.text('Barbell'), findsOneWidget);
      expect(find.text('Bench'), findsOneWidget);
    });

    testWidgets('displays correct equipment icons',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: ['Barbell', 'Dumbbells', 'Bench'],
            ),
          ),
        ),
      );

      // Should show fitness center icons for barbell and dumbbells
      expect(find.byIcon(Icons.fitness_center), findsAtLeastNWidgets(2));
      // Should show weekend icon for bench
      expect(find.byIcon(Icons.weekend), findsOneWidget);
    });

    testWidgets('shows availability chips for equipment',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: ['Barbell'],
            ),
          ),
        ),
      );

      expect(find.text('Available'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('displays equipment descriptions', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: ['Barbell'],
            ),
          ),
        ),
      );

      expect(find.text('Standard Olympic barbell with weight plates'),
          findsOneWidget);
    });

    testWidgets('shows alternatives when showAlternatives is true',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: ['Barbell'],
              showAlternatives: true,
            ),
          ),
        ),
      );

      expect(find.text('Alternatives:'), findsOneWidget);
      expect(find.byIcon(Icons.swap_horiz), findsOneWidget);
      expect(find.text('Dumbbells'), findsOneWidget);
      expect(find.text('Resistance Bands'), findsOneWidget);
    });

    testWidgets('hides alternatives when showAlternatives is false',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: ['Barbell'],
              showAlternatives: false,
            ),
          ),
        ),
      );

      expect(find.text('Alternatives:'), findsNothing);
      expect(find.text('Dumbbells'), findsNothing);
    });

    testWidgets('hides alternatives in compact mode',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: ['Barbell'],
              showAlternatives: true,
              isCompact: true,
            ),
          ),
        ),
      );

      expect(find.text('Alternatives:'), findsNothing);
      expect(find.text('Dumbbells'), findsNothing);
    });

    testWidgets('displays custom alternatives when provided',
        (WidgetTester tester) async {
      final customAlternatives = {
        'Barbell': ['Custom Alternative 1', 'Custom Alternative 2'],
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: ['Barbell'],
              alternatives: customAlternatives,
              showAlternatives: true,
            ),
          ),
        ),
      );

      expect(find.text('Custom Alternative 1'), findsOneWidget);
      expect(find.text('Custom Alternative 2'), findsOneWidget);
      // Should not show default alternatives
      expect(find.text('Dumbbells'), findsNothing);
    });

    testWidgets('handles single item correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: ['Barbell'],
            ),
          ),
        ),
      );

      expect(find.text('1 item'), findsOneWidget);
    });

    testWidgets('handles multiple items correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: ['Barbell', 'Bench', 'Dumbbells'],
            ),
          ),
        ),
      );

      expect(find.text('3 items'), findsOneWidget);
    });

    testWidgets(
        'displays different equipment types with correct icons and colors',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EquipmentRequirementsList(
              requiredEquipment: [
                'Kettlebell',
                'Resistance Bands',
                'Medicine Ball'
              ],
            ),
          ),
        ),
      );

      expect(find.textContaining('Kettlebell'), findsAtLeastNWidgets(1));
      expect(find.textContaining('Resistance Bands'), findsAtLeastNWidgets(1));
      expect(find.textContaining('Medicine Ball'), findsAtLeastNWidgets(1));

      // Should show different icons
      expect(
          find.byIcon(Icons.sports_gymnastics), findsOneWidget); // Kettlebell
      expect(
          find.byIcon(Icons.linear_scale), findsOneWidget); // Resistance Bands
      expect(find.byIcon(Icons.sports_basketball),
          findsOneWidget); // Medicine Ball
    });
  });
}
