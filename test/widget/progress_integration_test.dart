import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/widgets/progress_charts.dart';
import '../../lib/widgets/strength_progression_view.dart';
import '../../lib/models/session_analytics.dart';
import '../../lib/design_system/design_system.dart';

void main() {
  group('Progress Integration Tests', () {
    Widget createTestWidget(Widget child) {
      return MaterialApp(
        theme: ThemeData.light(),
        home: Scaffold(
          body: child,
        ),
      );
    }

    testWidgets('ProgressCharts displays correctly with data', (tester) async {
      // Arrange
      final sessionData = [
        SessionAnalytics(
          sessionId: 'session1',
          totalDuration: const Duration(minutes: 45),
          setsCompleted: 12,
          totalReps: 36,
          totalVolume: 1800,
          estimatedCalories: 250,
          exercisePerformance: {
            'bench_press': ExercisePerformance(
              exerciseId: 'bench_press',
              exerciseName: 'Bench Press',
              setsCompleted: 4,
              totalReps: 12,
              totalVolume: 1800,
              maxWeight: 150,
              maxReps: 12,
              timeSpent: const Duration(minutes: 15),
            ),
          },
          improvementScore: 75.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 7)),
        ),
      ];

      // Act
      await tester.pumpWidget(
        createTestWidget(
          ProgressCharts(
            sessionData: sessionData,
            exerciseId: 'bench_press',
            exerciseName: 'Bench Press',
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Bench Press'), findsOneWidget);
      expect(find.text('Progress Over Time'), findsOneWidget);
    });

    testWidgets('ProgressCharts displays empty state when no data',
        (tester) async {
      // Act
      await tester.pumpWidget(
        createTestWidget(
          const ProgressCharts(
            sessionData: [],
            exerciseId: 'bench_press',
            exerciseName: 'Bench Press',
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('No data available'), findsOneWidget);
      expect(
          find.text('Complete more workouts to see progress'), findsOneWidget);
    });

    testWidgets('StrengthProgressionView displays correctly with data',
        (tester) async {
      // Arrange
      final sessionData = [
        SessionAnalytics(
          sessionId: 'session1',
          totalDuration: const Duration(minutes: 45),
          setsCompleted: 12,
          totalReps: 36,
          totalVolume: 1800,
          estimatedCalories: 250,
          exercisePerformance: {
            'bench_press': ExercisePerformance(
              exerciseId: 'bench_press',
              exerciseName: 'Bench Press',
              setsCompleted: 4,
              totalReps: 12,
              totalVolume: 1800,
              maxWeight: 150,
              maxReps: 12,
              timeSpent: const Duration(minutes: 15),
            ),
          },
          improvementScore: 75.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 7)),
        ),
      ];

      // Act
      await tester.pumpWidget(
        createTestWidget(
          StrengthProgressionView(
            sessionData: sessionData,
            exerciseId: 'bench_press',
            exerciseName: 'Bench Press',
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Strength Progression'), findsOneWidget);
      expect(find.text('Bench Press'), findsOneWidget);
    });

    testWidgets('StrengthProgressionView displays empty state when no data',
        (tester) async {
      // Act
      await tester.pumpWidget(
        createTestWidget(
          const StrengthProgressionView(
            sessionData: [],
            exerciseId: 'bench_press',
            exerciseName: 'Bench Press',
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('No progression data'), findsOneWidget);
      expect(find.text('Complete more workouts to track strength gains'),
          findsOneWidget);
    });

    testWidgets('Progress charts handle chart type switching', (tester) async {
      // Arrange
      final sessionData = [
        SessionAnalytics(
          sessionId: 'session1',
          totalDuration: const Duration(minutes: 45),
          setsCompleted: 12,
          totalReps: 36,
          totalVolume: 1800,
          estimatedCalories: 250,
          exercisePerformance: {
            'bench_press': ExercisePerformance(
              exerciseId: 'bench_press',
              exerciseName: 'Bench Press',
              setsCompleted: 4,
              totalReps: 12,
              totalVolume: 1800,
              maxWeight: 150,
              maxReps: 12,
              timeSpent: const Duration(minutes: 15),
            ),
          },
          improvementScore: 75.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 7)),
        ),
      ];

      // Act
      await tester.pumpWidget(
        createTestWidget(
          ProgressCharts(
            sessionData: sessionData,
            exerciseId: 'bench_press',
            exerciseName: 'Bench Press',
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert - should find chart type selector chips
      expect(find.byType(FilterChip), findsWidgets);

      // Act - tap on Volume chart type chip
      final volumeChip = find.widgetWithText(FilterChip, 'Volume');
      await tester.tap(volumeChip);
      await tester.pumpAndSettle();

      // Assert - Volume chart should be available
      expect(find.widgetWithText(FilterChip, 'Volume'), findsOneWidget);
    });
  });
}
