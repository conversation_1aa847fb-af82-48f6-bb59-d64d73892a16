import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import '../../lib/widgets/exercise_modification_panel.dart';
import '../../lib/models/exercise.dart';
import '../../lib/models/offline_workout_session.dart';
import '../../lib/services/progress_service.dart';

// Mock classes
class MockProgressService extends ProgressService {
  @override
  Future<List<Map<String, dynamic>>> getExerciseProgress(String exerciseId,
      {int limit = 10}) async {
    return [];
  }
}

void main() {
  group('ExerciseModificationPanel Widget Tests', () {
    late MockProgressService mockProgressService;
    late Exercise testExercise;
    late List<OfflineSetData> testPreviousSets;

    setUp(() {
      mockProgressService = MockProgressService();

      testExercise = Exercise(
        id: 'test_exercise_1',
        name: 'Bench Press',
        primaryMuscle: 'Chest',
        equipment: 'Barbell',
        description: 'Test exercise',
      );

      testPreviousSets = [
        OfflineSetData(
          setId: 'set_1',
          exerciseId: 'test_exercise_1',
          exerciseName: 'Bench Press',
          setNumber: 1,
          weight: 135.0,
          reps: 8,
          completedAt: DateTime.now().subtract(const Duration(minutes: 5)),
          difficultyRating: 3,
        ),
        OfflineSetData(
          setId: 'set_2',
          exerciseId: 'test_exercise_1',
          exerciseName: 'Bench Press',
          setNumber: 2,
          weight: 135.0,
          reps: 7,
          completedAt: DateTime.now().subtract(const Duration(minutes: 2)),
          difficultyRating: 4,
        ),
      ];
    });

    Widget createTestWidget({
      Function(ExerciseModification)? onModificationApplied,
      VoidCallback? onClose,
    }) {
      return MaterialApp(
        theme: ThemeData.light(),
        home: Scaffold(
          body: MultiProvider(
            providers: [
              Provider<ProgressService>.value(value: mockProgressService),
            ],
            child: ExerciseModificationPanel(
              currentExercise: testExercise,
              currentSetNumber: 3,
              sessionId: 'test_session',
              previousSetsInSession: testPreviousSets,
              onModificationApplied: onModificationApplied ?? (modification) {},
              onClose: onClose,
            ),
          ),
        ),
      );
    }

    testWidgets('displays exercise modification panel with correct header',
        (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Check header content
      expect(find.text('Modify Exercise'), findsOneWidget);
      expect(find.text('Bench Press'), findsOneWidget);
      expect(find.text('Set 3'), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('displays three tabs correctly', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Check tab bar
      expect(find.text('Adjust Weight'), findsOneWidget);
      expect(find.text('Alternatives'), findsOneWidget);
      expect(find.text('Skip Exercise'), findsOneWidget);

      // Check tab icons
      expect(find.byIcon(Icons.tune), findsOneWidget);
      expect(find.byIcon(Icons.swap_horiz), findsOneWidget);
      expect(find.byIcon(Icons.skip_next), findsOneWidget);
    });

    testWidgets('weight adjustment tab shows manual adjustment fields',
        (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should be on weight adjustment tab by default
      expect(find.text('Manual Adjustment'), findsOneWidget);
      expect(find.text('Weight (lbs)'), findsOneWidget);
      expect(find.text('Target Reps'), findsOneWidget);
      expect(find.text('Quick Adjustments'), findsOneWidget);

      // Check quick adjustment buttons
      expect(find.text('-10%'), findsOneWidget);
      expect(find.text('-5%'), findsOneWidget);
      expect(find.text('+5%'), findsOneWidget);
      expect(find.text('+10%'), findsOneWidget);

      expect(find.text('Apply Weight Adjustment'), findsOneWidget);
    });

    testWidgets('weight adjustment tab shows AI suggestion when available',
        (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Wait for async operations
      await tester.pump(const Duration(seconds: 1));

      // The AI suggestion may or may not appear depending on data
      // Just verify the basic structure is there
      expect(find.text('Manual Adjustment'), findsOneWidget);
    });

    testWidgets('quick adjustment buttons modify weight correctly',
        (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter initial weight
      await tester.enterText(find.byType(TextFormField).first, '100');
      await tester.pump();

      // Tap +10% button
      await tester.tap(find.text('+10%'));
      await tester.pump();

      // Check that weight was adjusted
      expect(find.text('110.0'), findsOneWidget);
    });

    testWidgets('applies weight adjustment when button pressed',
        (tester) async {
      ExerciseModification? appliedModification;
      bool closeCalled = false;

      await tester.pumpWidget(createTestWidget(
        onModificationApplied: (modification) {
          appliedModification = modification;
        },
        onClose: () {
          closeCalled = true;
        },
      ));
      await tester.pumpAndSettle();

      // Enter weight and reps
      await tester.enterText(find.byType(TextFormField).first, '140');
      await tester.enterText(find.byType(TextFormField).last, '6');
      await tester.pump();

      // Tap apply button
      await tester.tap(find.text('Apply Weight Adjustment'));
      await tester.pump();

      // Check that modification was applied
      expect(appliedModification, isNotNull);
      expect(
          appliedModification!.type, ExerciseModificationType.weightAdjustment);
      expect(appliedModification!.newWeight, 140.0);
      expect(appliedModification!.newReps, 6);
      expect(closeCalled, isTrue);
    });

    testWidgets('alternative exercises tab shows loading initially',
        (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Switch to alternatives tab
      await tester.tap(find.text('Alternatives'));
      await tester.pumpAndSettle();

      expect(find.text('Alternative Exercises'), findsOneWidget);
      expect(
          find.text(
              'Choose an alternative exercise that targets the same muscle group'),
          findsOneWidget);
    });

    testWidgets('skip exercise tab shows skip options', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Switch to skip tab
      await tester.tap(find.text('Skip Exercise'));
      await tester.pumpAndSettle();

      expect(find.text('Skip Exercise'), findsWidgets); // Tab and header
      expect(find.text('Reason for skipping (optional)'), findsOneWidget);
      expect(find.text('Equipment not available'), findsOneWidget);
      expect(find.text('Injury or discomfort'), findsOneWidget);
      expect(find.text('Too difficult'), findsOneWidget);
      expect(find.text('Skip This Exercise'), findsOneWidget);
    });

    testWidgets('skip exercise applies modification correctly', (tester) async {
      ExerciseModification? appliedModification;
      bool closeCalled = false;

      await tester.pumpWidget(createTestWidget(
        onModificationApplied: (modification) {
          appliedModification = modification;
        },
        onClose: () {
          closeCalled = true;
        },
      ));
      await tester.pumpAndSettle();

      // Switch to skip tab
      await tester.tap(find.text('Skip Exercise'));
      await tester.pumpAndSettle();

      // Select a reason
      await tester.tap(find.text('Equipment not available'));
      await tester.pump();

      // Tap skip button
      await tester.tap(find.text('Skip This Exercise'));
      await tester.pump();

      // Check that modification was applied
      expect(appliedModification, isNotNull);
      expect(appliedModification!.type, ExerciseModificationType.exerciseSkip);
      expect(appliedModification!.reason, 'Equipment not available');
      expect(closeCalled, isTrue);
    });

    testWidgets('close button calls onClose callback', (tester) async {
      bool closeCalled = false;

      await tester.pumpWidget(createTestWidget(
        onClose: () {
          closeCalled = true;
        },
      ));
      await tester.pumpAndSettle();

      // Tap close button
      await tester.tap(find.byIcon(Icons.close));
      await tester.pump();

      expect(closeCalled, isTrue);
    });

    testWidgets('validates weight input correctly', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter invalid weight (negative)
      await tester.enterText(find.byType(TextFormField).first, '-10');
      await tester.enterText(find.byType(TextFormField).last, '8');
      await tester.pump();

      // Tap apply button
      await tester.tap(find.text('Apply Weight Adjustment'));
      await tester.pump();

      // Should show error message
      expect(find.text('Please enter a valid weight'), findsOneWidget);
    });

    testWidgets('validates reps input correctly', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter valid weight but invalid reps
      await tester.enterText(find.byType(TextFormField).first, '100');
      await tester.enterText(find.byType(TextFormField).last, '0');
      await tester.pump();

      // Tap apply button
      await tester.tap(find.text('Apply Weight Adjustment'));
      await tester.pump();

      // Should show error message
      expect(find.text('Please enter valid reps'), findsOneWidget);
    });

    testWidgets('shows warning message in skip tab', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Switch to skip tab
      await tester.tap(find.text('Skip Exercise'));
      await tester.pumpAndSettle();

      // Check warning message
      expect(find.byIcon(Icons.warning_amber), findsOneWidget);
      expect(
          find.text(
              'Skipping exercises regularly may affect your progress. Consider finding alternatives or adjusting the weight instead.'),
          findsOneWidget);
    });
  });

  group('ExerciseModification Data Class Tests', () {
    test('creates weight adjustment modification correctly', () {
      final modification = ExerciseModification.weightAdjustment(
        exerciseId: 'test_exercise',
        newWeight: 150.0,
        newReps: 8,
        reason: 'Test adjustment',
      );

      expect(modification.type, ExerciseModificationType.weightAdjustment);
      expect(modification.exerciseId, 'test_exercise');
      expect(modification.reason, 'Test adjustment');
      expect(modification.newWeight, 150.0);
      expect(modification.newReps, 8);
      expect(modification.newExercise, isNull);
    });

    test('creates exercise substitution modification correctly', () {
      final newExercise = Exercise(
        id: 'new_exercise',
        name: 'Push-ups',
        primaryMuscle: 'Chest',
      );

      final modification = ExerciseModification.exerciseSubstitution(
        originalExerciseId: 'old_exercise',
        newExercise: newExercise,
        reason: 'Equipment not available',
      );

      expect(modification.type, ExerciseModificationType.exerciseSubstitution);
      expect(modification.exerciseId, 'old_exercise');
      expect(modification.reason, 'Equipment not available');
      expect(modification.newExercise, newExercise);
      expect(modification.newWeight, isNull);
      expect(modification.newReps, isNull);
    });

    test('creates exercise skip modification correctly', () {
      final modification = ExerciseModification.exerciseSkip(
        exerciseId: 'test_exercise',
        reason: 'Injury',
      );

      expect(modification.type, ExerciseModificationType.exerciseSkip);
      expect(modification.exerciseId, 'test_exercise');
      expect(modification.reason, 'Injury');
      expect(modification.newWeight, isNull);
      expect(modification.newReps, isNull);
      expect(modification.newExercise, isNull);
    });
  });

  group('WeightAdjustmentSuggestion Tests', () {
    test('creates weight adjustment suggestion correctly', () {
      final suggestion = WeightAdjustmentSuggestion(
        suggestedWeight: 140.0,
        adjustmentType: WeightAdjustmentType.increase,
        reasoning: 'Previous sets were too easy',
        confidence: 0.8,
      );

      expect(suggestion.suggestedWeight, 140.0);
      expect(suggestion.adjustmentType, WeightAdjustmentType.increase);
      expect(suggestion.reasoning, 'Previous sets were too easy');
      expect(suggestion.confidence, 0.8);
    });
  });
}
