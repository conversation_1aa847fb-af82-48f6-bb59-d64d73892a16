import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import '../../lib/widgets/set_tracking_widget.dart';
import '../../lib/models/exercise.dart';
import '../../lib/models/offline_workout_session.dart';
import '../../lib/services/offline_service.dart';
import '../../lib/services/progress_service.dart';
import '../../lib/design_system/design_system.dart';

// Simple mock classes for testing
class MockOfflineService extends OfflineService {
  final List<Map<String, dynamic>> _addedSets = [];

  @override
  Future<String> addSet({
    required String sessionId,
    required String exerciseId,
    required String exerciseName,
    required int setNumber,
    required double weight,
    required int reps,
    Duration restTime = Duration.zero,
    int? difficultyRating,
    String? notes,
    Map<String, dynamic>? metadata,
  }) async {
    _addedSets.add({
      'sessionId': sessionId,
      'exerciseId': exerciseId,
      'exerciseName': exerciseName,
      'setNumber': setNumber,
      'weight': weight,
      'reps': reps,
      'restTime': restTime,
      'difficultyRating': difficultyRating,
      'notes': notes,
      'metadata': metadata,
    });
    return 'mock-set-id';
  }

  List<Map<String, dynamic>> get addedSets => _addedSets;
}

class MockProgressService extends ChangeNotifier {
  final Map<String, List<Map<String, dynamic>>> _exerciseProgress = {};

  void setExerciseProgress(
      String exerciseId, List<Map<String, dynamic>> progress) {
    _exerciseProgress[exerciseId] = progress;
  }

  Future<List<Map<String, dynamic>>> getExerciseProgress(
    String exerciseId, {
    int limit = 10,
  }) async {
    return _exerciseProgress[exerciseId] ?? [];
  }
}

void main() {
  group('SetTrackingWidget', () {
    late MockOfflineService mockOfflineService;
    late MockProgressService mockProgressService;
    late Exercise testExercise;
    late List<OfflineSetData> completedSets;

    setUp(() {
      mockOfflineService = MockOfflineService();
      mockProgressService = MockProgressService();

      testExercise = Exercise(
        id: 'exercise-1',
        name: 'Bench Press',
        primaryMuscle: 'Chest',
        equipment: 'Barbell',
      );

      completedSets = [];
    });

    Widget createTestWidget({
      int setNumber = 1,
      String sessionId = 'test-session',
      OfflineSetData? previousSetData,
      List<OfflineSetData> previousSetsInSession = const [],
      Function(OfflineSetData)? onSetCompleted,
      Function(String, int)? onSetSkipped,
      bool isEnabled = true,
    }) {
      return MaterialApp(
        theme: ThemeData(
          extensions: [
            AppColors.light(),
          ],
        ),
        home: Scaffold(
          body: MultiProvider(
            providers: [
              ChangeNotifierProvider<OfflineService>.value(
                  value: mockOfflineService),
              ChangeNotifierProvider<ProgressService>.value(
                  value: mockProgressService),
            ],
            child: SetTrackingWidget(
              exercise: testExercise,
              setNumber: setNumber,
              sessionId: sessionId,
              previousSetData: previousSetData,
              previousSetsInSession: previousSetsInSession,
              onSetCompleted:
                  onSetCompleted ?? (setData) => completedSets.add(setData),
              onSetSkipped: onSetSkipped,
              isEnabled: isEnabled,
            ),
          ),
        ),
      );
    }

    testWidgets('should display exercise name and set number',
        (WidgetTester tester) async {
      // Arrange
      mockProgressService.setExerciseProgress('exercise-1', []);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Bench Press'), findsOneWidget);
      expect(find.text('Set 1'), findsOneWidget);
    });

    testWidgets('should display weight and reps input fields',
        (WidgetTester tester) async {
      // Arrange
      mockProgressService.setExerciseProgress('exercise-1', []);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(
          find.byType(TextFormField), findsNWidgets(3)); // Weight, reps, notes
      expect(find.text('Weight (lbs)'), findsOneWidget);
      expect(find.text('Reps'), findsOneWidget);
      expect(find.text('Notes (optional)'), findsOneWidget);
    });

    testWidgets('should display difficulty rating buttons',
        (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('How difficult was this set?'), findsOneWidget);
      expect(find.text('1 = Very Easy, 3 = Moderate, 5 = Very Hard'),
          findsOneWidget);

      // Should have 5 difficulty rating buttons
      for (int i = 1; i <= 5; i++) {
        expect(find.text(i.toString()), findsOneWidget);
      }
    });

    testWidgets('should allow selecting difficulty rating',
        (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Tap on difficulty rating 4
      await tester.tap(find.text('4'));
      await tester.pumpAndSettle();

      // Assert - The button should be selected (visual feedback would be tested in integration tests)
      // For unit tests, we verify the tap was registered
      expect(find.text('4'), findsOneWidget);
    });

    testWidgets('should display AI suggestion when available',
        (WidgetTester tester) async {
      // Arrange
      final historicalData = [
        {'weight': 185.0, 'reps': 8, 'completed_at': '2024-01-15T10:00:00Z'},
      ];

      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => historicalData);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('AI Suggested'), findsOneWidget);
      expect(find.byIcon(Icons.lightbulb_outline), findsOneWidget);
      expect(find.byIcon(Icons.psychology), findsOneWidget);
    });

    testWidgets('should pre-fill inputs with AI suggestions',
        (WidgetTester tester) async {
      // Arrange
      final historicalData = [
        {'weight': 185.0, 'reps': 8, 'completed_at': '2024-01-15T10:00:00Z'},
      ];

      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => historicalData);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert - Check that input fields are pre-filled
      final weightField = tester.widget<TextFormField>(
        find.widgetWithText(TextFormField, 'Weight (lbs)').first,
      );
      final repsField = tester.widget<TextFormField>(
        find.widgetWithText(TextFormField, 'Reps').first,
      );

      expect(weightField.controller?.text, isNotEmpty);
      expect(repsField.controller?.text, isNotEmpty);
    });

    testWidgets('should allow manual input override of suggestions',
        (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter custom values
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Weight (lbs)'), '200.5');
      await tester.enterText(find.widgetWithText(TextFormField, 'Reps'), '10');
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Notes (optional)'),
          'Felt strong today');

      // Assert
      expect(find.text('200.5'), findsOneWidget);
      expect(find.text('10'), findsOneWidget);
      expect(find.text('Felt strong today'), findsOneWidget);
    });

    testWidgets('should complete set with valid inputs',
        (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      when(mockOfflineService.addSet(
        sessionId: any(named: 'sessionId'),
        exerciseId: any(named: 'exerciseId'),
        exerciseName: any(named: 'exerciseName'),
        setNumber: any(named: 'setNumber'),
        weight: any(named: 'weight'),
        reps: any(named: 'reps'),
        difficultyRating: any(named: 'difficultyRating'),
        notes: any(named: 'notes'),
      )).thenAnswer((_) async => 'test-set-id');

      bool setCompleted = false;
      OfflineSetData? completedSetData;

      // Act
      await tester.pumpWidget(createTestWidget(
        onSetCompleted: (setData) {
          setCompleted = true;
          completedSetData = setData;
        },
      ));
      await tester.pumpAndSettle();

      // Enter valid inputs
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Weight (lbs)'), '185.0');
      await tester.enterText(find.widgetWithText(TextFormField, 'Reps'), '8');

      // Select difficulty rating
      await tester.tap(find.text('3'));
      await tester.pumpAndSettle();

      // Complete the set
      await tester.tap(find.text('Complete Set'));
      await tester.pumpAndSettle();

      // Assert
      expect(setCompleted, isTrue);
      expect(completedSetData, isNotNull);
      expect(completedSetData!.weight, equals(185.0));
      expect(completedSetData!.reps, equals(8));
      expect(completedSetData!.difficultyRating, equals(3));

      verify(mockOfflineService.addSet(
        sessionId: 'test-session',
        exerciseId: 'exercise-1',
        exerciseName: 'Bench Press',
        setNumber: 1,
        weight: 185.0,
        reps: 8,
        difficultyRating: 3,
        notes: null,
      )).called(1);
    });

    testWidgets('should validate weight input', (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter invalid weight
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Weight (lbs)'), '-10');
      await tester.enterText(find.widgetWithText(TextFormField, 'Reps'), '8');

      // Try to complete set
      await tester.tap(find.text('Complete Set'));
      await tester.pumpAndSettle();

      // Assert - Should show validation error
      expect(find.text('Please enter a valid weight'), findsOneWidget);
    });

    testWidgets('should validate reps input', (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter invalid reps
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Weight (lbs)'), '185');
      await tester.enterText(find.widgetWithText(TextFormField, 'Reps'), '0');

      // Try to complete set
      await tester.tap(find.text('Complete Set'));
      await tester.pumpAndSettle();

      // Assert - Should show validation error
      expect(find.text('Please enter a valid number of reps'), findsOneWidget);
    });

    testWidgets('should allow skipping set when skip callback provided',
        (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      bool setSkipped = false;
      String? skippedExerciseId;
      int? skippedSetNumber;

      // Act
      await tester.pumpWidget(createTestWidget(
        onSetSkipped: (exerciseId, setNumber) {
          setSkipped = true;
          skippedExerciseId = exerciseId;
          skippedSetNumber = setNumber;
        },
      ));
      await tester.pumpAndSettle();

      // Skip the set
      await tester.tap(find.text('Skip Set'));
      await tester.pumpAndSettle();

      // Assert
      expect(setSkipped, isTrue);
      expect(skippedExerciseId, equals('exercise-1'));
      expect(skippedSetNumber, equals(1));
    });

    testWidgets('should not show skip button when callback not provided',
        (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      // Act
      await tester.pumpWidget(createTestWidget(onSetSkipped: null));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Skip Set'), findsNothing);
    });

    testWidgets('should disable inputs when not enabled',
        (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      // Act
      await tester.pumpWidget(createTestWidget(isEnabled: false));
      await tester.pumpAndSettle();

      // Assert - All interactive elements should be disabled
      final weightField = tester.widget<TextFormField>(
        find.widgetWithText(TextFormField, 'Weight (lbs)').first,
      );
      final repsField = tester.widget<TextFormField>(
        find.widgetWithText(TextFormField, 'Reps').first,
      );
      final notesField = tester.widget<TextFormField>(
        find.widgetWithText(TextFormField, 'Notes (optional)').first,
      );
      final completeButton = tester.widget<ElevatedButton>(
        find.widgetWithText(ElevatedButton, 'Complete Set'),
      );

      expect(weightField.enabled, isFalse);
      expect(repsField.enabled, isFalse);
      expect(notesField.enabled, isFalse);
      expect(completeButton.onPressed, isNull);
    });

    testWidgets('should show loading state during set completion',
        (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      // Make the addSet call take some time
      when(mockOfflineService.addSet(
        sessionId: any(named: 'sessionId'),
        exerciseId: any(named: 'exerciseId'),
        exerciseName: any(named: 'exerciseName'),
        setNumber: any(named: 'setNumber'),
        weight: any(named: 'weight'),
        reps: any(named: 'reps'),
        difficultyRating: any(named: 'difficultyRating'),
        notes: any(named: 'notes'),
      )).thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 100));
        return 'test-set-id';
      });

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter valid inputs
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Weight (lbs)'), '185');
      await tester.enterText(find.widgetWithText(TextFormField, 'Reps'), '8');

      // Start completing the set
      await tester.tap(find.text('Complete Set'));
      await tester.pump(); // Don't settle, so we can see loading state

      // Assert - Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Complete Set'), findsNothing);

      // Wait for completion
      await tester.pumpAndSettle();
    });

    testWidgets('should clear form after successful set completion',
        (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      when(mockOfflineService.addSet(
        sessionId: any(named: 'sessionId'),
        exerciseId: any(named: 'exerciseId'),
        exerciseName: any(named: 'exerciseName'),
        setNumber: any(named: 'setNumber'),
        weight: any(named: 'weight'),
        reps: any(named: 'reps'),
        difficultyRating: any(named: 'difficultyRating'),
        notes: any(named: 'notes'),
      )).thenAnswer((_) async => 'test-set-id');

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter inputs
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Weight (lbs)'), '185');
      await tester.enterText(find.widgetWithText(TextFormField, 'Reps'), '8');
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Notes (optional)'), 'Test note');

      // Select difficulty
      await tester.tap(find.text('3'));
      await tester.pumpAndSettle();

      // Complete set
      await tester.tap(find.text('Complete Set'));
      await tester.pumpAndSettle();

      // Assert - Form should be cleared
      final weightField = tester.widget<TextFormField>(
        find.widgetWithText(TextFormField, 'Weight (lbs)').first,
      );
      final repsField = tester.widget<TextFormField>(
        find.widgetWithText(TextFormField, 'Reps').first,
      );
      final notesField = tester.widget<TextFormField>(
        find.widgetWithText(TextFormField, 'Notes (optional)').first,
      );

      expect(weightField.controller?.text, isEmpty);
      expect(repsField.controller?.text, isEmpty);
      expect(notesField.controller?.text, isEmpty);
    });

    testWidgets('should handle errors during set completion',
        (WidgetTester tester) async {
      // Arrange
      when(mockProgressService.getExerciseProgress('exercise-1', limit: 10))
          .thenAnswer((_) async => []);

      when(mockOfflineService.addSet(
        sessionId: any(named: 'sessionId'),
        exerciseId: any(named: 'exerciseId'),
        exerciseName: any(named: 'exerciseName'),
        setNumber: any(named: 'setNumber'),
        weight: any(named: 'weight'),
        reps: any(named: 'reps'),
        difficultyRating: any(named: 'difficultyRating'),
        notes: any(named: 'notes'),
      )).thenThrow(Exception('Database error'));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Enter valid inputs
      await tester.enterText(
          find.widgetWithText(TextFormField, 'Weight (lbs)'), '185');
      await tester.enterText(find.widgetWithText(TextFormField, 'Reps'), '8');

      // Try to complete set
      await tester.tap(find.text('Complete Set'));
      await tester.pumpAndSettle();

      // Assert - Should show error message
      expect(find.text('Error saving set: Exception: Database error'),
          findsOneWidget);
    });
  });
}
