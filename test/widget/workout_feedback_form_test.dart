import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/workout_feedback_form.dart';

void main() {
  group('WorkoutFeedbackForm', () {
    late int submittedRating;
    late String? submittedNotes;
    late bool skipCalled;

    setUp(() {
      submittedRating = 0;
      submittedNotes = null;
      skipCalled = false;
    });

    Widget createTestWidget({
      int? initialRating,
      String? initialNotes,
      bool includeSkip = false,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: WorkoutFeedbackForm(
            initialRating: initialRating,
            initialNotes: initialNotes,
            onFeedbackSubmitted: (rating, notes) {
              submittedRating = rating;
              submittedNotes = notes;
            },
            onSkip: includeSkip ? () => skipCalled = true : null,
          ),
        ),
      );
    }

    testWidgets('displays header with title and description',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('How was your workout?'), findsOneWidget);
      expect(find.text('Your feedback helps us improve your experience'),
          findsOneWidget);
      expect(find.byIcon(Icons.feedback), findsOneWidget);
    });

    testWidgets('displays rating buttons with emojis and labels',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('😞'), findsOneWidget);
      expect(find.text('😐'), findsOneWidget);
      expect(find.text('😊'), findsOneWidget);
      expect(find.text('😁'), findsOneWidget);
      expect(find.text('🔥'), findsOneWidget);

      expect(find.text('Too Hard'), findsOneWidget);
      expect(find.text('Challenging'), findsOneWidget);
      expect(find.text('Just Right'), findsOneWidget);
      expect(find.text('Easy'), findsOneWidget);
      expect(find.text('Too Easy'), findsOneWidget);
    });

    testWidgets('defaults to rating 3 (Just Right)',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find the GestureDetector that contains the "Just Right" emoji
      final justRightGesture = find.ancestor(
        of: find.text('😊'),
        matching: find.byType(GestureDetector),
      );

      expect(justRightGesture, findsOneWidget);

      // Check that the text "Just Right" is displayed with primary color (selected state)
      final justRightText = find.text('Just Right');
      expect(justRightText, findsOneWidget);
    });

    testWidgets('uses initial rating when provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(initialRating: 1));

      // Find the GestureDetector that contains the "Too Hard" emoji
      final tooHardGesture = find.ancestor(
        of: find.text('😞'),
        matching: find.byType(GestureDetector),
      );

      expect(tooHardGesture, findsOneWidget);

      // Check that the text "Too Hard" is displayed
      final tooHardText = find.text('Too Hard');
      expect(tooHardText, findsOneWidget);
    });

    testWidgets('allows rating selection', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap on "Too Hard" rating
      await tester.tap(find.text('😞'));
      await tester.pump();

      // Find the GestureDetector that contains the "Too Hard" emoji
      final tooHardGesture = find.ancestor(
        of: find.text('😞'),
        matching: find.byType(GestureDetector),
      );

      expect(tooHardGesture, findsOneWidget);

      // Check that the text "Too Hard" is displayed
      final tooHardText = find.text('Too Hard');
      expect(tooHardText, findsOneWidget);
    });

    testWidgets('displays notes text field', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Additional Notes (Optional)'), findsOneWidget);
      expect(find.byType(TextField), findsOneWidget);

      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.maxLines, equals(4));
      expect(textField.maxLength, equals(500));
    });

    testWidgets('uses initial notes when provided',
        (WidgetTester tester) async {
      const initialNotes = 'Great workout!';
      await tester.pumpWidget(createTestWidget(initialNotes: initialNotes));

      expect(find.text(initialNotes), findsOneWidget);
    });

    testWidgets('allows text input in notes field',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      const testNotes = 'This was a challenging workout';
      await tester.enterText(find.byType(TextField), testNotes);
      await tester.pump();

      expect(find.text(testNotes), findsOneWidget);
    });

    testWidgets('displays submit button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Submit Feedback'), findsOneWidget);
      expect(find.byIcon(Icons.check), findsOneWidget);
    });

    testWidgets('submits feedback with rating and notes',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Select rating 5
      await tester.tap(find.text('🔥'));
      await tester.pump();

      // Enter notes
      const testNotes = 'Easy workout, need more challenge';
      await tester.enterText(find.byType(TextField), testNotes);
      await tester.pump();

      // Submit feedback
      await tester.tap(find.text('Submit Feedback'));
      await tester.pump();

      // Wait for async operation
      await tester.pump(const Duration(milliseconds: 600));

      expect(submittedRating, equals(5));
      expect(submittedNotes, equals(testNotes));
    });

    testWidgets('submits feedback with rating only when notes are empty',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Select rating 2
      await tester.tap(find.text('😐'));
      await tester.pump();

      // Submit feedback without notes
      await tester.tap(find.text('Submit Feedback'));
      await tester.pump();

      // Wait for async operation
      await tester.pump(const Duration(milliseconds: 600));

      expect(submittedRating, equals(2));
      expect(submittedNotes, isNull);
    });

    testWidgets('shows loading state during submission',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Submit feedback
      await tester.tap(find.text('Submit Feedback'));
      await tester.pump();

      // Should show loading state
      expect(find.text('Submitting...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for completion
      await tester.pump(const Duration(milliseconds: 600));
    });

    testWidgets('shows skip button when onSkip is provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(includeSkip: true));

      expect(find.text('Skip for now'), findsOneWidget);
    });

    testWidgets('hides skip button when onSkip is not provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(includeSkip: false));

      expect(find.text('Skip for now'), findsNothing);
    });

    testWidgets('calls onSkip when skip button is tapped',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(includeSkip: true));

      await tester.tap(find.text('Skip for now'));
      await tester.pump();

      expect(skipCalled, isTrue);
    });

    testWidgets('disables buttons during submission',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(includeSkip: true));

      // Start submission
      await tester.tap(find.text('Submit Feedback'));
      await tester.pump();

      // Should show loading state
      expect(find.text('Submitting...'), findsOneWidget);

      // Try to tap skip button - should be disabled
      final skipButton = tester.widget<TextButton>(
        find.ancestor(
          of: find.text('Skip for now'),
          matching: find.byType(TextButton),
        ),
      );
      expect(skipButton.onPressed, isNull);

      // Wait for the timer to complete to avoid pending timer error
      await tester.pump(const Duration(milliseconds: 600));
    });

    testWidgets('handles empty notes correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Enter some text then clear it
      await tester.enterText(find.byType(TextField), 'Some text');
      await tester.pump();
      await tester.enterText(find.byType(TextField), '   '); // Only whitespace
      await tester.pump();

      // Submit feedback
      await tester.tap(find.text('Submit Feedback'));
      await tester.pump();

      // Wait for async operation
      await tester.pump(const Duration(milliseconds: 600));

      expect(submittedRating, equals(3)); // Default rating
      expect(submittedNotes, isNull); // Should be null for whitespace-only
    });

    testWidgets('respects character limit in notes field',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.maxLength, equals(500));

      // The counter should be visible
      expect(find.text('0/500'), findsOneWidget);
    });
  });
}
