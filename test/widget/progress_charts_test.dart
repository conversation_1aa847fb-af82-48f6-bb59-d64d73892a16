import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/progress_charts.dart';
import 'package:openfitv2/models/session_analytics.dart';

void main() {
  group('ProgressCharts Widget Tests', () {
    late List<SessionAnalytics> mockSessionData;

    setUp(() {
      mockSessionData = [
        SessionAnalytics(
          sessionId: 'session1',
          totalDuration: const Duration(minutes: 45),
          setsCompleted: 12,
          totalReps: 120,
          totalVolume: 2400.0,
          estimatedCalories: 300,
          exercisePerformance: {
            'exercise1': ExercisePerformance(
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
              setsCompleted: 4,
              totalReps: 40,
              totalVolume: 800.0,
              maxWeight: 135.0,
              maxReps: 12,
              timeSpent: const Duration(minutes: 15),
              setDetails: [
                SetPerformance(
                  setNumber: 1,
                  weight: 135.0,
                  reps: 10,
                  volume: 1350.0,
                  restTime: const Duration(minutes: 2),
                ),
              ],
            ),
          },
          improvementScore: 85.0,
          sessionDate: DateTime(2024, 1, 1),
        ),
        SessionAnalytics(
          sessionId: 'session2',
          totalDuration: const Duration(minutes: 50),
          setsCompleted: 15,
          totalReps: 150,
          totalVolume: 3000.0,
          estimatedCalories: 350,
          exercisePerformance: {
            'exercise1': ExercisePerformance(
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
              setsCompleted: 4,
              totalReps: 44,
              totalVolume: 880.0,
              maxWeight: 145.0,
              maxReps: 12,
              timeSpent: const Duration(minutes: 16),
              setDetails: [
                SetPerformance(
                  setNumber: 1,
                  weight: 145.0,
                  reps: 11,
                  volume: 1595.0,
                  restTime: const Duration(minutes: 2),
                ),
              ],
            ),
          },
          improvementScore: 90.0,
          sessionDate: DateTime(2024, 1, 8),
        ),
      ];
    });

    testWidgets('displays progress chart with session data',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProgressCharts(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Verify the widget displays the exercise name
      expect(find.text('Bench Press'), findsOneWidget);
      expect(find.text('Progress Over Time'), findsOneWidget);

      // Verify chart type selector is present
      expect(find.text('Weight'), findsOneWidget);
      expect(find.text('Reps'), findsOneWidget);
      expect(find.text('Volume'), findsOneWidget);
      expect(find.text('Sets'), findsOneWidget);

      // Verify improvement stats are shown
      expect(find.text('Improvement'), findsOneWidget);
    });

    testWidgets('displays empty state when no data available',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProgressCharts(
              sessionData: [],
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Verify empty state is displayed
      expect(find.text('No data available'), findsOneWidget);
      expect(
          find.text('Complete more workouts to see progress'), findsOneWidget);
      expect(find.byIcon(Icons.analytics_outlined), findsOneWidget);
    });

    testWidgets('switches chart types correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProgressCharts(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Initially Weight should be selected
      final weightChip = find.widgetWithText(FilterChip, 'Weight');
      expect(weightChip, findsOneWidget);

      // Tap on Reps chip
      await tester.tap(find.widgetWithText(FilterChip, 'Reps'));
      await tester.pumpAndSettle();

      // Verify the chart type changed
      expect(find.widgetWithText(FilterChip, 'Reps'), findsOneWidget);
    });

    testWidgets('displays improvement statistics correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProgressCharts(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Verify improvement stats are calculated and displayed
      expect(find.text('Improvement'), findsOneWidget);

      // Weight improvement: 145.0 - 135.0 = +10.0 lbs
      expect(find.textContaining('+10.0 lbs'), findsOneWidget);

      // Volume improvement: 880.0 - 800.0 = +80 vol
      expect(find.textContaining('+80 vol'), findsOneWidget);
    });

    testWidgets('handles exercise data filtering correctly',
        (WidgetTester tester) async {
      final sessionDataWithoutExercise = [
        SessionAnalytics(
          sessionId: 'session1',
          totalDuration: const Duration(minutes: 45),
          setsCompleted: 12,
          totalReps: 120,
          totalVolume: 2400.0,
          estimatedCalories: 300,
          exercisePerformance: {
            'different_exercise': ExercisePerformance(
              exerciseId: 'different_exercise',
              exerciseName: 'Squat',
              setsCompleted: 4,
              totalReps: 40,
              totalVolume: 800.0,
              maxWeight: 135.0,
              maxReps: 12,
              timeSpent: const Duration(minutes: 15),
            ),
          },
          improvementScore: 85.0,
          sessionDate: DateTime(2024, 1, 1),
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProgressCharts(
              sessionData: sessionDataWithoutExercise,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Should show empty state since no data for the specified exercise
      expect(find.text('No data available'), findsOneWidget);
    });

    testWidgets('displays chart legend correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProgressCharts(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Verify legend is displayed at the bottom
      expect(find.text('Weight'),
          findsAtLeastNWidgets(2)); // One in selector, one in legend
    });

    testWidgets('initializes with correct chart type',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProgressCharts(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
              chartType: ProgressChartType.volume,
            ),
          ),
        ),
      );

      // Verify Volume chip is selected initially
      final volumeChip = find.widgetWithText(FilterChip, 'Volume');
      expect(volumeChip, findsOneWidget);

      final FilterChip chip = tester.widget(volumeChip);
      expect(chip.selected, isTrue);
    });

    testWidgets('handles single data point correctly',
        (WidgetTester tester) async {
      final singleSessionData = [mockSessionData.first];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProgressCharts(
              sessionData: singleSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Should still display the chart with single data point
      expect(find.text('Bench Press'), findsOneWidget);
      expect(find.text('Progress Over Time'), findsOneWidget);

      // Improvement should show 0 since there's only one data point
      expect(find.text('Improvement'), findsOneWidget);
    });
  });
}
