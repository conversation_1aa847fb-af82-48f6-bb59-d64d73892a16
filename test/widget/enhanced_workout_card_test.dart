import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/enhanced_workout_card.dart';
import 'package:openfitv2/models/workout.dart';
import 'package:openfitv2/models/exercise.dart';

void main() {
  group('EnhancedWorkoutCard Widget Tests', () {
    late Workout testWorkout;

    setUp(() {
      testWorkout = _createTestWorkout();
    });

    testWidgets('displays workout name and summary', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(workout: testWorkout),
          ),
        ),
      );

      expect(find.text('Test Workout'), findsOneWidget);
      expect(find.text(testWorkout.workoutSummary), findsOneWidget);
    });

    testWidgets('shows workout thumbnail with appropriate icon',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(workout: testWorkout),
          ),
        ),
      );

      // Should show workout icons (one in thumbnail, one in muscle group section)
      expect(find.byIcon(Icons.fitness_center), findsWidgets);
    });

    testWidgets('displays workout stats chips', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(workout: testWorkout),
          ),
        ),
      );

      // Should show duration, calories, and difficulty stats
      expect(find.byIcon(Icons.timer), findsOneWidget);
      expect(find.byIcon(Icons.local_fire_department), findsOneWidget);
      expect(find.byIcon(Icons.trending_up), findsOneWidget);
    });

    testWidgets('shows favorite button and toggles state', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(workout: testWorkout),
          ),
        ),
      );

      // Find the favorite button specifically
      final favoriteButton = find.byType(IconButton).first;
      expect(favoriteButton, findsOneWidget);

      // Tap favorite button
      await tester.tap(favoriteButton);
      await tester.pumpAndSettle();

      // The state should have changed (we can't easily test the icon change due to async SharedPreferences)
      expect(favoriteButton, findsOneWidget);
    });

    testWidgets('displays progress indicator when showProgress is true',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(
              workout: testWorkout,
              showProgress: true,
              progressValue: 0.7,
            ),
          ),
        ),
      );

      expect(find.text('Progress'), findsOneWidget);
      expect(find.text('70%'), findsOneWidget);
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
    });

    testWidgets('shows last completed info when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(
              workout: testWorkout,
              lastCompletedText: 'Last completed 2 days ago',
            ),
          ),
        ),
      );

      expect(find.text('Last completed 2 days ago'), findsOneWidget);
      expect(find.byIcon(Icons.history), findsOneWidget);
    });

    testWidgets('displays AI description when available', (tester) async {
      final workoutWithAI = Workout(
        id: '1',
        userId: 'user1',
        name: 'AI Workout',
        aiDescription: 'This is an AI-generated workout description',
        createdAt: DateTime.now(),
        exercises: [],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(workout: workoutWithAI),
          ),
        ),
      );

      expect(find.text('This is an AI-generated workout description'),
          findsOneWidget);
      expect(find.byIcon(Icons.auto_awesome), findsOneWidget);
    });

    testWidgets('shows exercise preview for workouts with exercises',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(workout: testWorkout),
          ),
        ),
      );

      expect(find.text('Exercises Preview'), findsOneWidget);
      expect(find.text('Push Up • 3×10'), findsOneWidget);
      expect(find.text('Dumbbell Press • 3×12'), findsOneWidget);
    });

    testWidgets('displays muscle group and equipment tags', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(workout: testWorkout),
          ),
        ),
      );

      expect(find.text('Target Muscles'), findsOneWidget);
      expect(find.text('Equipment'), findsOneWidget);
      expect(find.text('Chest'), findsAtLeastNWidgets(1));
      expect(find.text('Dumbbells'), findsAtLeastNWidgets(1));
    });

    testWidgets('shows action buttons with correct text', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(workout: testWorkout),
          ),
        ),
      );

      expect(find.text('Start Workout'), findsOneWidget);
      expect(find.text('Details'), findsOneWidget);
    });

    testWidgets('shows continue workout for active workouts', (tester) async {
      final activeWorkout = Workout(
        id: '1',
        userId: 'user1',
        name: 'Active Workout',
        isActive: true,
        createdAt: DateTime.now(),
        exercises: [],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(workout: activeWorkout),
          ),
        ),
      );

      expect(find.text('Continue Workout'), findsOneWidget);
    });

    testWidgets('calls onTap callback when details button is pressed',
        (tester) async {
      bool onTapCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(
              workout: testWorkout,
              onTap: () {
                onTapCalled = true;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.text('Details'));
      await tester.pump();

      expect(onTapCalled, true);
    });

    testWidgets('calls onStart callback when start button is pressed',
        (tester) async {
      bool onStartCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(
              workout: testWorkout,
              onStart: () {
                onStartCalled = true;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.text('Start Workout'));
      await tester.pump();

      expect(onStartCalled, true);
    });

    testWidgets('truncates long AI descriptions', (tester) async {
      final longDescription =
          'This is a very long AI description that should be truncated because it exceeds the maximum length limit for display in the workout card component';

      final workoutWithLongAI = Workout(
        id: '1',
        userId: 'user1',
        name: 'Long AI Workout',
        aiDescription: longDescription,
        createdAt: DateTime.now(),
        exercises: [],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedWorkoutCard(workout: workoutWithLongAI),
          ),
        ),
      );

      // Should find truncated text with ellipsis
      expect(find.textContaining('...'), findsOneWidget);
    });
  });
}

Workout _createTestWorkout() {
  final exercise1 = Exercise(
    id: '1',
    name: 'Push Up',
    description: 'Basic push up exercise',
    primaryMuscle: 'Chest',
    equipment: null,
    category: 'Strength',
    instructions: 'Get in plank position, Lower body, Push up',
  );

  final exercise2 = Exercise(
    id: '2',
    name: 'Dumbbell Press',
    description: 'Chest press with dumbbells',
    primaryMuscle: 'Chest',
    equipment: 'Dumbbells',
    category: 'Strength',
    instructions: 'Lie on bench, Press dumbbells up',
  );

  final workoutExercise1 = WorkoutExercise(
    id: '1',
    workoutId: '1',
    exerciseId: '1',
    exercise: exercise1,
    sets: 3,
    reps: [10, 10, 10],
    orderIndex: 0,
    name: 'Push Up',
    completed: false,
  );

  final workoutExercise2 = WorkoutExercise(
    id: '2',
    workoutId: '1',
    exerciseId: '2',
    exercise: exercise2,
    sets: 3,
    reps: [12, 12, 12],
    orderIndex: 1,
    name: 'Dumbbell Press',
    completed: false,
  );

  return Workout(
    id: '1',
    userId: 'user1',
    name: 'Test Workout',
    aiDescription: 'A test workout for unit testing',
    createdAt: DateTime.now(),
    exercises: [workoutExercise1, workoutExercise2],
  );
}
