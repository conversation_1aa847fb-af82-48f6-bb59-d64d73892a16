import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/performance_comparison.dart';
import 'package:openfitv2/design_system/design_system.dart';

void main() {
  group('PerformanceComparison Widget Tests', () {
    testWidgets('displays no data message when sessions are null',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: PerformanceComparison(
              currentSession: null,
              previousSession: null,
            ),
          ),
        ),
      );

      expect(find.text('Performance'), findsOneWidget);
      expect(find.text('No current session data'), findsOneWidget);
    });

    testWidgets('displays performance header correctly',
        (WidgetTester tester) async {
      final currentSession = PerformanceMetrics(
        totalReps: 100,
        totalWeight: 500,
        totalVolume: 2500,
        duration: const Duration(minutes: 45),
        setsCompleted: 10,
        timestamp: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: PerformanceComparison(
              currentSession: currentSession,
              previousSession: null,
            ),
          ),
        ),
      );

      expect(find.text('Performance'), findsOneWidget);
      expect(find.byIcon(Icons.trending_up), findsOneWidget);
      expect(find.text('No previous session to compare'), findsOneWidget);
    });

    testWidgets('shows quick comparison when detailed is disabled',
        (WidgetTester tester) async {
      final currentSession = PerformanceMetrics(
        totalReps: 100,
        totalWeight: 500,
        totalVolume: 2500,
        duration: const Duration(minutes: 45),
        setsCompleted: 10,
        timestamp: DateTime.now(),
      );

      final previousSession = PerformanceMetrics(
        totalReps: 90,
        totalWeight: 450,
        totalVolume: 2250,
        duration: const Duration(minutes: 50),
        setsCompleted: 9,
        timestamp: DateTime.now().subtract(const Duration(days: 1)),
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: PerformanceComparison(
              currentSession: currentSession,
              previousSession: previousSession,
              showDetailedComparison: false,
            ),
          ),
        ),
      );

      // Should not show tabs
      expect(find.text('Overall'), findsNothing);
      expect(find.text('Exercise'), findsNothing);

      // Should show quick metrics
      expect(find.text('Volume'), findsOneWidget);
      expect(find.text('2500 lbs'), findsOneWidget);
      expect(find.text('Reps'), findsOneWidget);
      expect(find.text('100'), findsOneWidget);
      expect(find.text('Sets'), findsOneWidget);
      expect(find.text('10'), findsOneWidget);
    });

    testWidgets('handles view history callback', (WidgetTester tester) async {
      bool historyTapped = false;

      final currentSession = PerformanceMetrics(
        totalReps: 100,
        totalWeight: 500,
        totalVolume: 2500,
        duration: const Duration(minutes: 45),
        setsCompleted: 10,
        timestamp: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: PerformanceComparison(
              currentSession: currentSession,
              previousSession: null,
              onViewHistory: () => historyTapped = true,
            ),
          ),
        ),
      );

      // Find and tap history button
      await tester.tap(find.byIcon(Icons.history));
      await tester.pump();

      expect(historyTapped, isTrue);
    });
  });
}
