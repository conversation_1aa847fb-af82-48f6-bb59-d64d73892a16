import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/live_session_tracker.dart';
import 'package:openfitv2/models/workout.dart';
import 'package:openfitv2/models/exercise.dart';
import 'package:openfitv2/design_system/design_system.dart';

void main() {
  group('LiveSessionTracker Widget Tests', () {
    late Workout testWorkout;

    setUp(() {
      final testExercise = Exercise(
        id: '1',
        name: 'Push-ups',
        primaryMuscle: 'Chest',
        equipment: 'None',
      );

      final workoutExercise = WorkoutExercise(
        id: '1',
        workoutId: 'workout1',
        exerciseId: '1',
        exercise: testExercise,
        sets: 3,
        reps: [10, 10, 10],
        weight: [0, 0, 0],
        orderIndex: 0,
        restInterval: 60,
        name: 'Push-ups',
      );

      testWorkout = Workout(
        id: 'workout1',
        userId: 'user1',
        name: 'Test Workout',
        createdAt: DateTime.now(),
        exercises: [workoutExercise],
      );
    });

    testWidgets('displays live session information correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: LiveSessionTracker(
              workout: testWorkout,
              currentExerciseIndex: 0,
              currentSet: 1,
              elapsedSeconds: 300, // 5 minutes
            ),
          ),
        ),
      );

      // Verify header elements
      expect(find.text('Live Session'), findsOneWidget);
      expect(find.text('In Progress'), findsOneWidget);
      expect(find.text('05:00'), findsOneWidget);
      expect(find.text('Elapsed'), findsOneWidget);

      // Verify progress section
      expect(find.text('0%'), findsOneWidget); // No sets completed yet
      expect(find.text('Complete'), findsOneWidget);
      expect(find.text('0 / 3'), findsOneWidget);
      expect(find.text('Sets'), findsOneWidget);

      // Verify current exercise info
      expect(find.text('Current Exercise'), findsOneWidget);
      expect(find.text('Push-ups'), findsOneWidget);
      expect(find.text('Set 1 / 3'), findsOneWidget);
      expect(find.text('Chest'), findsOneWidget);
    });

    testWidgets('shows paused state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: LiveSessionTracker(
              workout: testWorkout,
              currentExerciseIndex: 0,
              currentSet: 1,
              elapsedSeconds: 300,
              isPaused: true,
            ),
          ),
        ),
      );

      expect(find.text('Paused'), findsOneWidget);
      expect(find.byIcon(Icons.pause), findsOneWidget);
      expect(find.text('Resume'), findsOneWidget);
    });

    testWidgets('shows correct progress calculation',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: LiveSessionTracker(
              workout: testWorkout,
              currentExerciseIndex: 0,
              currentSet: 2, // Second set of first exercise
              elapsedSeconds: 600,
            ),
          ),
        ),
      );

      // Should show 33% complete (1 out of 3 sets completed)
      expect(find.text('33%'), findsOneWidget);
      expect(find.text('1 / 3'), findsOneWidget);
      expect(find.text('Set 2 / 3'), findsOneWidget);
    });

    testWidgets('handles pause/resume button taps',
        (WidgetTester tester) async {
      bool pauseCalled = false;
      bool resumeCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: LiveSessionTracker(
              workout: testWorkout,
              currentExerciseIndex: 0,
              currentSet: 1,
              elapsedSeconds: 300,
              onPause: () => pauseCalled = true,
              onResume: () => resumeCalled = true,
            ),
          ),
        ),
      );

      // Tap pause button
      await tester.tap(find.text('Pause'));
      await tester.pump();
      expect(pauseCalled, isTrue);

      // Test with paused state
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: LiveSessionTracker(
              workout: testWorkout,
              currentExerciseIndex: 0,
              currentSet: 1,
              elapsedSeconds: 300,
              isPaused: true,
              onPause: () => pauseCalled = true,
              onResume: () => resumeCalled = true,
            ),
          ),
        ),
      );

      // Tap resume button
      await tester.tap(find.text('Resume'));
      await tester.pump();
      expect(resumeCalled, isTrue);
    });

    testWidgets('shows stats modal when analytics button is tapped',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: LiveSessionTracker(
              workout: testWorkout,
              currentExerciseIndex: 0,
              currentSet: 1,
              elapsedSeconds: 300,
            ),
          ),
        ),
      );

      // Tap analytics button
      await tester.tap(find.byIcon(Icons.analytics_outlined));
      await tester.pumpAndSettle();

      // Verify modal is shown
      expect(find.text('Session Stats'), findsOneWidget);
      expect(find.text('Test Workout'), findsOneWidget);
      expect(find.text('1 / 1'), findsOneWidget); // Exercises
      expect(find.text('1 / 3'), findsOneWidget); // Current Set
      expect(find.text('5m 0s'), findsOneWidget); // Duration
    });

    testWidgets('formats elapsed time correctly', (WidgetTester tester) async {
      // Test different time formats
      final testCases = [
        (30, '00:30'),
        (90, '01:30'),
        (3661, '01:01:01'), // 1 hour, 1 minute, 1 second
      ];

      for (final (seconds, expected) in testCases) {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              extensions: [
                AppColorsTheme.light(),
              ],
            ),
            home: Scaffold(
              body: LiveSessionTracker(
                workout: testWorkout,
                currentExerciseIndex: 0,
                currentSet: 1,
                elapsedSeconds: seconds,
              ),
            ),
          ),
        );

        expect(find.text(expected), findsOneWidget);
      }
    });

    testWidgets('handles empty workout gracefully',
        (WidgetTester tester) async {
      final emptyWorkout = Workout(
        id: 'empty',
        userId: 'user1',
        name: 'Empty Workout',
        createdAt: DateTime.now(),
        exercises: [],
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: LiveSessionTracker(
              workout: emptyWorkout,
              currentExerciseIndex: 0,
              currentSet: 1,
              elapsedSeconds: 300,
            ),
          ),
        ),
      );

      // Should not crash and show basic info
      expect(find.text('Live Session'), findsOneWidget);
      expect(find.text('05:00'), findsOneWidget);
      expect(find.text('0%'), findsOneWidget);
    });

    testWidgets('shows correct accessibility labels',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: LiveSessionTracker(
              workout: testWorkout,
              currentExerciseIndex: 0,
              currentSet: 1,
              elapsedSeconds: 300,
            ),
          ),
        ),
      );

      // Check for accessibility tooltips
      expect(find.byTooltip('View Stats'), findsOneWidget);
    });
  });
}
