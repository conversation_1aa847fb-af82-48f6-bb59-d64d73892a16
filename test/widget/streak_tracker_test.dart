import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/widgets/streak_tracker.dart';
import '../../lib/design_system/design_system.dart';

void main() {
  group('StreakTracker Widget Tests', () {
    testWidgets('displays streak information correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: StreakTracker(
              currentStreak: 7,
              longestStreak: 15,
              consistencyScore: 85.0,
              recentWorkoutDates: [
                DateTime.now(),
                DateTime.now().subtract(const Duration(days: 1)),
                DateTime.now().subtract(const Duration(days: 2)),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify streak information is displayed
      expect(find.text('Workout Streak'), findsOneWidget);
      expect(find.text('7'), findsOneWidget);
      expect(find.text('15 days'), findsOneWidget);
      expect(find.text('85%'),
          findsAtLeastNWidgets(1)); // Appears in multiple places
    });

    testWidgets('displays correct streak message for different streak lengths',
        (WidgetTester tester) async {
      // Test high streak
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: StreakTracker(
              currentStreak: 30,
              longestStreak: 30,
              consistencyScore: 95.0,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Amazing consistency! 🏆'), findsOneWidget);

      // Test medium streak
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: StreakTracker(
              currentStreak: 7,
              longestStreak: 10,
              consistencyScore: 75.0,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Great streak! Keep it up! 🔥'), findsOneWidget);
    });

    testWidgets('displays weekly calendar correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: StreakTracker(
              currentStreak: 3,
              longestStreak: 5,
              consistencyScore: 60.0,
              recentWorkoutDates: [
                DateTime.now(),
                DateTime.now().subtract(const Duration(days: 1)),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify weekly calendar is shown
      expect(find.text('This Week'), findsOneWidget);

      // Should show day abbreviations
      expect(find.text('M'), findsOneWidget);
      expect(find.text('T'), findsAtLeastNWidgets(2)); // Tuesday and Thursday
      expect(find.text('W'), findsOneWidget);
      expect(find.text('F'), findsOneWidget);
      expect(find.text('S'), findsAtLeastNWidgets(2)); // Saturday and Sunday
    });

    testWidgets('displays consistency bar with correct color',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: StreakTracker(
              currentStreak: 5,
              longestStreak: 10,
              consistencyScore: 90.0,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify consistency score is displayed
      expect(find.text('Consistency Score'), findsOneWidget);
      expect(find.text('90%'),
          findsAtLeastNWidgets(1)); // Appears in multiple places
    });
  });
}
