import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/exercise_video_player.dart';

void main() {
  group('ExerciseVideoPlayer Widget Tests', () {
    testWidgets('displays placeholder when no video URL is provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseVideoPlayer(
              exerciseName: 'Push-ups',
            ),
          ),
        ),
      );

      // Should show placeholder
      expect(find.text('No video available'), findsOneWidget);
      expect(find.text('for Push-ups'), findsOneWidget);
      expect(find.byIcon(Icons.video_library_outlined), findsOneWidget);
    });

    testWidgets('displays video player when video URL is provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseVideoPlayer(
              videoUrl: 'https://example.com/video.mp4',
              exerciseName: 'Push-ups',
            ),
          ),
        ),
      );

      // Should show video player
      expect(find.text('Push-ups'), findsOneWidget);
      expect(find.byIcon(Icons.play_circle_filled), findsOneWidget);
      expect(find.text('No video available'), findsNothing);
    });

    testWidgets('shows format toggle when both video formats are available',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseVideoPlayer(
              videoUrl: 'https://example.com/video.mp4',
              verticalVideoUrl: 'https://example.com/vertical.mp4',
              exerciseName: 'Push-ups',
            ),
          ),
        ),
      );

      // Should show format toggle
      expect(find.text('Landscape'), findsOneWidget);
      expect(find.byIcon(Icons.stay_current_landscape), findsOneWidget);
    });

    testWidgets('toggles play/pause when tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseVideoPlayer(
              videoUrl: 'https://example.com/video.mp4',
              exerciseName: 'Push-ups',
            ),
          ),
        ),
      );

      // Initially should show play button
      expect(find.byIcon(Icons.play_circle_filled), findsOneWidget);

      // Tap the video area to play
      await tester.tap(find.byType(ExerciseVideoPlayer));
      await tester.pump();

      // Should now show pause button
      expect(find.byIcon(Icons.pause_circle_filled), findsOneWidget);
    });

    testWidgets('shows controls when showControls is true',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseVideoPlayer(
              videoUrl: 'https://example.com/video.mp4',
              exerciseName: 'Push-ups',
              showControls: true,
            ),
          ),
        ),
      );

      // Should show control buttons
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
      expect(find.byIcon(Icons.fullscreen), findsOneWidget);
    });

    testWidgets('hides controls when showControls is false',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseVideoPlayer(
              videoUrl: 'https://example.com/video.mp4',
              exerciseName: 'Push-ups',
              showControls: false,
            ),
          ),
        ),
      );

      // Should not show control buttons in overlay
      expect(find.byIcon(Icons.play_arrow), findsNothing);
      expect(find.byIcon(Icons.fullscreen), findsNothing);
    });

    testWidgets('toggles video format when format toggle is tapped',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseVideoPlayer(
              videoUrl: 'https://example.com/video.mp4',
              verticalVideoUrl: 'https://example.com/vertical.mp4',
              exerciseName: 'Push-ups',
            ),
          ),
        ),
      );

      // Initially should show landscape
      expect(find.text('Landscape'), findsOneWidget);
      expect(find.byIcon(Icons.stay_current_landscape), findsOneWidget);

      // Tap format toggle
      await tester.tap(find.text('Landscape'));
      await tester.pump();

      // Should now show vertical
      expect(find.text('Vertical'), findsOneWidget);
      expect(find.byIcon(Icons.stay_current_portrait), findsOneWidget);
    });

    testWidgets('starts playing automatically when autoPlay is true',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ExerciseVideoPlayer(
              videoUrl: 'https://example.com/video.mp4',
              exerciseName: 'Push-ups',
              autoPlay: true,
            ),
          ),
        ),
      );

      // Should show pause button since it's auto-playing
      expect(find.byIcon(Icons.pause_circle_filled), findsOneWidget);
    });
  });
}
