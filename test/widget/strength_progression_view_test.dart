import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/strength_progression_view.dart';
import 'package:openfitv2/models/session_analytics.dart';

void main() {
  group('StrengthProgressionView Widget Tests', () {
    late List<SessionAnalytics> mockSessionData;

    setUp(() {
      mockSessionData = [
        SessionAnalytics(
          sessionId: 'session1',
          totalDuration: const Duration(minutes: 45),
          setsCompleted: 12,
          totalReps: 120,
          totalVolume: 2400.0,
          estimatedCalories: 300,
          exercisePerformance: {
            'exercise1': ExercisePerformance(
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
              setsCompleted: 4,
              totalReps: 40,
              totalVolume: 800.0,
              maxWeight: 135.0,
              maxReps: 10,
              timeSpent: const Duration(minutes: 15),
              setDetails: [
                SetPerformance(
                  setNumber: 1,
                  weight: 135.0,
                  reps: 10,
                  volume: 1350.0,
                  restTime: const Duration(minutes: 2),
                ),
              ],
            ),
          },
          improvementScore: 85.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 30)),
        ),
        SessionAnalytics(
          sessionId: 'session2',
          totalDuration: const Duration(minutes: 50),
          setsCompleted: 15,
          totalReps: 150,
          totalVolume: 3000.0,
          estimatedCalories: 350,
          exercisePerformance: {
            'exercise1': ExercisePerformance(
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
              setsCompleted: 4,
              totalReps: 44,
              totalVolume: 880.0,
              maxWeight: 145.0,
              maxReps: 12,
              timeSpent: const Duration(minutes: 16),
              setDetails: [
                SetPerformance(
                  setNumber: 1,
                  weight: 145.0,
                  reps: 12,
                  volume: 1740.0,
                  restTime: const Duration(minutes: 2),
                ),
              ],
            ),
          },
          improvementScore: 90.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 15)),
        ),
        SessionAnalytics(
          sessionId: 'session3',
          totalDuration: const Duration(minutes: 55),
          setsCompleted: 16,
          totalReps: 160,
          totalVolume: 3200.0,
          estimatedCalories: 380,
          exercisePerformance: {
            'exercise1': ExercisePerformance(
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
              setsCompleted: 4,
              totalReps: 48,
              totalVolume: 960.0,
              maxWeight: 155.0,
              maxReps: 12,
              timeSpent: const Duration(minutes: 17),
              setDetails: [
                SetPerformance(
                  setNumber: 1,
                  weight: 155.0,
                  reps: 12,
                  volume: 1860.0,
                  restTime: const Duration(minutes: 2),
                ),
              ],
            ),
          },
          improvementScore: 95.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 5)),
        ),
      ];
    });

    testWidgets('displays strength progression view with session data',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Verify the widget displays the correct headers
      expect(find.text('Strength Progression'), findsOneWidget);
      expect(find.text('Bench Press'), findsOneWidget);

      // Verify metric selector is present
      expect(find.text('1RM'), findsOneWidget);
      expect(find.text('Max Weight'), findsOneWidget);
      expect(find.text('Total Volume'), findsOneWidget);
      expect(find.text('Avg Weight'), findsOneWidget);

      // Verify time range chip is displayed
      expect(find.text('3M'), findsOneWidget); // 90 days = ~3 months
    });

    testWidgets('displays empty state when no data available',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: [],
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Verify empty state is displayed
      expect(find.text('No progression data'), findsOneWidget);
      expect(find.text('Complete more workouts to track strength gains'),
          findsOneWidget);
      expect(find.byIcon(Icons.trending_up_outlined), findsOneWidget);
    });

    testWidgets('switches strength metrics correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Initially 1RM should be selected
      final oneRMChip = find.widgetWithText(FilterChip, '1RM');
      expect(oneRMChip, findsOneWidget);

      // Tap on Max Weight chip
      await tester.tap(find.widgetWithText(FilterChip, 'Max Weight'));
      await tester.pumpAndSettle();

      // Verify the metric type changed
      expect(find.widgetWithText(FilterChip, 'Max Weight'), findsOneWidget);
    });

    testWidgets('displays progression statistics correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Verify progression stats section is present
      expect(find.text('Starting 1RM'), findsOneWidget);
      expect(find.text('Current 1RM'), findsOneWidget);
      expect(find.text('Improvement'), findsOneWidget);

      // Verify icons are present
      expect(find.byIcon(Icons.play_arrow_outlined), findsOneWidget);
      expect(find.byIcon(Icons.trending_up_outlined), findsOneWidget);
      expect(find.byIcon(Icons.celebration_outlined), findsOneWidget);
    });

    testWidgets('filters data by time range correctly',
        (WidgetTester tester) async {
      // Create data with some sessions outside the time range
      final extendedSessionData = [
        ...mockSessionData,
        SessionAnalytics(
          sessionId: 'old_session',
          totalDuration: const Duration(minutes: 40),
          setsCompleted: 10,
          totalReps: 100,
          totalVolume: 2000.0,
          estimatedCalories: 250,
          exercisePerformance: {
            'exercise1': ExercisePerformance(
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
              setsCompleted: 3,
              totalReps: 30,
              totalVolume: 600.0,
              maxWeight: 125.0,
              maxReps: 10,
              timeSpent: const Duration(minutes: 12),
            ),
          },
          improvementScore: 80.0,
          sessionDate: DateTime.now()
              .subtract(const Duration(days: 200)), // Outside 90-day range
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: extendedSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
              timeRange: const Duration(days: 90),
            ),
          ),
        ),
      );

      // Should still display the chart (old session should be filtered out)
      expect(find.text('Strength Progression'), findsOneWidget);
      expect(find.text('Bench Press'), findsOneWidget);
    });

    testWidgets('handles exercise data filtering correctly',
        (WidgetTester tester) async {
      final sessionDataWithoutExercise = [
        SessionAnalytics(
          sessionId: 'session1',
          totalDuration: const Duration(minutes: 45),
          setsCompleted: 12,
          totalReps: 120,
          totalVolume: 2400.0,
          estimatedCalories: 300,
          exercisePerformance: {
            'different_exercise': ExercisePerformance(
              exerciseId: 'different_exercise',
              exerciseName: 'Squat',
              setsCompleted: 4,
              totalReps: 40,
              totalVolume: 800.0,
              maxWeight: 135.0,
              maxReps: 12,
              timeSpent: const Duration(minutes: 15),
            ),
          },
          improvementScore: 85.0,
          sessionDate: DateTime.now().subtract(const Duration(days: 30)),
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: sessionDataWithoutExercise,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Should show empty state since no data for the specified exercise
      expect(find.text('No progression data'), findsOneWidget);
    });

    testWidgets('displays time range chip with correct format',
        (WidgetTester tester) async {
      // Test different time ranges
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
              timeRange: const Duration(days: 30),
            ),
          ),
        ),
      );

      expect(find.text('30D'), findsOneWidget);

      // Test yearly range
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
              timeRange: const Duration(days: 365),
            ),
          ),
        ),
      );

      expect(find.text('1Y'), findsOneWidget);
    });

    testWidgets('calculates 1RM correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Verify that 1RM calculations are displayed
      expect(find.textContaining('lbs'),
          findsAtLeastNWidgets(3)); // Starting, Current, Improvement
    });

    testWidgets('handles single data point correctly',
        (WidgetTester tester) async {
      final singleSessionData = [mockSessionData.first];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: singleSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Should still display the chart with single data point
      expect(find.text('Strength Progression'), findsOneWidget);
      expect(find.text('Bench Press'), findsOneWidget);
    });

    testWidgets('displays improvement percentage correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StrengthProgressionView(
              sessionData: mockSessionData,
              exerciseId: 'exercise1',
              exerciseName: 'Bench Press',
            ),
          ),
        ),
      );

      // Verify improvement percentage is shown
      expect(find.textContaining('%'), findsOneWidget);
    });
  });
}
