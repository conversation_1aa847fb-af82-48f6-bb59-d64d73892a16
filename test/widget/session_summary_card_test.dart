import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/session_summary_card.dart';
import 'package:openfitv2/models/session_analytics.dart';

void main() {
  group('SessionSummaryCard', () {
    late SessionAnalytics mockSessionAnalytics;
    late SessionAnalytics mockPreviousSession;

    setUp(() {
      mockSessionAnalytics = SessionAnalytics(
        sessionId: 'session-1',
        totalDuration: const Duration(minutes: 45, seconds: 30),
        setsCompleted: 12,
        totalReps: 120,
        totalVolume: 2400.0,
        estimatedCalories: 350,
        exercisePerformance: {
          'exercise-1': ExercisePerformance(
            exerciseId: 'exercise-1',
            exerciseName: 'Bench Press',
            setsCompleted: 4,
            totalReps: 40,
            totalVolume: 800.0,
            maxWeight: 135.0,
            maxReps: 12,
            timeSpent: const Duration(minutes: 15),
          ),
        },
        newRecords: [
          PersonalRecord(
            exerciseId: 'exercise-1',
            exerciseName: 'Bench Press',
            type: PersonalRecordType.maxWeight,
            value: 135.0,
            achievedDate: DateTime.now(),
            previousValue: 130.0,
          ),
        ],
        improvementScore: 85.0,
        sessionDate: DateTime.now(),
      );

      mockPreviousSession = SessionAnalytics(
        sessionId: 'session-0',
        totalDuration: const Duration(minutes: 40),
        setsCompleted: 10,
        totalReps: 100,
        totalVolume: 2200.0,
        estimatedCalories: 320,
        exercisePerformance: {},
        newRecords: [],
        improvementScore: 75.0,
        sessionDate: DateTime.now().subtract(const Duration(days: 3)),
      );
    });

    Widget createTestWidget({
      SessionAnalytics? sessionAnalytics,
      SessionAnalytics? previousSession,
      VoidCallback? onViewDetails,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: SessionSummaryCard(
            sessionAnalytics: sessionAnalytics ?? mockSessionAnalytics,
            previousSession: previousSession,
            onViewDetails: onViewDetails,
          ),
        ),
      );
    }

    testWidgets('displays basic session information',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Workout Complete!'), findsOneWidget);
      expect(find.text('Great job on your session'), findsOneWidget);
      expect(find.text('45m 30s'), findsOneWidget);
      expect(find.text('12'), findsOneWidget);
      expect(find.text('350'), findsOneWidget);
      expect(find.text('2400 lbs'), findsOneWidget);
    });

    testWidgets('displays performance grade', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('A'), findsOneWidget);
    });

    testWidgets('shows improvement comparison when previous session exists',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        previousSession: mockPreviousSession,
      ));

      expect(find.text('Improved: +200.0 lbs volume, +2 sets, +20 reps'),
          findsOneWidget);
      expect(find.byIcon(Icons.trending_up), findsAtLeastNWidgets(1));
    });

    testWidgets('shows first workout message when no previous session',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('First time doing this workout! Great start!'),
          findsOneWidget);
      expect(find.byIcon(Icons.star), findsOneWidget);
    });

    testWidgets('displays personal records when available',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('New Personal Records!'), findsOneWidget);
      expect(find.text('• New max weight for Bench Press: 135.0 lbs'),
          findsOneWidget);
    });

    testWidgets('shows view details button when callback provided',
        (WidgetTester tester) async {
      bool buttonPressed = false;

      await tester.pumpWidget(createTestWidget(
        onViewDetails: () => buttonPressed = true,
      ));

      expect(find.text('View Detailed Analysis'), findsOneWidget);

      await tester.tap(find.text('View Detailed Analysis'));
      await tester.pump();

      expect(buttonPressed, isTrue);
    });

    testWidgets('hides view details button when no callback provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('View Detailed Analysis'), findsNothing);
    });

    testWidgets('handles session with no personal records',
        (WidgetTester tester) async {
      final sessionWithoutRecords = SessionAnalytics(
        sessionId: 'session-2',
        totalDuration: const Duration(minutes: 30),
        setsCompleted: 8,
        totalReps: 80,
        totalVolume: 1600.0,
        estimatedCalories: 250,
        exercisePerformance: {},
        newRecords: [],
        improvementScore: 70.0,
        sessionDate: DateTime.now(),
      );

      await tester.pumpWidget(createTestWidget(
        sessionAnalytics: sessionWithoutRecords,
      ));

      expect(find.text('New Personal Records!'), findsNothing);
    });

    testWidgets('shows decline message when performance decreased',
        (WidgetTester tester) async {
      final worseSession = SessionAnalytics(
        sessionId: 'session-worse',
        totalDuration: const Duration(minutes: 35),
        setsCompleted: 8,
        totalReps: 80,
        totalVolume: 1800.0,
        estimatedCalories: 280,
        exercisePerformance: {},
        newRecords: [],
        improvementScore: 65.0,
        sessionDate: DateTime.now(),
      );

      await tester.pumpWidget(createTestWidget(
        sessionAnalytics: worseSession,
        previousSession: mockPreviousSession,
      ));

      expect(find.text('Keep pushing! -400.0 lbs volume, -2 sets, -20 reps'),
          findsOneWidget);
      expect(find.byIcon(Icons.trending_down), findsOneWidget);
    });

    testWidgets('shows similar performance message when no change',
        (WidgetTester tester) async {
      final sameSession = SessionAnalytics(
        sessionId: 'session-same',
        totalDuration: const Duration(minutes: 40),
        setsCompleted: 10,
        totalReps: 100,
        totalVolume: 2200.0,
        estimatedCalories: 320,
        exercisePerformance: {},
        newRecords: [],
        improvementScore: 75.0,
        sessionDate: DateTime.now(),
      );

      await tester.pumpWidget(createTestWidget(
        sessionAnalytics: sameSession,
        previousSession: mockPreviousSession,
      ));

      expect(find.text('Similar performance to last time'), findsOneWidget);
      expect(find.byIcon(Icons.horizontal_rule), findsOneWidget);
    });

    testWidgets('displays multiple personal records correctly',
        (WidgetTester tester) async {
      final sessionWithMultipleRecords = SessionAnalytics(
        sessionId: 'session-multi-records',
        totalDuration: const Duration(minutes: 50),
        setsCompleted: 15,
        totalReps: 150,
        totalVolume: 3000.0,
        estimatedCalories: 400,
        exercisePerformance: {},
        newRecords: [
          PersonalRecord(
            exerciseId: 'exercise-1',
            exerciseName: 'Bench Press',
            type: PersonalRecordType.maxWeight,
            value: 135.0,
            achievedDate: DateTime.now(),
          ),
          PersonalRecord(
            exerciseId: 'exercise-2',
            exerciseName: 'Squat',
            type: PersonalRecordType.maxReps,
            value: 15.0,
            achievedDate: DateTime.now(),
          ),
          PersonalRecord(
            exerciseId: 'exercise-3',
            exerciseName: 'Deadlift',
            type: PersonalRecordType.maxVolume,
            value: 2000.0,
            achievedDate: DateTime.now(),
          ),
          PersonalRecord(
            exerciseId: 'exercise-4',
            exerciseName: 'Pull-ups',
            type: PersonalRecordType.maxReps,
            value: 12.0,
            achievedDate: DateTime.now(),
          ),
        ],
        improvementScore: 90.0,
        sessionDate: DateTime.now(),
      );

      await tester.pumpWidget(createTestWidget(
        sessionAnalytics: sessionWithMultipleRecords,
      ));

      expect(find.text('New Personal Records!'), findsOneWidget);
      expect(find.text('+ 1 more records'), findsOneWidget);
    });

    testWidgets('displays correct performance grade colors',
        (WidgetTester tester) async {
      // Test A+ grade (90+)
      final excellentSession = SessionAnalytics(
        sessionId: 'excellent',
        totalDuration: const Duration(minutes: 45),
        setsCompleted: 12,
        totalReps: 120,
        totalVolume: 2400.0,
        estimatedCalories: 350,
        exercisePerformance: {},
        newRecords: [],
        improvementScore: 95.0,
        sessionDate: DateTime.now(),
      );

      await tester.pumpWidget(createTestWidget(
        sessionAnalytics: excellentSession,
      ));

      expect(find.text('A+'), findsOneWidget);
    });
  });
}
