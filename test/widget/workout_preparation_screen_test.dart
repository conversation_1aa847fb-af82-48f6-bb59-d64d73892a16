import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/models/workout.dart';
import 'package:openfitv2/models/exercise.dart';
import 'package:openfitv2/screens/workout_preparation_screen.dart';

void main() {
  group('WorkoutPreparationScreen Widget Tests', () {
    late Workout testWorkout;

    setUp(() {
      testWorkout = Workout(
        id: '1',
        userId: 'test-user',
        name: 'Test Workout',
        aiDescription: 'A test workout for unit testing',
        createdAt: DateTime.now(),
        exercises: [
          WorkoutExercise(
            id: '1',
            workoutId: '1',
            exerciseId: 'ex1',
            exercise: Exercise(
              id: 'ex1',
              name: 'Push-ups',
              primaryMuscle: 'Chest',
              equipment: 'None',
              videoUrl: 'https://example.com/pushups.mp4',
            ),
            sets: 3,
            reps: [10, 10, 10],
            orderIndex: 0,
            name: 'Push-ups',
          ),
          WorkoutExercise(
            id: '2',
            workoutId: '1',
            exerciseId: 'ex2',
            exercise: Exercise(
              id: 'ex2',
              name: 'Bench Press',
              primaryMuscle: 'Chest',
              equipment: 'Barbell',
              videoUrl: 'https://example.com/bench.mp4',
            ),
            sets: 3,
            reps: [8, 8, 8],
            orderIndex: 1,
            name: 'Bench Press',
          ),
        ],
      );
    });

    Widget createTestWidget() {
      return MaterialApp(
        theme: ThemeData.light(),
        home: WorkoutPreparationScreen(workout: testWorkout),
      );
    }

    testWidgets('displays workout preparation screen correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Workout Preparation'), findsOneWidget);
      expect(find.text('Preparation Progress'), findsOneWidget);
      expect(find.text('Workout Overview'), findsOneWidget);
    });

    testWidgets('shows workout overview section', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Workout Overview'), findsOneWidget);
      expect(find.text('2'), findsOneWidget); // Exercise count
      expect(find.text('Exercises'), findsOneWidget);
    });

    testWidgets('displays equipment checklist', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Equipment Checklist'), findsOneWidget);
      expect(find.text('Barbell'), findsOneWidget);
    });

    testWidgets('shows no equipment message for bodyweight workout',
        (WidgetTester tester) async {
      final bodyweightWorkout = Workout(
        id: '2',
        userId: 'test-user',
        name: 'Bodyweight Workout',
        createdAt: DateTime.now(),
        exercises: [
          WorkoutExercise(
            id: '1',
            workoutId: '2',
            exerciseId: 'ex1',
            exercise: Exercise(
              id: 'ex1',
              name: 'Push-ups',
              primaryMuscle: 'Chest',
              equipment: 'None',
            ),
            sets: 3,
            reps: [10, 10, 10],
            orderIndex: 0,
            name: 'Push-ups',
          ),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: WorkoutPreparationScreen(workout: bodyweightWorkout),
        ),
      );

      expect(find.text('No Equipment Needed!'), findsOneWidget);
      expect(find.text('This workout uses bodyweight exercises only'),
          findsOneWidget);
    });

    testWidgets('allows checking equipment items', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find and tap the checkbox for Barbell
      final checkboxes = find.byType(Checkbox);
      expect(checkboxes, findsAtLeastNWidgets(1));

      // Scroll to make sure checkbox is visible
      await tester.ensureVisible(checkboxes.first);
      await tester.pumpAndSettle();

      await tester.tap(checkboxes.first, warnIfMissed: false);
      await tester.pump();

      // Verify checkbox is checked
      final checkboxWidget = tester.widget<Checkbox>(checkboxes.first);
      expect(checkboxWidget.value, isTrue);
    });

    testWidgets('shows exercise preview section', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Exercise Preview'), findsOneWidget);
      expect(find.text('1 of 2'), findsOneWidget);
      expect(find.textContaining('Push-ups'), findsAtLeastNWidgets(1));
    });

    testWidgets('navigates between exercises in preview',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Initially should show first exercise
      expect(find.textContaining('Push-ups'), findsAtLeastNWidgets(1));
      expect(find.text('1 of 2'), findsOneWidget);

      // Scroll to make sure Next button is visible
      await tester.ensureVisible(find.text('Next'));
      await tester.pumpAndSettle();

      // Tap next button
      await tester.tap(find.text('Next'));
      await tester.pump();

      // Should now show second exercise
      expect(find.textContaining('Bench Press'), findsAtLeastNWidgets(1));
      expect(find.text('2 of 2'), findsOneWidget);

      // Tap previous button
      await tester.tap(find.text('Previous'));
      await tester.pump();

      // Should show first exercise again
      expect(find.textContaining('Push-ups'), findsAtLeastNWidgets(1));
      expect(find.text('1 of 2'), findsOneWidget);
    });

    testWidgets('shows workout customization section',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Customize Workout'), findsOneWidget);
      expect(find.text('Skip Exercises'), findsOneWidget);
    });

    testWidgets('allows skipping exercises', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find checkboxes in the skip exercises section
      final skipCheckboxes = find.byType(Checkbox);
      expect(skipCheckboxes, findsAtLeastNWidgets(1));

      // Scroll to make sure the last checkbox is visible
      await tester.ensureVisible(skipCheckboxes.last);
      await tester.pumpAndSettle();

      // Tap the last checkbox (should be for skipping exercises)
      await tester.tap(skipCheckboxes.last, warnIfMissed: false);
      await tester.pump();

      // Verify checkbox is checked
      final checkboxWidget = tester.widget<Checkbox>(skipCheckboxes.last);
      expect(checkboxWidget.value, isTrue);
    });

    testWidgets('disables start button when equipment not checked',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Scroll to make sure Start Workout button is visible
      await tester.ensureVisible(find.text('Start Workout'));
      await tester.pumpAndSettle();

      // Find start workout button
      final startButton = find.text('Start Workout');
      expect(startButton, findsOneWidget);

      // Button should be disabled initially (AppButton uses ElevatedButton internally)
      final buttonWidget = tester.widget<ElevatedButton>(
        find.ancestor(
          of: startButton,
          matching: find.byType(ElevatedButton),
        ),
      );
      expect(buttonWidget.onPressed, isNull);
    });

    testWidgets('enables start button when all equipment is checked',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check the equipment checkbox
      final equipmentCheckbox = find.byType(Checkbox).first;
      await tester.ensureVisible(equipmentCheckbox);
      await tester.pumpAndSettle();

      await tester.tap(equipmentCheckbox, warnIfMissed: false);
      await tester.pump();

      // Scroll to make sure Start Workout button is visible
      await tester.ensureVisible(find.text('Start Workout'));
      await tester.pumpAndSettle();

      // Start button should now be enabled
      final startButton = find.text('Start Workout');
      final buttonWidget = tester.widget<ElevatedButton>(
        find.ancestor(
          of: startButton,
          matching: find.byType(ElevatedButton),
        ),
      );
      expect(buttonWidget.onPressed, isNotNull);
    });

    testWidgets('shows progress indicator correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byType(LinearProgressIndicator), findsOneWidget);
      expect(find.textContaining('/4'), findsOneWidget); // Progress fraction
    });

    testWidgets('shows cancel button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets('displays exercise video player', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Should show video player for the current exercise
      expect(find.byType(Container), findsAtLeastNWidgets(1));
    });

    testWidgets('shows warning when equipment not ready',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Complete equipment checklist to start workout'),
          findsOneWidget);
      expect(find.byIcon(Icons.warning_amber), findsOneWidget);
    });

    testWidgets('allows marking exercises as favorites',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find the favorite button in the exercise preview
      final favoriteButton = find.byIcon(Icons.favorite_border);
      expect(favoriteButton, findsOneWidget);

      // Scroll to make sure favorite button is visible
      await tester.ensureVisible(favoriteButton);
      await tester.pumpAndSettle();

      // Tap the favorite button
      await tester.tap(favoriteButton);
      await tester.pump();

      // Should now show filled favorite icon
      expect(find.byIcon(Icons.favorite), findsOneWidget);
      expect(find.byIcon(Icons.favorite_border), findsNothing);
    });

    testWidgets('shows estimated calories and difficulty level',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Calories'), findsOneWidget);
      expect(find.text('Difficulty'), findsOneWidget);
      // The test workout has 2 exercises with 3 sets each and 1 equipment (Barbell)
      // Score = 2*2 + 6 + 1*3 = 4 + 6 + 3 = 13, which should be "Intermediate"
      expect(find.textContaining('Intermediate'), findsAtLeastNWidgets(1));
    });

    testWidgets('handles empty exercise list gracefully',
        (WidgetTester tester) async {
      final emptyWorkout = Workout(
        id: '3',
        userId: 'test-user',
        name: 'Empty Workout',
        createdAt: DateTime.now(),
        exercises: [],
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: WorkoutPreparationScreen(workout: emptyWorkout),
        ),
      );

      expect(find.text('Workout Preparation'), findsOneWidget);
      expect(find.textContaining('0'),
          findsAtLeastNWidgets(1)); // Exercise count should be 0
    });
  });
}
