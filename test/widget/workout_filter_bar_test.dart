import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/workout_filter_bar.dart';
import 'package:openfitv2/services/workout_filter_service.dart';
import 'package:openfitv2/models/workout.dart';
import 'package:openfitv2/models/exercise.dart';

void main() {
  group('WorkoutFilterBar Widget Tests', () {
    late WorkoutFilterService filterService;
    late List<Workout> testWorkouts;

    setUp(() {
      filterService = WorkoutFilterService();
      testWorkouts = _createTestWorkouts();
      filterService.setWorkouts(testWorkouts);
    });

    testWidgets('displays search bar with correct hint text', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBar(filterService: filterService),
          ),
        ),
      );

      expect(find.text('Search workouts, exercises, or muscle groups...'),
          findsOneWidget);
      expect(find.byIcon(Icons.search), findsOneWidget);
    });

    testWidgets('shows filter button with correct initial state',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBar(filterService: filterService),
          ),
        ),
      );

      expect(find.text('Filters'), findsOneWidget);
      expect(find.byIcon(Icons.tune), findsOneWidget);
    });

    testWidgets('updates filter button when filters are active',
        (tester) async {
      // Apply a filter
      filterService.toggleMuscleGroup('Chest');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBar(filterService: filterService),
          ),
        ),
      );

      expect(find.text('Filters (1)'), findsOneWidget);
    });

    testWidgets('shows active filter chips when filters are applied',
        (tester) async {
      // Apply filters
      filterService.toggleMuscleGroup('Chest');
      filterService.toggleEquipment('Dumbbells');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBar(filterService: filterService),
          ),
        ),
      );

      expect(find.text('Chest'), findsOneWidget);
      expect(find.text('Dumbbells'), findsOneWidget);
      expect(find.text('Clear All'), findsOneWidget);
    });

    testWidgets('removes filter chip when close button is tapped',
        (tester) async {
      // Apply a filter
      filterService.toggleMuscleGroup('Chest');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBar(filterService: filterService),
          ),
        ),
      );

      // Find and tap the close button on the Chest chip
      final closeButton = find.descendant(
        of: find.ancestor(
          of: find.text('Chest'),
          matching: find.byType(Chip),
        ),
        matching: find.byIcon(Icons.close),
      );

      await tester.tap(closeButton);
      await tester.pump();

      expect(filterService.criteria.muscleGroups.contains('Chest'), false);
    });

    testWidgets('clears all filters when clear all button is tapped',
        (tester) async {
      // Apply multiple filters
      filterService.toggleMuscleGroup('Chest');
      filterService.toggleEquipment('Dumbbells');
      filterService.toggleDifficulty(WorkoutDifficulty.beginner);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBar(filterService: filterService),
          ),
        ),
      );

      // Tap clear all button
      await tester.tap(find.text('Clear All'));
      await tester.pump();

      expect(filterService.criteria.hasActiveFilters, false);
    });

    testWidgets('shows search suggestions when typing', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBar(filterService: filterService),
          ),
        ),
      );

      // Focus on search field and type
      await tester.tap(find.byType(TextField));
      await tester.enterText(find.byType(TextField), 'Push');
      await tester.pump();

      // Should show suggestions containing "Push"
      expect(find.text('Push Up Workout'), findsOneWidget);
    });

    testWidgets('selects suggestion when tapped', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBar(filterService: filterService),
          ),
        ),
      );

      // Focus on search field and type
      await tester.tap(find.byType(TextField));
      await tester.enterText(find.byType(TextField), 'Push');
      await tester.pump();

      // Tap on suggestion
      await tester.tap(find.text('Push Up Workout'));
      await tester.pump();

      // Check that the search field is updated
      expect(find.text('Push Up Workout'), findsOneWidget);
    });

    testWidgets('opens filter bottom sheet when filter button is tapped',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBar(filterService: filterService),
          ),
        ),
      );

      // Tap filter button
      await tester.tap(find.text('Filters'));
      await tester.pumpAndSettle();

      // Should show bottom sheet with filter options
      expect(find.text('Filter Workouts'), findsOneWidget);
    });

    testWidgets('calls onFiltersChanged callback when filters are updated',
        (tester) async {
      bool callbackCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBar(
              filterService: filterService,
              onFiltersChanged: () {
                callbackCalled = true;
              },
            ),
          ),
        ),
      );

      // Apply a filter by typing in search
      await tester.tap(find.byType(TextField));
      await tester.enterText(find.byType(TextField), 'test');
      await tester.pump();

      expect(callbackCalled, true);
    });
  });

  group('WorkoutFilterBottomSheet Widget Tests', () {
    late WorkoutFilterService filterService;

    setUp(() {
      filterService = WorkoutFilterService();
      filterService.setWorkouts(_createTestWorkouts());
    });

    testWidgets('displays filter bottom sheet', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkoutFilterBottomSheet(filterService: filterService),
          ),
        ),
      );

      expect(find.text('Filter Workouts'), findsOneWidget);
      expect(find.text('Clear All'), findsOneWidget);
    });
  });
}

List<Workout> _createTestWorkouts() {
  final exercise1 = Exercise(
    id: '1',
    name: 'Push Up',
    description: 'Basic push up exercise',
    primaryMuscle: 'Chest',
    equipment: null,
    category: 'Strength',
    instructions: 'Get in plank position, Lower body, Push up',
  );

  final exercise2 = Exercise(
    id: '2',
    name: 'Dumbbell Press',
    description: 'Chest press with dumbbells',
    primaryMuscle: 'Chest',
    equipment: 'Dumbbells',
    category: 'Strength',
    instructions: 'Lie on bench, Press dumbbells up',
  );

  final workoutExercise1 = WorkoutExercise(
    id: '1',
    workoutId: '1',
    exerciseId: '1',
    exercise: exercise1,
    sets: 3,
    reps: [10, 10, 10],
    orderIndex: 0,
    name: 'Push Up',
    completed: false,
  );

  final workoutExercise2 = WorkoutExercise(
    id: '2',
    workoutId: '2',
    exerciseId: '2',
    exercise: exercise2,
    sets: 3,
    reps: [12, 12, 12],
    orderIndex: 0,
    name: 'Dumbbell Press',
    completed: false,
  );

  return [
    Workout(
      id: '1',
      userId: 'user1',
      name: 'Push Up Workout',
      aiDescription: 'A bodyweight chest workout',
      createdAt: DateTime.now(),
      exercises: [workoutExercise1],
    ),
    Workout(
      id: '2',
      userId: 'user1',
      name: 'Dumbbell Chest Workout',
      aiDescription: 'A dumbbell-based chest workout',
      createdAt: DateTime.now(),
      exercises: [workoutExercise2],
    ),
  ];
}
