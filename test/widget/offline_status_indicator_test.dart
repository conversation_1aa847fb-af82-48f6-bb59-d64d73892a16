import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import '../../lib/widgets/offline_status_indicator.dart';
import '../../lib/models/offline_workout_session.dart';
import '../../lib/design_system/design_system.dart';

// Mock classes
class MockOfflineService extends ChangeNotifier {
  final OfflineSessionSummary _mockSummary;

  MockOfflineService(this._mockSummary);

  Future<OfflineSessionSummary> getSessionSummary({String? userId}) async {
    return _mockSummary;
  }
}

class MockSyncService extends ChangeNotifier {
  final bool _isSyncing;

  MockSyncService({
    required bool isSyncing,
  }) : _isSyncing = isSyncing;

  bool get isSyncing => _isSyncing;

  Map<String, dynamic> getSyncStatistics() {
    return {
      'totalSyncAttempts': 10,
      'successfulSyncs': 8,
      'failedSyncs': 2,
      'successRate': '80.0',
      'lastSyncAttempt': DateTime.now().toIso8601String(),
      'lastSuccessfulSync': DateTime.now().subtract(const Duration(minutes: 5)).toIso8601String(),
      'isSyncing': _isSyncing,
      'autoSyncEnabled': true,
    };
  }

  Future<MockSyncResult> syncPendingData() async {
    return MockSyncResult(
      success: true,
      message: 'Sync completed successfully',
      syncedSessions: 1,
      failedSessions: 0,
    );
  }
}

class MockSyncResult {
  final bool success;
  final String message;
  final int syncedSessions;
  final int failedSessions;

  MockSyncResult({
    required this.success,
    required this.message,
    required this.syncedSessions,
    required this.failedSessions,
  });
}

void main() {
  group('OfflineStatusIndicator', () {
    testWidgets('shows nothing when online and no pending data', (WidgetTester tester) async {
      final mockSummary = OfflineSessionSummary(
        totalSessions: 0,
        pendingSyncSessions: 0,
        failedSyncSessions: 0,
        totalOfflineVolume: 0.0,
        totalOfflineTime: Duration.zero,
      );

      final mockOfflineService = MockOfflineService(mockSummary);
      final mockSyncService = MockSyncService(isSyncing: false);

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider.value(value: mockOfflineService),
              ChangeNotifierProvider.value(value: mockSyncService),
            ],
            child: const Scaffold(
              body: OfflineStatusIndicator(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show nothing when online and no pending data
      expect(find.byType(OfflineStatusIndicator), findsOneWidget);
      expect(find.text('Offline Mode'), findsNothing);
      expect(find.text('Pending Sync'), findsNothing);
    });

    testWidgets('shows pending sync indicator when there are pending sessions', (WidgetTester tester) async {
      final mockSummary = OfflineSessionSummary(
        totalSessions: 2,
        pendingSyncSessions: 2,
        failedSyncSessions: 0,
        totalOfflineVolume: 100.0,
        totalOfflineTime: const Duration(minutes: 30),
      );

      final mockOfflineService = MockOfflineService(mockSummary);
      final mockSyncService = MockSyncService(isSyncing: false);

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider.value(value: mockOfflineService),
              ChangeNotifierProvider.value(value: mockSyncService),
            ],
            child: const Scaffold(
              body: OfflineStatusIndicator(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show pending sync indicator
      expect(find.text('Pending Sync'), findsOneWidget);
      expect(find.text('2 workouts waiting to sync'), findsOneWidget);
      expect(find.byIcon(Icons.cloud_upload), findsOneWidget);
    });

    testWidgets('shows sync issues indicator when there are failed sessions', (WidgetTester tester) async {
      final mockSummary = OfflineSessionSummary(
        totalSessions: 3,
        pendingSyncSessions: 1,
        failedSyncSessions: 2,
        totalOfflineVolume: 150.0,
        totalOfflineTime: const Duration(minutes: 45),
      );

      final mockOfflineService = MockOfflineService(mockSummary);
      final mockSyncService = MockSyncService(isSyncing: false);

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider.value(value: mockOfflineService),
              ChangeNotifierProvider.value(value: mockSyncService),
            ],
            child: const Scaffold(
              body: OfflineStatusIndicator(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show sync issues indicator (failed takes priority)
      expect(find.text('Sync Issues'), findsOneWidget);
      expect(find.text('2 workouts failed to sync'), findsOneWidget);
      expect(find.byIcon(Icons.sync_problem), findsOneWidget);
    });

    testWidgets('shows syncing indicator when sync is in progress', (WidgetTester tester) async {
      final mockSummary = OfflineSessionSummary(
        totalSessions: 1,
        pendingSyncSessions: 1,
        failedSyncSessions: 0,
        totalOfflineVolume: 50.0,
        totalOfflineTime: const Duration(minutes: 15),
      );

      final mockOfflineService = MockOfflineService(mockSummary);
      final mockSyncService = MockSyncService(isSyncing: true);

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider.value(value: mockOfflineService),
              ChangeNotifierProvider.value(value: mockSyncService),
            ],
            child: const Scaffold(
              body: OfflineStatusIndicator(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show syncing indicator
      expect(find.text('Syncing Data'), findsOneWidget);
      expect(find.text('Uploading workout progress...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('shows compact indicator when compact is true', (WidgetTester tester) async {
      final mockSummary = OfflineSessionSummary(
        totalSessions: 1,
        pendingSyncSessions: 1,
        failedSyncSessions: 0,
        totalOfflineVolume: 50.0,
        totalOfflineTime: const Duration(minutes: 15),
      );

      final mockOfflineService = MockOfflineService(mockSummary);
      final mockSyncService = MockSyncService(isSyncing: false);

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider.value(value: mockOfflineService),
              ChangeNotifierProvider.value(value: mockSyncService),
            ],
            child: const Scaffold(
              body: OfflineStatusIndicator(compact: true),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show compact indicator with icon only
      expect(find.byIcon(Icons.cloud_upload), findsOneWidget);
      expect(find.text('Pending Sync'), findsNothing); // No text in compact mode
      expect(find.byType(Tooltip), findsOneWidget);
    });
  });
}