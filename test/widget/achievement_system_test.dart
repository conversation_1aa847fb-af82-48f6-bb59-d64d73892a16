import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../lib/widgets/achievement_system.dart';
import '../../lib/models/session_analytics.dart';

void main() {
  group('AchievementSystem Widget Tests', () {
    testWidgets('displays achievement header', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: AchievementSystem(
              currentStreak: 0,
              longestStreak: 0,
              consistencyScore: 0.0,
            ),
          ),
        ),
      );

      expect(find.text('Achievements'), findsOneWidget);
      expect(find.text('Track your progress and milestones'), findsOneWidget);
      expect(find.byIcon(Icons.emoji_events), findsAtLeastNWidgets(1));
    });

    testWidgets('displays streak tracker', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: AchievementSystem(
              currentStreak: 5,
              longestStreak: 10,
              consistencyScore: 75.0,
            ),
          ),
        ),
      );

      expect(find.text('Workout Streak'), findsOneWidget);
      expect(find.text('5'), findsAtLeastNWidgets(1));
      expect(find.text('10 days'), findsOneWidget);
    });

    testWidgets('displays personal records when available',
        (WidgetTester tester) async {
      final personalRecords = [
        PersonalRecord(
          exerciseId: '1',
          exerciseName: 'Bench Press',
          type: PersonalRecordType.maxWeight,
          value: 225.0,
          achievedDate: DateTime.now(),
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: SingleChildScrollView(
              child: AchievementSystem(
                currentStreak: 0,
                longestStreak: 0,
                consistencyScore: 0.0,
                recentPersonalRecords: personalRecords,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Personal Records'), findsOneWidget);
      expect(find.text('Bench Press'), findsOneWidget);
    });

    testWidgets('displays no achievements message when no achievements',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: AchievementSystem(
              currentStreak: 0,
              longestStreak: 0,
              consistencyScore: 0.0,
            ),
          ),
        ),
      );

      expect(find.text('No achievements yet'), findsOneWidget);
      expect(find.text('Keep working out to unlock achievements!'),
          findsOneWidget);
    });

    testWidgets('displays streak achievements', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: AchievementSystem(
              currentStreak: 7,
              longestStreak: 10,
              consistencyScore: 0.0,
            ),
          ),
        ),
      );

      expect(find.text('Recent Achievements'), findsOneWidget);
      expect(find.text('Getting Started'), findsOneWidget);
      expect(find.text('Week Warrior'), findsOneWidget);
      expect(find.text('3 day streak'), findsOneWidget);
      expect(find.text('7 day streak'), findsOneWidget);
    });

    testWidgets('displays consistency achievements',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: AchievementSystem(
              currentStreak: 0,
              longestStreak: 0,
              consistencyScore: 85.0,
            ),
          ),
        ),
      );

      expect(find.text('Consistent'), findsOneWidget);
      expect(find.text('70% consistency'), findsOneWidget);
    });

    testWidgets('displays workout count achievements',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: AchievementSystem(
              currentStreak: 0,
              longestStreak: 0,
              consistencyScore: 0.0,
              consistencyMetrics: {'totalWorkouts': 25},
            ),
          ),
        ),
      );

      expect(find.text('Getting Fit'), findsOneWidget);
      expect(find.text('10 workouts completed'), findsOneWidget);
    });

    testWidgets('displays personal record achievements',
        (WidgetTester tester) async {
      final personalRecords = [
        PersonalRecord(
          exerciseId: '1',
          exerciseName: 'Bench Press',
          type: PersonalRecordType.maxWeight,
          value: 225.0,
          achievedDate: DateTime.now(),
        ),
        PersonalRecord(
          exerciseId: '2',
          exerciseName: 'Squat',
          type: PersonalRecordType.maxReps,
          value: 15.0,
          achievedDate: DateTime.now(),
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: SingleChildScrollView(
              child: AchievementSystem(
                currentStreak: 0,
                longestStreak: 0,
                consistencyScore: 0.0,
                recentPersonalRecords: personalRecords,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Record Breaker'), findsOneWidget);
      expect(find.text('2 new PRs'), findsOneWidget);
    });

    testWidgets('displays achievement badges with correct styling',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: AchievementSystem(
              currentStreak: 5,
              longestStreak: 10,
              consistencyScore: 75.0,
            ),
          ),
        ),
      );

      // Check for achievement badge containers
      expect(find.byType(Container), findsAtLeastNWidgets(1));

      // Check for achievement icons
      expect(find.byIcon(Icons.trending_up), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.check_circle), findsAtLeastNWidgets(1));
    });
  });
}
