import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/widgets/smart_rest_timer.dart';
import 'package:openfitv2/design_system/design_system.dart';

void main() {
  group('SmartRestTimer Widget Tests', () {
    testWidgets('displays timer with initial duration',
        (WidgetTester tester) async {
      bool completed = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 90,
              currentExerciseName: 'Push-ups',
              nextExerciseName: 'Squats',
              currentSet: 1,
              totalSets: 3,
              onComplete: () => completed = true,
            ),
          ),
        ),
      );

      // Verify header elements
      expect(find.text('Rest Timer'), findsOneWidget);
      expect(find.text('Active'), findsOneWidget);
      expect(find.text('Set 1'), findsOneWidget);
      expect(find.text('of 3'), findsOneWidget);

      // Verify timer display shows initial time
      expect(find.text('1:30'), findsOneWidget);
      expect(find.text('remaining'), findsOneWidget);

      // Verify action buttons
      expect(find.text('Skip Rest'), findsOneWidget);
      expect(find.text('Add 30s'), findsOneWidget);

      // Verify next exercise preview
      expect(find.text('Next Exercise'), findsOneWidget);
      expect(find.text('Squats'), findsOneWidget);
    });

    testWidgets('shows motivational content when enabled',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 90,
              onComplete: () {},
              showMotivationalContent: true,
              motivationalMessages: ['Keep going!', 'You got this!'],
            ),
          ),
        ),
      );

      // Should show one of the motivational messages
      expect(
        find
            .textContaining('Keep going!')
            .or(find.textContaining('You got this!')),
        findsOneWidget,
      );
    });

    testWidgets('hides motivational content when disabled',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 90,
              onComplete: () {},
              showMotivationalContent: false,
            ),
          ),
        ),
      );

      // Should not show motivational content
      expect(find.byIcon(Icons.psychology), findsNothing);
    });

    testWidgets('handles skip rest button tap', (WidgetTester tester) async {
      bool completed = false;
      bool skipped = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 90,
              onComplete: () => completed = true,
              onSkip: () => skipped = true,
            ),
          ),
        ),
      );

      // Tap skip button
      await tester.tap(find.text('Skip Rest'));
      await tester.pumpAndSettle();

      expect(skipped, isTrue);
      expect(completed, isFalse);
    });

    testWidgets('handles add time button tap', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 90,
              onComplete: () {},
            ),
          ),
        ),
      );

      // Initial time should be 1:30
      expect(find.text('1:30'), findsOneWidget);

      // Tap add 30s button
      await tester.tap(find.text('Add 30s'));
      await tester.pump();

      // Time should now be 2:00
      expect(find.text('2:00'), findsOneWidget);
    });

    testWidgets('handles time adjustment buttons', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 90,
              onComplete: () {},
            ),
          ),
        ),
      );

      // Find time control buttons
      expect(find.text('-30s'), findsOneWidget);
      expect(find.text('-15s'), findsOneWidget);
      expect(find.text('+15s'), findsOneWidget);
      expect(find.text('+30s'), findsOneWidget);

      // Test adding 15 seconds
      await tester.tap(find.text('+15s'));
      await tester.pump();
      expect(find.text('1:45'), findsOneWidget);

      // Test subtracting 15 seconds
      await tester.tap(find.text('-15s'));
      await tester.pump();
      expect(find.text('1:30'), findsOneWidget);
    });

    testWidgets('handles pause/resume functionality',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 90,
              onComplete: () {},
            ),
          ),
        ),
      );

      // Find pause button (should show pause icon initially)
      final pauseButton = find.byIcon(Icons.pause);
      expect(pauseButton, findsOneWidget);

      // Tap pause button
      await tester.tap(pauseButton);
      await tester.pump();

      // Should now show play icon and paused state
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
      expect(find.text('Paused'), findsOneWidget);

      // Tap play button to resume
      await tester.tap(find.byIcon(Icons.play_arrow));
      await tester.pump();

      // Should be back to active state
      expect(find.text('Active'), findsOneWidget);
      expect(find.byIcon(Icons.pause), findsOneWidget);
    });

    testWidgets('shows next exercise preview when provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 90,
              nextExerciseName: 'Bench Press',
              onComplete: () {},
            ),
          ),
        ),
      );

      expect(find.text('Next Exercise'), findsOneWidget);
      expect(find.text('Bench Press'), findsOneWidget);
      expect(find.byIcon(Icons.fitness_center), findsOneWidget);
    });

    testWidgets('hides next exercise preview when not provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 90,
              onComplete: () {},
            ),
          ),
        ),
      );

      expect(find.text('Next Exercise'), findsNothing);
    });

    testWidgets('formats time correctly for different durations',
        (WidgetTester tester) async {
      final testCases = [
        (30, '0:30'),
        (60, '1:00'),
        (90, '1:30'),
        (120, '2:00'),
        (150, '2:30'),
      ];

      for (final (seconds, expected) in testCases) {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              extensions: [
                AppColorsTheme.light(),
              ],
            ),
            home: Scaffold(
              body: SmartRestTimer(
                initialDurationSeconds: seconds,
                onComplete: () {},
              ),
            ),
          ),
        );

        expect(find.text(expected), findsOneWidget);
      }
    });

    testWidgets('prevents negative time adjustment',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 15, // Start with 15 seconds
              onComplete: () {},
            ),
          ),
        ),
      );

      expect(find.text('0:15'), findsOneWidget);

      // Try to subtract 30 seconds (should clamp to 0)
      await tester.tap(find.text('-30s'));
      await tester.pump();

      expect(find.text('0:00'), findsOneWidget);
    });

    testWidgets('limits maximum time adjustment', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 270, // Start with 4:30 (close to max)
              onComplete: () {},
            ),
          ),
        ),
      );

      expect(find.text('4:30'), findsOneWidget);

      // Try to add 30 seconds (should clamp to 5:00 max)
      await tester.tap(find.text('+30s'));
      await tester.pump();

      expect(find.text('5:00'), findsOneWidget);

      // Try to add more (should stay at 5:00)
      await tester.tap(find.text('+30s'));
      await tester.pump();

      expect(find.text('5:00'), findsOneWidget);
    });

    testWidgets('shows correct accessibility semantics',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            extensions: [
              AppColorsTheme.light(),
            ],
          ),
          home: Scaffold(
            body: SmartRestTimer(
              initialDurationSeconds: 90,
              onComplete: () {},
            ),
          ),
        ),
      );

      // Verify semantic labels are present
      expect(find.byType(Semantics), findsWidgets);
    });
  });
}
