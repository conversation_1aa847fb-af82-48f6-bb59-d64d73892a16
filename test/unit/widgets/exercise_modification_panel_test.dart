import 'package:flutter_test/flutter_test.dart';
import '../../../lib/widgets/exercise_modification_panel.dart';
import '../../../lib/models/exercise.dart';

void main() {
  group('ExerciseModification Data Class Tests', () {
    test('creates weight adjustment modification correctly', () {
      final modification = ExerciseModification.weightAdjustment(
        exerciseId: 'test_exercise',
        newWeight: 150.0,
        newReps: 8,
        reason: 'Test adjustment',
      );

      expect(modification.type, ExerciseModificationType.weightAdjustment);
      expect(modification.exerciseId, 'test_exercise');
      expect(modification.reason, 'Test adjustment');
      expect(modification.newWeight, 150.0);
      expect(modification.newReps, 8);
      expect(modification.newExercise, isNull);
    });

    test('creates exercise substitution modification correctly', () {
      final newExercise = Exercise(
        id: 'new_exercise',
        name: 'Push-ups',
        primaryMuscle: 'Chest',
      );

      final modification = ExerciseModification.exerciseSubstitution(
        originalExerciseId: 'old_exercise',
        newExercise: newExercise,
        reason: 'Equipment not available',
      );

      expect(modification.type, ExerciseModificationType.exerciseSubstitution);
      expect(modification.exerciseId, 'old_exercise');
      expect(modification.reason, 'Equipment not available');
      expect(modification.newExercise, newExercise);
      expect(modification.newWeight, isNull);
      expect(modification.newReps, isNull);
    });

    test('creates exercise skip modification correctly', () {
      final modification = ExerciseModification.exerciseSkip(
        exerciseId: 'test_exercise',
        reason: 'Injury',
      );

      expect(modification.type, ExerciseModificationType.exerciseSkip);
      expect(modification.exerciseId, 'test_exercise');
      expect(modification.reason, 'Injury');
      expect(modification.newWeight, isNull);
      expect(modification.newReps, isNull);
      expect(modification.newExercise, isNull);
    });
  });

  group('WeightAdjustmentSuggestion Tests', () {
    test('creates weight adjustment suggestion correctly', () {
      final suggestion = WeightAdjustmentSuggestion(
        suggestedWeight: 140.0,
        adjustmentType: WeightAdjustmentType.increase,
        reasoning: 'Previous sets were too easy',
        confidence: 0.8,
      );

      expect(suggestion.suggestedWeight, 140.0);
      expect(suggestion.adjustmentType, WeightAdjustmentType.increase);
      expect(suggestion.reasoning, 'Previous sets were too easy');
      expect(suggestion.confidence, 0.8);
    });

    test('weight adjustment type enum values are correct', () {
      expect(WeightAdjustmentType.values.length, 3);
      expect(
          WeightAdjustmentType.values, contains(WeightAdjustmentType.increase));
      expect(
          WeightAdjustmentType.values, contains(WeightAdjustmentType.decrease));
      expect(
          WeightAdjustmentType.values, contains(WeightAdjustmentType.maintain));
    });
  });

  group('ExerciseModificationType Tests', () {
    test('exercise modification type enum values are correct', () {
      expect(ExerciseModificationType.values.length, 3);
      expect(ExerciseModificationType.values,
          contains(ExerciseModificationType.weightAdjustment));
      expect(ExerciseModificationType.values,
          contains(ExerciseModificationType.exerciseSubstitution));
      expect(ExerciseModificationType.values,
          contains(ExerciseModificationType.exerciseSkip));
    });
  });
}
