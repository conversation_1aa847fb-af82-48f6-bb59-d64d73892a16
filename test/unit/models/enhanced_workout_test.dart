import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/models/enhanced_workout.dart';
import 'package:openfitv2/models/workout.dart';
import 'package:openfitv2/models/exercise.dart';

void main() {
  group('WorkoutDifficulty', () {
    test('should return correct labels', () {
      expect(WorkoutDifficulty.beginner.label, 'Beginner');
      expect(WorkoutDifficulty.intermediate.label, 'Intermediate');
      expect(WorkoutDifficulty.advanced.label, 'Advanced');
      expect(WorkoutDifficulty.expert.label, 'Expert');
    });

    test('should parse from string correctly', () {
      expect(
          WorkoutDifficulty.fromString('beginner'), WorkoutDifficulty.beginner);
      expect(WorkoutDifficulty.fromString('INTERMEDIATE'),
          WorkoutDifficulty.intermediate);
      expect(
          WorkoutDifficulty.fromString('advanced'), WorkoutDifficulty.advanced);
      expect(WorkoutDifficulty.fromString('expert'), WorkoutDifficulty.expert);
      expect(
          WorkoutDifficulty.fromString('invalid'), WorkoutDifficulty.beginner);
      expect(WorkoutDifficulty.fromString(null), WorkoutDifficulty.beginner);
    });
  });

  group('EnhancedWorkout', () {
    late Workout baseWorkout;
    late List<WorkoutExercise> exercises;

    setUp(() {
      exercises = [
        WorkoutExercise(
          id: 'we1',
          workoutId: 'w1',
          exerciseId: 'e1',
          exercise: Exercise(
            id: 'e1',
            name: 'Push-ups',
            primaryMuscle: 'Chest',
            equipment: 'None',
          ),
          sets: 3,
          reps: [10, 10, 10],
          orderIndex: 0,
          name: 'Push-ups',
        ),
        WorkoutExercise(
          id: 'we2',
          workoutId: 'w1',
          exerciseId: 'e2',
          exercise: Exercise(
            id: 'e2',
            name: 'Squats',
            primaryMuscle: 'Legs',
            equipment: 'Barbell',
          ),
          sets: 4,
          reps: [12, 12, 10, 8],
          weight: [100, 110, 120, 130],
          orderIndex: 1,
          name: 'Squats',
        ),
      ];

      baseWorkout = Workout(
        id: 'w1',
        userId: 'u1',
        name: 'Test Workout',
        createdAt: DateTime(2024, 1, 1),
        exercises: exercises,
      );
    });

    test('should create from base workout with defaults', () {
      final enhanced = EnhancedWorkout.fromWorkout(baseWorkout);

      expect(enhanced.id, 'w1');
      expect(enhanced.name, 'Test Workout');
      expect(enhanced.difficulty, WorkoutDifficulty.beginner);
      expect(enhanced.targetMuscleGroups, ['Chest', 'Legs']);
      expect(enhanced.requiredEquipment, ['Barbell']);
      expect(enhanced.estimatedCalories, greaterThan(0));
      expect(enhanced.estimatedDuration, greaterThan(0));
    });

    test('should create from base workout with custom values', () {
      final enhanced = EnhancedWorkout.fromWorkout(
        baseWorkout,
        difficulty: WorkoutDifficulty.advanced,
        estimatedCalories: 300,
        estimatedDuration: 45,
        userRating: 4.5,
        completionCount: 5,
      );

      expect(enhanced.difficulty, WorkoutDifficulty.advanced);
      expect(enhanced.estimatedCalories, 300);
      expect(enhanced.estimatedDuration, 45);
      expect(enhanced.userRating, 4.5);
      expect(enhanced.completionCount, 5);
    });

    test('should serialize to and from JSON correctly', () {
      final progress = WorkoutProgress(
        workoutId: 'w1',
        totalCompletions: 3,
        averageRating: 4.0,
        totalSets: 21,
        totalReps: 93,
        totalVolume: 2100.0,
        firstCompleted: DateTime(2024, 1, 1),
        lastCompleted: DateTime(2024, 1, 15),
      );

      final enhanced = EnhancedWorkout.fromWorkout(
        baseWorkout,
        difficulty: WorkoutDifficulty.intermediate,
        estimatedCalories: 250,
        estimatedDuration: 40,
        userRating: 4.2,
        completionCount: 3,
        lastCompleted: DateTime(2024, 1, 15),
        progress: progress,
      );

      final json = enhanced.toJson();
      final restored = EnhancedWorkout.fromJson(json);

      expect(restored.id, enhanced.id);
      expect(restored.difficulty, enhanced.difficulty);
      expect(restored.estimatedCalories, enhanced.estimatedCalories);
      expect(restored.estimatedDuration, enhanced.estimatedDuration);
      expect(restored.userRating, enhanced.userRating);
      expect(restored.completionCount, enhanced.completionCount);
      expect(restored.lastCompleted, enhanced.lastCompleted);
      expect(restored.progress?.workoutId, enhanced.progress?.workoutId);
    });

    test('should calculate recommendation status correctly', () {
      // No progress data
      final noProgress = EnhancedWorkout.fromWorkout(baseWorkout);
      expect(noProgress.isRecommended, false);

      // High average rating
      final highRating = EnhancedWorkout.fromWorkout(
        baseWorkout,
        progress: WorkoutProgress(
          workoutId: 'w1',
          totalCompletions: 3,
          averageRating: 3.5,
          totalSets: 21,
          totalReps: 93,
          totalVolume: 2100.0,
          firstCompleted: DateTime(2024, 1, 1),
          lastCompleted: DateTime(2024, 1, 15),
        ),
      );
      expect(highRating.isRecommended, true);

      // Good user rating with completions
      final goodUserRating = EnhancedWorkout.fromWorkout(
        baseWorkout,
        userRating: 4.0,
        completionCount: 2,
      );
      expect(goodUserRating.isRecommended, true);
    });

    test('should calculate progress percentage correctly', () {
      // No progress
      final noProgress = EnhancedWorkout.fromWorkout(baseWorkout);
      expect(noProgress.progressPercentage, 0.0);

      // With progress
      final withProgress = EnhancedWorkout.fromWorkout(
        baseWorkout,
        progress: WorkoutProgress(
          workoutId: 'w1',
          totalCompletions: 5,
          averageRating: 4.0,
          totalSets: 35,
          totalReps: 155,
          totalVolume: 3500.0,
          firstCompleted: DateTime(2024, 1, 1),
          lastCompleted: DateTime(2024, 1, 15),
        ),
      );
      expect(withProgress.progressPercentage, greaterThan(0.0));
      expect(withProgress.progressPercentage, lessThanOrEqualTo(1.0));
    });

    test('should format duration and calories correctly', () {
      final enhanced = EnhancedWorkout.fromWorkout(
        baseWorkout,
        estimatedDuration: 75, // 1 hour 15 minutes
        estimatedCalories: 300,
      );

      expect(enhanced.formattedEstimatedDuration, '1h 15m');
      expect(enhanced.formattedCalories, '300 cal');

      final shortDuration = EnhancedWorkout.fromWorkout(
        baseWorkout,
        estimatedDuration: 30,
        estimatedCalories: 0,
      );

      expect(shortDuration.formattedEstimatedDuration, '30m');
      expect(shortDuration.formattedCalories, 'Unknown');
    });

    test('should return unique exercises correctly', () {
      final enhanced = EnhancedWorkout.fromWorkout(baseWorkout);
      final uniqueExercises = enhanced.uniqueExercises;

      expect(uniqueExercises.length, 2);
      expect(uniqueExercises.map((e) => e.name), contains('Push-ups'));
      expect(uniqueExercises.map((e) => e.name), contains('Squats'));
    });

    test('should calculate estimated values correctly', () {
      // Test private static methods through public interface
      final enhanced = EnhancedWorkout.fromWorkout(baseWorkout);

      expect(enhanced.estimatedCalories, greaterThan(0));
      expect(enhanced.estimatedDuration, greaterThan(0));

      // Should be based on number of sets and exercises
      final totalSets = exercises.fold<int>(0, (sum, e) => sum + e.sets);
      expect(enhanced.estimatedDuration, totalSets * 3); // 3 minutes per set
    });
  });

  group('WorkoutProgress', () {
    test('should serialize to and from JSON correctly', () {
      final exerciseProgress = [
        ExerciseProgress(
          exerciseId: 'e1',
          exerciseName: 'Push-ups',
          totalSets: 9,
          totalReps: 30,
          totalVolume: 0.0,
          maxWeight: 0.0,
          maxReps: 12,
          firstPerformed: DateTime(2024, 1, 1),
          lastPerformed: DateTime(2024, 1, 15),
        ),
      ];

      final progress = WorkoutProgress(
        workoutId: 'w1',
        totalCompletions: 3,
        averageRating: 4.2,
        totalSets: 21,
        totalReps: 93,
        totalVolume: 2100.0,
        firstCompleted: DateTime(2024, 1, 1),
        lastCompleted: DateTime(2024, 1, 15),
        exerciseProgress: exerciseProgress,
        personalRecords: {'max_weight_e1': 150.0},
      );

      final json = progress.toJson();
      final restored = WorkoutProgress.fromJson(json);

      expect(restored.workoutId, progress.workoutId);
      expect(restored.totalCompletions, progress.totalCompletions);
      expect(restored.averageRating, progress.averageRating);
      expect(restored.exerciseProgress.length, 1);
      expect(restored.personalRecords['max_weight_e1'], 150.0);
    });

    test('should calculate averages correctly', () {
      final progress = WorkoutProgress(
        workoutId: 'w1',
        totalCompletions: 4,
        averageRating: 4.0,
        totalSets: 28,
        totalReps: 124,
        totalVolume: 2800.0,
        firstCompleted: DateTime(2024, 1, 1),
        lastCompleted: DateTime(2024, 1, 15),
      );

      expect(progress.averageVolume, 700.0); // 2800 / 4
      expect(progress.averageSetsPerSession, 7.0); // 28 / 4
      expect(progress.averageRepsPerSession, 31.0); // 124 / 4
    });

    test('should calculate time spent correctly', () {
      final progress = WorkoutProgress(
        workoutId: 'w1',
        totalCompletions: 3,
        averageRating: 4.0,
        totalSets: 21,
        totalReps: 93,
        totalVolume: 2100.0,
        firstCompleted: DateTime(2024, 1, 1),
        lastCompleted: DateTime(2024, 1, 15),
      );

      expect(progress.totalTimeSpent.inDays, 14);
    });

    test('should detect improvement correctly', () {
      final improvingProgress = ExerciseProgress(
        exerciseId: 'e1',
        exerciseName: 'Squats',
        totalSets: 12,
        totalReps: 48,
        totalVolume: 4800.0,
        maxWeight: 150.0,
        maxReps: 12,
        firstPerformed: DateTime(2024, 1, 1),
        lastPerformed: DateTime(2024, 1, 15),
        records: [
          PerformanceRecord(
            date: DateTime(2024, 1, 1),
            weight: 100.0,
            reps: 10,
            volume: 1000.0,
          ),
          PerformanceRecord(
            date: DateTime(2024, 1, 15),
            weight: 120.0,
            reps: 12,
            volume: 1440.0,
          ),
        ],
      );

      final progress = WorkoutProgress(
        workoutId: 'w1',
        totalCompletions: 2,
        averageRating: 4.0,
        totalSets: 12,
        totalReps: 48,
        totalVolume: 4800.0,
        firstCompleted: DateTime(2024, 1, 1),
        lastCompleted: DateTime(2024, 1, 15),
        exerciseProgress: [improvingProgress],
      );

      expect(progress.hasImprovement, true);
    });
  });

  group('ExerciseProgress', () {
    test('should detect improvement correctly', () {
      final records = [
        PerformanceRecord(
          date: DateTime(2024, 1, 1),
          weight: 100.0,
          reps: 10,
          volume: 1000.0,
        ),
        PerformanceRecord(
          date: DateTime(2024, 1, 8),
          weight: 110.0,
          reps: 10,
          volume: 1100.0,
        ),
        PerformanceRecord(
          date: DateTime(2024, 1, 15),
          weight: 120.0,
          reps: 12,
          volume: 1440.0,
        ),
      ];

      final progress = ExerciseProgress(
        exerciseId: 'e1',
        exerciseName: 'Squats',
        totalSets: 9,
        totalReps: 32,
        totalVolume: 3540.0,
        maxWeight: 120.0,
        maxReps: 12,
        firstPerformed: DateTime(2024, 1, 1),
        lastPerformed: DateTime(2024, 1, 15),
        records: records,
      );

      expect(progress.hasImprovement, true);
      expect(
          progress.improvementPercentage, 44.0); // (1440 - 1000) / 1000 * 100
    });

    test('should handle no improvement correctly', () {
      final records = [
        PerformanceRecord(
          date: DateTime(2024, 1, 1),
          weight: 100.0,
          reps: 10,
          volume: 1000.0,
        ),
        PerformanceRecord(
          date: DateTime(2024, 1, 15),
          weight: 90.0,
          reps: 10,
          volume: 900.0,
        ),
      ];

      final progress = ExerciseProgress(
        exerciseId: 'e1',
        exerciseName: 'Squats',
        totalSets: 6,
        totalReps: 20,
        totalVolume: 1900.0,
        maxWeight: 100.0,
        maxReps: 10,
        firstPerformed: DateTime(2024, 1, 1),
        lastPerformed: DateTime(2024, 1, 15),
        records: records,
      );

      expect(progress.hasImprovement, false);
      expect(
          progress.improvementPercentage, -10.0); // (900 - 1000) / 1000 * 100
    });

    test('should handle single record correctly', () {
      final progress = ExerciseProgress(
        exerciseId: 'e1',
        exerciseName: 'Push-ups',
        totalSets: 3,
        totalReps: 30,
        totalVolume: 0.0,
        maxWeight: 0.0,
        maxReps: 12,
        firstPerformed: DateTime(2024, 1, 1),
        lastPerformed: DateTime(2024, 1, 1),
        records: [
          PerformanceRecord(
            date: DateTime(2024, 1, 1),
            weight: 0.0,
            reps: 10,
            volume: 0.0,
          ),
        ],
      );

      expect(progress.hasImprovement, false);
      expect(progress.improvementPercentage, 0.0);
    });
  });

  group('PerformanceRecord', () {
    test('should serialize to and from JSON correctly', () {
      final record = PerformanceRecord(
        date: DateTime(2024, 1, 15),
        weight: 120.0,
        reps: 12,
        volume: 1440.0,
        difficulty: 4,
      );

      final json = record.toJson();
      final restored = PerformanceRecord.fromJson(json);

      expect(restored.date, record.date);
      expect(restored.weight, record.weight);
      expect(restored.reps, record.reps);
      expect(restored.volume, record.volume);
      expect(restored.difficulty, record.difficulty);
    });
  });
}
