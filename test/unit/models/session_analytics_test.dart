import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/models/session_analytics.dart';

void main() {
  group('SessionAnalytics', () {
    late SessionAnalytics sessionAnalytics;
    late Map<String, ExercisePerformance> exercisePerformance;

    setUp(() {
      exercisePerformance = {
        'e1': ExercisePerformance(
          exerciseId: 'e1',
          exerciseName: 'Push-ups',
          setsCompleted: 3,
          totalReps: 30,
          totalVolume: 0.0,
          maxWeight: 0.0,
          maxReps: 12,
          muscleGroups: ['Chest', 'Triceps'],
          timeSpent: const Duration(minutes: 10),
          setDetails: [
            SetPerformance(
              setNumber: 1,
              weight: 0.0,
              reps: 10,
              volume: 0.0,
              restTime: const Duration(minutes: 1),
            ),
            SetPerformance(
              setNumber: 2,
              weight: 0.0,
              reps: 10,
              volume: 0.0,
              restTime: const Duration(minutes: 1),
            ),
            SetPerformance(
              setNumber: 3,
              weight: 0.0,
              reps: 10,
              volume: 0.0,
              restTime: const Duration(minutes: 1),
            ),
          ],
        ),
        'e2': ExercisePerformance(
          exerciseId: 'e2',
          exerciseName: 'Squats',
          setsCompleted: 4,
          totalReps: 40,
          totalVolume: 4000.0,
          maxWeight: 120.0,
          maxReps: 12,
          muscleGroups: ['Legs', 'Glutes'],
          timeSpent: const Duration(minutes: 15),
        ),
      };

      sessionAnalytics = SessionAnalytics(
        sessionId: 's1',
        totalDuration: const Duration(minutes: 45),
        setsCompleted: 7,
        totalReps: 70,
        totalVolume: 4000.0,
        estimatedCalories: 300,
        exercisePerformance: exercisePerformance,
        newRecords: [
          PersonalRecord(
            exerciseId: 'e2',
            exerciseName: 'Squats',
            type: PersonalRecordType.maxWeight,
            value: 120.0,
            achievedDate: DateTime(2024, 1, 15),
            previousValue: 100.0,
          ),
        ],
        improvementScore: 85.0,
        sessionDate: DateTime(2024, 1, 15),
        overallRating: 4,
      );
    });

    test('should serialize to and from JSON correctly', () {
      final json = sessionAnalytics.toJson();
      final restored = SessionAnalytics.fromJson(json);

      expect(restored.sessionId, sessionAnalytics.sessionId);
      expect(restored.totalDuration, sessionAnalytics.totalDuration);
      expect(restored.setsCompleted, sessionAnalytics.setsCompleted);
      expect(restored.totalReps, sessionAnalytics.totalReps);
      expect(restored.totalVolume, sessionAnalytics.totalVolume);
      expect(restored.estimatedCalories, sessionAnalytics.estimatedCalories);
      expect(restored.improvementScore, sessionAnalytics.improvementScore);
      expect(restored.exercisePerformance.length, 2);
      expect(restored.newRecords.length, 1);
    });

    test('should format duration correctly', () {
      expect(sessionAnalytics.formattedDuration, '45m 0s');

      final longSession = SessionAnalytics(
        sessionId: 's2',
        totalDuration: const Duration(hours: 1, minutes: 30, seconds: 45),
        setsCompleted: 10,
        totalReps: 100,
        totalVolume: 5000.0,
        estimatedCalories: 400,
        improvementScore: 90.0,
        sessionDate: DateTime(2024, 1, 15),
      );

      expect(longSession.formattedDuration, '1h 30m 45s');

      final shortSession = SessionAnalytics(
        sessionId: 's3',
        totalDuration: const Duration(seconds: 30),
        setsCompleted: 1,
        totalReps: 10,
        totalVolume: 100.0,
        estimatedCalories: 50,
        improvementScore: 70.0,
        sessionDate: DateTime(2024, 1, 15),
      );

      expect(shortSession.formattedDuration, '30s');
    });

    test('should calculate averages correctly', () {
      expect(sessionAnalytics.averageVolumePerSet,
          closeTo(571.43, 0.01)); // 4000 / 7
      expect(sessionAnalytics.averageRepsPerSet, 10.0); // 70 / 7
      expect(
          sessionAnalytics.caloriesPerMinute, closeTo(6.67, 0.01)); // 300 / 45
    });

    test('should identify muscle groups worked', () {
      final muscleGroups = sessionAnalytics.muscleGroupsWorked;
      expect(muscleGroups, contains('Chest'));
      expect(muscleGroups, contains('Triceps'));
      expect(muscleGroups, contains('Legs'));
      expect(muscleGroups, contains('Glutes'));
      expect(muscleGroups.length, 4);
    });

    test('should assign performance grade correctly', () {
      expect(sessionAnalytics.performanceGrade, 'A'); // 85.0

      final aPlus = SessionAnalytics(
        sessionId: 's2',
        totalDuration: const Duration(minutes: 30),
        setsCompleted: 5,
        totalReps: 50,
        totalVolume: 2500.0,
        estimatedCalories: 200,
        improvementScore: 95.0,
        sessionDate: DateTime(2024, 1, 15),
      );
      expect(aPlus.performanceGrade, 'A+');

      final d = SessionAnalytics(
        sessionId: 's3',
        totalDuration: const Duration(minutes: 20),
        setsCompleted: 3,
        totalReps: 30,
        totalVolume: 1000.0,
        estimatedCalories: 100,
        improvementScore: 50.0,
        sessionDate: DateTime(2024, 1, 15),
      );
      expect(d.performanceGrade, 'D');
    });

    test('should compare with previous session correctly', () {
      final previousSession = SessionAnalytics(
        sessionId: 's0',
        totalDuration: const Duration(minutes: 40),
        setsCompleted: 6,
        totalReps: 60,
        totalVolume: 3500.0,
        estimatedCalories: 250,
        improvementScore: 80.0,
        sessionDate: DateTime(2024, 1, 10),
      );

      final comparison = sessionAnalytics.compareWith(previousSession);

      expect(comparison.volumeChange, 500.0); // 4000 - 3500
      expect(comparison.durationChange, const Duration(minutes: 5)); // 45 - 40
      expect(comparison.setsChange, 1); // 7 - 6
      expect(comparison.repsChange, 10); // 70 - 60
      expect(comparison.improvementChange, 5.0); // 85 - 80
      expect(comparison.isImprovement, true);
    });

    test('should handle comparison with null previous session', () {
      final comparison = sessionAnalytics.compareWith(null);

      expect(comparison.volumeChange, 0.0);
      expect(comparison.durationChange, Duration.zero);
      expect(comparison.setsChange, 0);
      expect(comparison.repsChange, 0);
      expect(comparison.improvementChange, 0.0);
      expect(comparison.isImprovement, false);
    });

    test('should detect new records correctly', () {
      expect(sessionAnalytics.hasNewRecords, true);
      expect(sessionAnalytics.newRecords.length, 1);
      expect(
          sessionAnalytics.newRecords.first.type, PersonalRecordType.maxWeight);
    });

    test('should count exercises correctly', () {
      expect(sessionAnalytics.exerciseCount, 2);
    });
  });

  group('ExercisePerformance', () {
    test('should serialize to and from JSON correctly', () {
      final performance = ExercisePerformance(
        exerciseId: 'e1',
        exerciseName: 'Bench Press',
        setsCompleted: 4,
        totalReps: 32,
        totalVolume: 3200.0,
        maxWeight: 120.0,
        maxReps: 10,
        muscleGroups: ['Chest', 'Triceps'],
        timeSpent: const Duration(minutes: 12),
        setDetails: [
          SetPerformance(
            setNumber: 1,
            weight: 100.0,
            reps: 10,
            volume: 1000.0,
            restTime: const Duration(minutes: 2),
            difficultyRating: 3,
          ),
        ],
        averageDifficulty: 3.5,
      );

      final json = performance.toJson();
      final restored = ExercisePerformance.fromJson(json);

      expect(restored.exerciseId, performance.exerciseId);
      expect(restored.exerciseName, performance.exerciseName);
      expect(restored.setsCompleted, performance.setsCompleted);
      expect(restored.totalVolume, performance.totalVolume);
      expect(restored.averageDifficulty, performance.averageDifficulty);
      expect(restored.setDetails.length, 1);
    });

    test('should calculate averages correctly', () {
      final performance = ExercisePerformance(
        exerciseId: 'e1',
        exerciseName: 'Squats',
        setsCompleted: 4,
        totalReps: 40,
        totalVolume: 4000.0,
        maxWeight: 120.0,
        maxReps: 12,
        timeSpent: const Duration(minutes: 15),
        setDetails: [
          SetPerformance(
            setNumber: 1,
            weight: 100.0,
            reps: 10,
            volume: 1000.0,
            restTime: const Duration(minutes: 2),
          ),
          SetPerformance(
            setNumber: 2,
            weight: 110.0,
            reps: 10,
            volume: 1100.0,
            restTime: const Duration(minutes: 2),
          ),
          SetPerformance(
            setNumber: 3,
            weight: 120.0,
            reps: 10,
            volume: 1200.0,
            restTime: const Duration(minutes: 2),
          ),
          SetPerformance(
            setNumber: 4,
            weight: 120.0,
            reps: 10,
            volume: 1200.0,
            restTime: const Duration(minutes: 2),
          ),
        ],
      );

      expect(performance.averageVolumePerSet, 1000.0); // 4000 / 4
      expect(performance.averageRepsPerSet, 10.0); // 40 / 4
      expect(performance.averageWeightPerSet, 112.5); // (100+110+120+120) / 4
    });

    test('should format time spent correctly', () {
      final performance = ExercisePerformance(
        exerciseId: 'e1',
        exerciseName: 'Push-ups',
        setsCompleted: 3,
        totalReps: 30,
        totalVolume: 0.0,
        maxWeight: 0.0,
        maxReps: 12,
        timeSpent: const Duration(minutes: 8, seconds: 30),
      );

      expect(performance.formattedTimeSpent, '8m 30s');
    });
  });

  group('SetPerformance', () {
    test('should serialize to and from JSON correctly', () {
      final setPerformance = SetPerformance(
        setNumber: 1,
        weight: 100.0,
        reps: 10,
        volume: 1000.0,
        restTime: const Duration(minutes: 2),
        difficultyRating: 4,
        notes: 'Good form',
      );

      final json = setPerformance.toJson();
      final restored = SetPerformance.fromJson(json);

      expect(restored.setNumber, setPerformance.setNumber);
      expect(restored.weight, setPerformance.weight);
      expect(restored.reps, setPerformance.reps);
      expect(restored.volume, setPerformance.volume);
      expect(restored.restTime, setPerformance.restTime);
      expect(restored.difficultyRating, setPerformance.difficultyRating);
      expect(restored.notes, setPerformance.notes);
    });
  });

  group('PersonalRecord', () {
    test('should serialize to and from JSON correctly', () {
      final record = PersonalRecord(
        exerciseId: 'e1',
        exerciseName: 'Bench Press',
        type: PersonalRecordType.maxWeight,
        value: 150.0,
        achievedDate: DateTime(2024, 1, 15),
        previousValue: 140.0,
        notes: 'New PR!',
      );

      final json = record.toJson();
      final restored = PersonalRecord.fromJson(json);

      expect(restored.exerciseId, record.exerciseId);
      expect(restored.exerciseName, record.exerciseName);
      expect(restored.type, record.type);
      expect(restored.value, record.value);
      expect(restored.achievedDate, record.achievedDate);
      expect(restored.previousValue, record.previousValue);
      expect(restored.notes, record.notes);
    });

    test('should calculate improvement percentage correctly', () {
      final record = PersonalRecord(
        exerciseId: 'e1',
        exerciseName: 'Squats',
        type: PersonalRecordType.maxWeight,
        value: 150.0,
        achievedDate: DateTime(2024, 1, 15),
        previousValue: 120.0,
      );

      expect(record.improvementPercentage, 25.0); // (150 - 120) / 120 * 100
    });

    test('should handle zero previous value', () {
      final record = PersonalRecord(
        exerciseId: 'e1',
        exerciseName: 'Push-ups',
        type: PersonalRecordType.maxReps,
        value: 20.0,
        achievedDate: DateTime(2024, 1, 15),
        previousValue: 0.0,
      );

      expect(record.improvementPercentage, 0.0);
    });

    test('should format values correctly for different types', () {
      final weightRecord = PersonalRecord(
        exerciseId: 'e1',
        exerciseName: 'Bench Press',
        type: PersonalRecordType.maxWeight,
        value: 150.5,
        achievedDate: DateTime(2024, 1, 15),
      );
      expect(weightRecord.formattedValue, '150.5 lbs');

      final repsRecord = PersonalRecord(
        exerciseId: 'e2',
        exerciseName: 'Push-ups',
        type: PersonalRecordType.maxReps,
        value: 25.0,
        achievedDate: DateTime(2024, 1, 15),
      );
      expect(repsRecord.formattedValue, '25 reps');

      final timeRecord = PersonalRecord(
        exerciseId: 'e3',
        exerciseName: 'Plank',
        type: PersonalRecordType.bestTime,
        value: 90.0, // 1 minute 30 seconds
        achievedDate: DateTime(2024, 1, 15),
      );
      expect(timeRecord.formattedValue, '1:30');
    });

    test('should provide correct descriptions', () {
      final record = PersonalRecord(
        exerciseId: 'e1',
        exerciseName: 'Deadlift',
        type: PersonalRecordType.maxWeight,
        value: 200.0,
        achievedDate: DateTime(2024, 1, 15),
      );

      expect(record.description, 'New max weight for Deadlift');
    });
  });

  group('PersonalRecordType', () {
    test('should parse from string correctly', () {
      expect(PersonalRecordType.fromString('max_weight'),
          PersonalRecordType.maxWeight);
      expect(PersonalRecordType.fromString('max_reps'),
          PersonalRecordType.maxReps);
      expect(PersonalRecordType.fromString('max_volume'),
          PersonalRecordType.maxVolume);
      expect(PersonalRecordType.fromString('best_time'),
          PersonalRecordType.bestTime);
      expect(PersonalRecordType.fromString('invalid'),
          PersonalRecordType.maxWeight);
      expect(PersonalRecordType.fromString(null), PersonalRecordType.maxWeight);
    });
  });

  group('SessionComparison', () {
    test('should format changes correctly', () {
      final comparison = SessionComparison(
        volumeChange: 250.5,
        durationChange: const Duration(minutes: 5, seconds: 30),
        setsChange: 2,
        repsChange: -5,
        improvementChange: 10.5,
        isImprovement: true,
      );

      expect(comparison.volumeChangeFormatted, '+250.5 lbs');
      expect(comparison.durationChangeFormatted, '+5m 30s');
      expect(comparison.setsChangeFormatted, '+2 sets');
      expect(comparison.repsChangeFormatted, '-5 reps');
      expect(comparison.improvementChangeFormatted, '+10.5%');
    });

    test('should handle negative changes correctly', () {
      final comparison = SessionComparison(
        volumeChange: -100.0,
        durationChange: const Duration(minutes: -3),
        setsChange: -1,
        repsChange: -10,
        improvementChange: -5.0,
        isImprovement: false,
      );

      expect(comparison.volumeChangeFormatted, '-100.0 lbs');
      expect(comparison.durationChangeFormatted, '-3m 0s');
      expect(comparison.setsChangeFormatted, '-1 sets');
      expect(comparison.repsChangeFormatted, '-10 reps');
      expect(comparison.improvementChangeFormatted, '-5.0%');
    });
  });
}
