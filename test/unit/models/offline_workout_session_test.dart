import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/models/offline_workout_session.dart';

void main() {
  group('SyncStatus', () {
    test('should return correct labels', () {
      expect(SyncStatus.pending.label, 'Pending');
      expect(SyncStatus.syncing.label, 'Syncing');
      expect(SyncStatus.synced.label, 'Synced');
      expect(SyncStatus.failed.label, 'Failed');
    });

    test('should parse from string correctly', () {
      expect(SyncStatus.fromString('pending'), SyncStatus.pending);
      expect(SyncStatus.fromString('SYNCING'), SyncStatus.syncing);
      expect(SyncStatus.fromString('synced'), SyncStatus.synced);
      expect(SyncStatus.fromString('failed'), SyncStatus.failed);
      expect(SyncStatus.fromString('invalid'), SyncStatus.pending);
      expect(SyncStatus.fromString(null), SyncStatus.pending);
    });
  });

  group('OfflineWorkoutSession', () {
    late OfflineWorkoutSession session;
    late List<OfflineSetData> completedSets;

    setUp(() {
      completedSets = [
        OfflineSetData(
          setId: 'set1',
          exerciseId: 'e1',
          exerciseName: 'Push-ups',
          setNumber: 1,
          weight: 0.0,
          reps: 10,
          completedAt: DateTime(2024, 1, 15, 10, 0),
          restTime: const Duration(minutes: 1),
          difficultyRating: 3,
        ),
        OfflineSetData(
          setId: 'set2',
          exerciseId: 'e2',
          exerciseName: 'Squats',
          setNumber: 1,
          weight: 100.0,
          reps: 12,
          completedAt: DateTime(2024, 1, 15, 10, 5),
          restTime: const Duration(minutes: 2),
          difficultyRating: 4,
        ),
      ];

      session = OfflineWorkoutSession(
        sessionId: 's1',
        workoutId: 'w1',
        userId: 'u1',
        startTime: DateTime(2024, 1, 15, 9, 0),
        endTime: DateTime(2024, 1, 15, 10, 30),
        completedSets: completedSets,
        sessionMetadata: {'gym': 'Home Gym'},
        syncStatus: SyncStatus.pending,
        lastModified: DateTime(2024, 1, 15, 10, 30),
        duration: 5400, // 90 minutes in seconds
        notes: 'Good workout',
        rating: 4,
        exerciseOrder: ['e1', 'e2'],
      );
    });

    test('should serialize to and from JSON correctly', () {
      final json = session.toJson();
      final restored = OfflineWorkoutSession.fromJson(json);

      expect(restored.sessionId, session.sessionId);
      expect(restored.workoutId, session.workoutId);
      expect(restored.userId, session.userId);
      expect(restored.startTime, session.startTime);
      expect(restored.endTime, session.endTime);
      expect(restored.completedSets.length, session.completedSets.length);
      expect(restored.syncStatus, session.syncStatus);
      expect(restored.duration, session.duration);
      expect(restored.notes, session.notes);
      expect(restored.rating, session.rating);
      expect(restored.exerciseOrder, session.exerciseOrder);
    });

    test('should calculate computed properties correctly', () {
      expect(session.isCompleted, true);
      expect(session.needsSync, true);
      expect(session.isSynced, false);
      expect(session.totalSets, 2);
      expect(session.totalReps, 22); // 10 + 12
      expect(session.totalVolume, 1200.0); // 0*10 + 100*12
      expect(session.uniqueExerciseIds, ['e1', 'e2']);
      expect(session.exerciseCount, 2);
    });

    test('should format duration correctly', () {
      expect(session.formattedDuration, '1h 30m');

      final shortSession = session.copyWith(
        startTime: DateTime(2024, 1, 15, 10, 0),
        endTime: DateTime(2024, 1, 15, 10, 30),
      );
      expect(shortSession.formattedDuration, '30m');
    });

    test('should handle incomplete session correctly', () {
      final incompleteSession = OfflineWorkoutSession(
        sessionId: 's2',
        workoutId: 'w1',
        userId: 'u1',
        startTime: DateTime(2024, 1, 15, 9, 0),
        lastModified: DateTime(2024, 1, 15, 9, 30),
      );

      expect(incompleteSession.isCompleted, false);
      expect(incompleteSession.sessionDuration.inMinutes, greaterThan(0));
    });

    test('should determine sync retry eligibility correctly', () {
      // Pending status - should not retry
      expect(session.canRetrySync, false);

      // Failed status with no previous attempts
      final failedSession = session.copyWith(
        syncStatus: SyncStatus.failed,
        retryCount: 0,
        lastSyncAttempt: null,
      );
      expect(failedSession.canRetrySync, true);

      // Failed status with max retries
      final maxRetriesSession = session.copyWith(
        syncStatus: SyncStatus.failed,
        retryCount: 5,
      );
      expect(maxRetriesSession.canRetrySync, false);

      // Failed status with recent attempt (should wait)
      final recentFailedSession = session.copyWith(
        syncStatus: SyncStatus.failed,
        retryCount: 1,
        lastSyncAttempt: DateTime.now().subtract(const Duration(seconds: 30)),
      );
      expect(recentFailedSession.canRetrySync, false);

      // Failed status with old attempt (can retry)
      final oldFailedSession = session.copyWith(
        syncStatus: SyncStatus.failed,
        retryCount: 1,
        lastSyncAttempt: DateTime.now().subtract(const Duration(minutes: 5)),
      );
      expect(oldFailedSession.canRetrySync, true);
    });

    test('should add sets correctly', () {
      final newSet = OfflineSetData(
        setId: 'set3',
        exerciseId: 'e1',
        exerciseName: 'Push-ups',
        setNumber: 2,
        weight: 0.0,
        reps: 8,
        completedAt: DateTime(2024, 1, 15, 10, 10),
      );

      final updatedSession = session.addSet(newSet);

      expect(updatedSession.completedSets.length, 3);
      expect(updatedSession.completedSets.last.setId, 'set3');
      expect(updatedSession.lastModified.isAfter(session.lastModified), true);
    });

    test('should update sets correctly', () {
      final updatedSet = completedSets[0].copyWith(reps: 15);
      final updatedSession = session.updateSet(0, updatedSet);

      expect(updatedSession.completedSets[0].reps, 15);
      expect(updatedSession.lastModified.isAfter(session.lastModified), true);

      // Test invalid index
      final invalidUpdate = session.updateSet(10, updatedSet);
      expect(invalidUpdate, session);
    });

    test('should mark as completed correctly', () {
      final incompleteSession = session.copyWith(endTime: null);
      final completedSession = incompleteSession.markAsCompleted(
        notes: 'Great workout!',
        rating: 5,
      );

      expect(completedSession.isCompleted, true);
      expect(completedSession.notes, 'Great workout!');
      expect(completedSession.rating, 5);
      expect(completedSession.duration, greaterThan(0));
    });

    test('should handle sync status changes correctly', () {
      // Mark as syncing
      final syncingSession = session.markSyncing();
      expect(syncingSession.syncStatus, SyncStatus.syncing);
      expect(syncingSession.syncError, null);
      expect(syncingSession.lastSyncAttempt, isNotNull);

      // Mark sync failed
      final failedSession = session.markSyncFailed('Network error');
      expect(failedSession.syncStatus, SyncStatus.failed);
      expect(failedSession.syncError, 'Network error');
      expect(failedSession.retryCount, 1);

      // Mark as synced
      final syncedSession = session.markSynced();
      expect(syncedSession.syncStatus, SyncStatus.synced);
      expect(syncedSession.syncError, null);
    });
  });

  group('OfflineSetData', () {
    late OfflineSetData setData;

    setUp(() {
      setData = OfflineSetData(
        setId: 'set1',
        exerciseId: 'e1',
        exerciseName: 'Bench Press',
        setNumber: 1,
        weight: 135.0,
        reps: 10,
        completedAt: DateTime(2024, 1, 15, 10, 0),
        restTime: const Duration(minutes: 2, seconds: 30),
        difficultyRating: 4,
        notes: 'Good form',
        metadata: {'tempo': '2-1-2-1'},
      );
    });

    test('should serialize to and from JSON correctly', () {
      final json = setData.toJson();
      final restored = OfflineSetData.fromJson(json);

      expect(restored.setId, setData.setId);
      expect(restored.exerciseId, setData.exerciseId);
      expect(restored.exerciseName, setData.exerciseName);
      expect(restored.setNumber, setData.setNumber);
      expect(restored.weight, setData.weight);
      expect(restored.reps, setData.reps);
      expect(restored.completedAt, setData.completedAt);
      expect(restored.restTime, setData.restTime);
      expect(restored.difficultyRating, setData.difficultyRating);
      expect(restored.notes, setData.notes);
      expect(restored.metadata, setData.metadata);
    });

    test('should calculate volume correctly', () {
      expect(setData.volume, 1350.0); // 135 * 10
    });

    test('should format values correctly', () {
      expect(setData.formattedWeight, '135.0 lbs');
      expect(setData.formattedReps, '10 reps');
      expect(setData.formattedVolume, '1350.0 lbs');
      expect(setData.formattedRestTime, '2m 30s');

      // Test bodyweight exercise
      final bodyweightSet = setData.copyWith(weight: 0.0);
      expect(bodyweightSet.formattedWeight, 'Bodyweight');

      // Test short rest time
      final shortRest = setData.copyWith(restTime: const Duration(seconds: 45));
      expect(shortRest.formattedRestTime, '45s');
    });

    test('should return correct difficulty labels', () {
      expect(setData.difficultyLabel, 'Hard');

      final easySet = setData.copyWith(difficultyRating: 1);
      expect(easySet.difficultyLabel, 'Very Easy');

      final moderateSet = setData.copyWith(difficultyRating: 3);
      expect(moderateSet.difficultyLabel, 'Moderate');

      final veryHardSet = setData.copyWith(difficultyRating: 5);
      expect(veryHardSet.difficultyLabel, 'Very Hard');

      final unratedSet = setData.copyWith(clearDifficultyRating: true);
      expect(unratedSet.difficultyLabel, 'Not Rated');
    });

    test('should copy with changes correctly', () {
      final updated = setData.copyWith(
        weight: 140.0,
        reps: 8,
        notes: 'Increased weight',
      );

      expect(updated.weight, 140.0);
      expect(updated.reps, 8);
      expect(updated.notes, 'Increased weight');
      expect(updated.setId, setData.setId); // Unchanged
      expect(updated.exerciseId, setData.exerciseId); // Unchanged
    });

    test('should convert to Supabase format correctly', () {
      final supabaseFormat = setData.toSupabaseFormat();

      expect(supabaseFormat['id'], setData.setId);
      expect(supabaseFormat['exercise_id'], setData.exerciseId);
      expect(supabaseFormat['set_number'], setData.setNumber);
      expect(supabaseFormat['weight'], setData.weight);
      expect(supabaseFormat['reps'], setData.reps);
      expect(supabaseFormat['completed_at'],
          setData.completedAt.toIso8601String());
      expect(supabaseFormat['rest_time'], setData.restTime.inSeconds);
      expect(supabaseFormat['difficulty_rating'], setData.difficultyRating);
      expect(supabaseFormat['notes'], setData.notes);
    });
  });

  group('OfflineSessionSummary', () {
    test('should calculate summary correctly', () {
      final summary = OfflineSessionSummary(
        totalSessions: 10,
        pendingSyncSessions: 3,
        failedSyncSessions: 2,
        oldestPendingSession: DateTime(2024, 1, 10),
        totalOfflineVolume: 15000.0,
        totalOfflineTime: const Duration(hours: 8, minutes: 30),
      );

      expect(summary.hasUnsyncedData, true);
      expect(summary.unsyncedSessions, 5); // 3 + 2
      expect(summary.syncStatusSummary, '5 sessions need sync');
      expect(summary.formattedTotalTime, '8h 30m');
    });

    test('should handle all synced data correctly', () {
      final summary = OfflineSessionSummary(
        totalSessions: 5,
        pendingSyncSessions: 0,
        failedSyncSessions: 0,
        totalOfflineVolume: 5000.0,
        totalOfflineTime: const Duration(hours: 2),
      );

      expect(summary.hasUnsyncedData, false);
      expect(summary.unsyncedSessions, 0);
      expect(summary.syncStatusSummary, 'All data synced');
      expect(summary.formattedTotalTime, '2h 0m');
    });

    test('should format short duration correctly', () {
      final summary = OfflineSessionSummary(
        totalSessions: 1,
        pendingSyncSessions: 0,
        failedSyncSessions: 0,
        totalOfflineVolume: 500.0,
        totalOfflineTime: const Duration(minutes: 45),
      );

      expect(summary.formattedTotalTime, '45m');
    });
  });
}
