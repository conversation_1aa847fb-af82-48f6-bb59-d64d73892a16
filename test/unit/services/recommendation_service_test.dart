import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/services/recommendation_service.dart';
import 'package:openfitv2/services/workout_filter_service.dart';
import 'package:openfitv2/models/workout.dart';
import 'package:openfitv2/models/exercise.dart';

void main() {
  group('RecommendationService Tests', () {
    late RecommendationService recommendationService;
    late List<Workout> testWorkouts;

    setUp(() {
      recommendationService = RecommendationService(supabaseClient: null);
      testWorkouts = _createTestWorkouts();
    });

    group('Workout Difficulty Estimation', () {
      test('should classify beginner workout correctly', () {
        final beginnerWorkout =
            _createWorkoutWithComplexity(2, 6); // 2 exercises, 6 total sets
        final difficulty =
            recommendationService.estimateWorkoutDifficulty(beginnerWorkout);
        expect(difficulty, WorkoutDifficulty.beginner);
      });

      test('should classify intermediate workout correctly', () {
        final intermediateWorkout =
            _createWorkoutWithComplexity(5, 15); // 5 exercises, 15 total sets
        final difficulty = recommendationService
            .estimateWorkoutDifficulty(intermediateWorkout);
        expect(difficulty, WorkoutDifficulty.intermediate);
      });

      test('should classify advanced workout correctly', () {
        final advancedWorkout =
            _createWorkoutWithComplexity(8, 24); // 8 exercises, 24 total sets
        final difficulty =
            recommendationService.estimateWorkoutDifficulty(advancedWorkout);
        expect(difficulty, WorkoutDifficulty.advanced);
      });
    });

    group('Workout Intensity Estimation', () {
      test('should estimate low intensity for bodyweight workout', () {
        final bodyweightWorkout = _createBodyweightWorkout();
        final intensity =
            recommendationService.estimateWorkoutIntensity(bodyweightWorkout);
        expect(intensity, lessThan(0.6));
      });

      test('should estimate higher intensity for equipment-based workout', () {
        final equipmentWorkout = _createEquipmentWorkout();
        final intensity =
            recommendationService.estimateWorkoutIntensity(equipmentWorkout);
        expect(intensity, greaterThanOrEqualTo(0.4));
      });

      test('should estimate higher intensity for multi-muscle workout', () {
        final multiMuscleWorkout = _createMultiMuscleWorkout();
        final intensity =
            recommendationService.estimateWorkoutIntensity(multiMuscleWorkout);
        expect(intensity, greaterThan(0.4));
      });
    });

    group('Workout Duration Estimation', () {
      test('should estimate duration based on total sets', () {
        final workout =
            _createWorkoutWithComplexity(4, 12); // 4 exercises, 12 total sets
        final duration = recommendationService.estimateWorkoutDuration(workout);
        expect(duration, equals(21)); // 12 * 1.75 = 21 minutes
      });

      test('should handle workouts with no exercises', () {
        final emptyWorkout = Workout(
          id: 'empty',
          userId: 'user1',
          name: 'Empty Workout',
          createdAt: DateTime.now(),
          exercises: [],
        );
        final duration =
            recommendationService.estimateWorkoutDuration(emptyWorkout);
        expect(duration, equals(0));
      });
    });

    group('Workout Similarity Calculation', () {
      test('should calculate high similarity for matching muscle groups', () {
        final workout = _createChestWorkout();
        final history = WorkoutHistory(
          workoutId: 'history1',
          completedAt: DateTime.now().subtract(const Duration(days: 1)),
          duration: 30,
          rating: 4.5,
          muscleGroups: ['Chest', 'Arms'],
          equipment: ['Dumbbells'],
        );

        final similarity =
            recommendationService.calculateWorkoutSimilarity(workout, history);
        expect(similarity, greaterThanOrEqualTo(0.5));
      });

      test('should calculate low similarity for different muscle groups', () {
        final chestWorkout = _createChestWorkout();
        final legHistory = WorkoutHistory(
          workoutId: 'history1',
          completedAt: DateTime.now().subtract(const Duration(days: 1)),
          duration: 30,
          rating: 4.5,
          muscleGroups: ['Legs', 'Glutes'],
          equipment: ['Barbell'],
        );

        final similarity = recommendationService.calculateWorkoutSimilarity(
            chestWorkout, legHistory);
        expect(similarity, lessThan(0.3));
      });

      test('should consider equipment similarity', () {
        final dumbbellWorkout = _createEquipmentWorkout();
        final dumbbellHistory = WorkoutHistory(
          workoutId: 'history1',
          completedAt: DateTime.now().subtract(const Duration(days: 1)),
          duration: 25,
          rating: 4.0,
          muscleGroups: ['Chest'],
          equipment: ['Dumbbells'],
        );

        final similarity = recommendationService.calculateWorkoutSimilarity(
            dumbbellWorkout, dumbbellHistory);
        expect(similarity, greaterThan(0.3));
      });

      test('should consider duration similarity', () {
        final workout = _createWorkoutWithComplexity(4, 12); // ~21 minutes
        final similarDurationHistory = WorkoutHistory(
          workoutId: 'history1',
          completedAt: DateTime.now().subtract(const Duration(days: 1)),
          duration: 25, // Close to 21 minutes
          rating: 4.0,
          muscleGroups: ['Chest'],
          equipment: ['Dumbbells'],
        );

        final similarity = recommendationService.calculateWorkoutSimilarity(
            workout, similarDurationHistory);
        expect(similarity, greaterThan(0.1)); // Should get duration bonus
      });
    });

    group('Popular Recommendations', () {
      test('should recommend workouts with good exercise variety', () {
        final recommendations =
            recommendationService.getPopularRecommendations(testWorkouts);

        // Should find workouts with 4-8 exercises
        expect(recommendations, isNotEmpty);

        for (final rec in recommendations) {
          expect(rec.workout.exercises.length, greaterThanOrEqualTo(4));
          expect(rec.workout.exercises.length, lessThanOrEqualTo(8));
          expect(rec.type, RecommendationType.popular);
        }
      });

      test('should favor balanced muscle targeting', () {
        final balancedWorkout = _createMultiMuscleWorkout();
        final recommendations =
            recommendationService.getPopularRecommendations([balancedWorkout]);

        if (recommendations.isNotEmpty) {
          final rec = recommendations.first;
          expect(rec.workout.primaryMuscles.toSet().length,
              greaterThanOrEqualTo(2));
        }
      });
    });

    group('Fitness Goal Recommendations', () {
      test('should recommend strength workouts for strength goals', () {
        // Set up user profile with strength goals
        recommendationService.userProfile = const UserProfile(
          userId: 'user1',
          fitnessGoals: ['strength', 'build muscle'],
        );

        final recommendations =
            recommendationService.getFitnessGoalRecommendations(testWorkouts);

        expect(recommendations, isNotEmpty);

        for (final rec in recommendations) {
          expect(rec.type, RecommendationType.fitnessGoals);
          expect(rec.reason, contains('strength'));
        }
      });

      test('should recommend high intensity workouts for weight loss goals',
          () {
        // Set up user profile with weight loss goals
        recommendationService.userProfile = const UserProfile(
          userId: 'user1',
          fitnessGoals: ['weight loss', 'cardio'],
        );

        final highIntensityWorkout = _createHighIntensityWorkout();
        final recommendations = recommendationService
            .getFitnessGoalRecommendations([highIntensityWorkout]);

        if (recommendations.isNotEmpty) {
          final rec = recommendations.first;
          expect(rec.type, RecommendationType.fitnessGoals);
          expect(rec.reason, contains('weight loss'));
        }
      });
    });

    group('Difficulty Recommendations', () {
      test('should recommend beginner workouts for beginner users', () {
        // Set up beginner user profile
        recommendationService.userProfile = const UserProfile(
          userId: 'user1',
          fitnessLevel: 'beginner',
        );

        final beginnerWorkout = _createWorkoutWithComplexity(2, 6);
        final recommendations = recommendationService
            .getDifficultyRecommendations([beginnerWorkout]);

        expect(recommendations, isNotEmpty);

        final rec = recommendations.first;
        expect(rec.type, RecommendationType.difficulty);
        expect(rec.reason, contains('beginner'));
        expect(rec.confidence, greaterThan(0.5));
      });

      test('should recommend appropriate workouts for intermediate users', () {
        // Set up intermediate user profile
        recommendationService.userProfile = const UserProfile(
          userId: 'user1',
          fitnessLevel: 'intermediate',
        );

        final intermediateWorkout = _createWorkoutWithComplexity(5, 15);
        final recommendations = recommendationService
            .getDifficultyRecommendations([intermediateWorkout]);

        expect(recommendations, isNotEmpty);

        final rec = recommendations.first;
        expect(rec.type, RecommendationType.difficulty);
        expect(rec.confidence, greaterThan(0.5));
      });
    });

    group('Muscle Balance Recommendations', () {
      test('should recommend workouts for underworked muscle groups', () {
        // Set up workout history with only chest workouts
        recommendationService.workoutHistory = [
          WorkoutHistory(
            workoutId: 'chest1',
            completedAt: DateTime.now().subtract(const Duration(days: 1)),
            duration: 30,
            rating: 4.0,
            muscleGroups: ['Chest'],
            equipment: ['Dumbbells'],
          ),
          WorkoutHistory(
            workoutId: 'chest2',
            completedAt: DateTime.now().subtract(const Duration(days: 3)),
            duration: 25,
            rating: 4.5,
            muscleGroups: ['Chest'],
            equipment: ['Barbell'],
          ),
        ];

        final backWorkout = _createBackWorkout();
        final recommendations = recommendationService
            .getMuscleBalanceRecommendations([backWorkout]);

        expect(recommendations, isNotEmpty);

        final rec = recommendations.first;
        expect(rec.type, RecommendationType.muscleBalance);
        expect(rec.reason, contains('Balance'));
        expect(rec.reason, contains('Back'));
      });

      test('should not recommend when muscle groups are balanced', () {
        // Set up balanced workout history
        recommendationService.workoutHistory = [
          WorkoutHistory(
            workoutId: 'chest1',
            completedAt: DateTime.now().subtract(const Duration(days: 1)),
            duration: 30,
            rating: 4.0,
            muscleGroups: ['Chest'],
            equipment: ['Dumbbells'],
          ),
          WorkoutHistory(
            workoutId: 'back1',
            completedAt: DateTime.now().subtract(const Duration(days: 2)),
            duration: 30,
            rating: 4.0,
            muscleGroups: ['Back'],
            equipment: ['Pull-up Bar'],
          ),
          WorkoutHistory(
            workoutId: 'legs1',
            completedAt: DateTime.now().subtract(const Duration(days: 3)),
            duration: 35,
            rating: 4.5,
            muscleGroups: ['Legs'],
            equipment: ['Barbell'],
          ),
        ];

        final chestWorkout = _createChestWorkout();
        final recommendations = recommendationService
            .getMuscleBalanceRecommendations([chestWorkout]);

        // Should have fewer or no recommendations since muscles are balanced
        expect(recommendations.length, lessThanOrEqualTo(1));
      });
    });
  });
}

List<Workout> _createTestWorkouts() {
  return [
    _createChestWorkout(),
    _createBackWorkout(),
    _createBodyweightWorkout(),
    _createEquipmentWorkout(),
    _createMultiMuscleWorkout(),
  ];
}

Workout _createChestWorkout() {
  final exercise = Exercise(
    id: '1',
    name: 'Push Up',
    description: 'Basic push up',
    primaryMuscle: 'Chest',
    equipment: null,
    category: 'Strength',
  );

  final workoutExercise = WorkoutExercise(
    id: '1',
    workoutId: '1',
    exerciseId: '1',
    exercise: exercise,
    sets: 3,
    reps: [10, 10, 10],
    orderIndex: 0,
    name: 'Push Up',
    completed: false,
  );

  return Workout(
    id: '1',
    userId: 'user1',
    name: 'Chest Workout',
    createdAt: DateTime.now(),
    exercises: [workoutExercise],
  );
}

Workout _createBackWorkout() {
  final exercise = Exercise(
    id: '2',
    name: 'Pull Up',
    description: 'Basic pull up',
    primaryMuscle: 'Back',
    equipment: 'Pull-up Bar',
    category: 'Strength',
  );

  final workoutExercise = WorkoutExercise(
    id: '2',
    workoutId: '2',
    exerciseId: '2',
    exercise: exercise,
    sets: 3,
    reps: [8, 8, 8],
    orderIndex: 0,
    name: 'Pull Up',
    completed: false,
  );

  return Workout(
    id: '2',
    userId: 'user1',
    name: 'Back Workout',
    createdAt: DateTime.now(),
    exercises: [workoutExercise],
  );
}

Workout _createBodyweightWorkout() {
  final exercises = [
    WorkoutExercise(
      id: '3',
      workoutId: '3',
      exerciseId: '3',
      exercise: Exercise(
        id: '3',
        name: 'Bodyweight Squat',
        primaryMuscle: 'Legs',
        equipment: null,
        category: 'Strength',
      ),
      sets: 3,
      reps: [15, 15, 15],
      orderIndex: 0,
      name: 'Bodyweight Squat',
      completed: false,
    ),
    WorkoutExercise(
      id: '4',
      workoutId: '3',
      exerciseId: '4',
      exercise: Exercise(
        id: '4',
        name: 'Push Up',
        primaryMuscle: 'Chest',
        equipment: null,
        category: 'Strength',
      ),
      sets: 3,
      reps: [10, 10, 10],
      orderIndex: 1,
      name: 'Push Up',
      completed: false,
    ),
  ];

  return Workout(
    id: '3',
    userId: 'user1',
    name: 'Bodyweight Workout',
    createdAt: DateTime.now(),
    exercises: exercises,
  );
}

Workout _createEquipmentWorkout() {
  final exercise = Exercise(
    id: '5',
    name: 'Dumbbell Press',
    description: 'Chest press with dumbbells',
    primaryMuscle: 'Chest',
    equipment: 'Dumbbells',
    category: 'Strength',
  );

  final workoutExercise = WorkoutExercise(
    id: '5',
    workoutId: '4',
    exerciseId: '5',
    exercise: exercise,
    sets: 4,
    reps: [12, 12, 12, 12],
    orderIndex: 0,
    name: 'Dumbbell Press',
    completed: false,
  );

  return Workout(
    id: '4',
    userId: 'user1',
    name: 'Dumbbell Workout',
    createdAt: DateTime.now(),
    exercises: [workoutExercise],
  );
}

Workout _createMultiMuscleWorkout() {
  final exercises = [
    WorkoutExercise(
      id: '6',
      workoutId: '5',
      exerciseId: '6',
      exercise: Exercise(
        id: '6',
        name: 'Deadlift',
        primaryMuscle: 'Back',
        equipment: 'Barbell',
        category: 'Strength',
      ),
      sets: 3,
      reps: [8, 8, 8],
      orderIndex: 0,
      name: 'Deadlift',
      completed: false,
    ),
    WorkoutExercise(
      id: '7',
      workoutId: '5',
      exerciseId: '7',
      exercise: Exercise(
        id: '7',
        name: 'Squat',
        primaryMuscle: 'Legs',
        equipment: 'Barbell',
        category: 'Strength',
      ),
      sets: 3,
      reps: [10, 10, 10],
      orderIndex: 1,
      name: 'Squat',
      completed: false,
    ),
    WorkoutExercise(
      id: '8',
      workoutId: '5',
      exerciseId: '8',
      exercise: Exercise(
        id: '8',
        name: 'Bench Press',
        primaryMuscle: 'Chest',
        equipment: 'Barbell',
        category: 'Strength',
      ),
      sets: 3,
      reps: [10, 10, 10],
      orderIndex: 2,
      name: 'Bench Press',
      completed: false,
    ),
    WorkoutExercise(
      id: '9',
      workoutId: '5',
      exerciseId: '9',
      exercise: Exercise(
        id: '9',
        name: 'Overhead Press',
        primaryMuscle: 'Shoulders',
        equipment: 'Barbell',
        category: 'Strength',
      ),
      sets: 3,
      reps: [8, 8, 8],
      orderIndex: 3,
      name: 'Overhead Press',
      completed: false,
    ),
  ];

  return Workout(
    id: '5',
    userId: 'user1',
    name: 'Full Body Workout',
    createdAt: DateTime.now(),
    exercises: exercises,
  );
}

Workout _createWorkoutWithComplexity(int exerciseCount, int totalSets) {
  final exercises = <WorkoutExercise>[];
  final setsPerExercise = (totalSets / exerciseCount).round();

  for (int i = 0; i < exerciseCount; i++) {
    exercises.add(WorkoutExercise(
      id: 'ex$i',
      workoutId: 'complex',
      exerciseId: 'ex$i',
      exercise: Exercise(
        id: 'ex$i',
        name: 'Exercise $i',
        primaryMuscle: 'Chest',
        equipment: 'Dumbbells',
        category: 'Strength',
      ),
      sets: setsPerExercise,
      reps: List.filled(setsPerExercise, 10),
      orderIndex: i,
      name: 'Exercise $i',
      completed: false,
    ));
  }

  return Workout(
    id: 'complex',
    userId: 'user1',
    name: 'Complex Workout',
    createdAt: DateTime.now(),
    exercises: exercises,
  );
}

Workout _createHighIntensityWorkout() {
  final exercises = List.generate(
      8,
      (i) => WorkoutExercise(
            id: 'hiit$i',
            workoutId: 'hiit',
            exerciseId: 'hiit$i',
            exercise: Exercise(
              id: 'hiit$i',
              name: 'HIIT Exercise $i',
              primaryMuscle: i % 2 == 0 ? 'Chest' : 'Legs',
              equipment: 'Dumbbells',
              category: 'Cardio',
            ),
            sets: 4,
            reps: [15, 15, 15, 15],
            orderIndex: i,
            name: 'HIIT Exercise $i',
            completed: false,
          ));

  return Workout(
    id: 'hiit',
    userId: 'user1',
    name: 'HIIT Workout',
    createdAt: DateTime.now(),
    exercises: exercises,
  );
}
