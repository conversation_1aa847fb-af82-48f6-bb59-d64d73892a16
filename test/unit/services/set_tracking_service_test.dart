import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../lib/services/set_tracking_service.dart';
import '../../../lib/services/offline_service.dart';
import '../../../lib/services/progress_service.dart';
import '../../../lib/models/exercise.dart';
import '../../../lib/models/offline_workout_session.dart';

// Mock classes for testing
class MockOfflineService extends OfflineService {
  final Map<String, List<OfflineSetData>> _sessionSets = {};

  @override
  Future<String> addSet({
    required String sessionId,
    required String exerciseId,
    required String exerciseName,
    required int setNumber,
    required double weight,
    required int reps,
    Duration restTime = Duration.zero,
    int? difficultyRating,
    String? notes,
    Map<String, dynamic>? metadata,
  }) async {
    final setId = 'set_${DateTime.now().millisecondsSinceEpoch}';
    final setData = OfflineSetData(
      setId: setId,
      exerciseId: exerciseId,
      exerciseName: exerciseName,
      setNumber: setNumber,
      weight: weight,
      reps: reps,
      completedAt: DateTime.now(),
      restTime: restTime,
      difficultyRating: difficultyRating,
      notes: notes,
      metadata: metadata ?? {},
    );

    _sessionSets.putIfAbsent(sessionId, () => []).add(setData);
    return setId;
  }
}

class MockProgressService extends ChangeNotifier {
  final Map<String, List<Map<String, dynamic>>> _exerciseProgress = {};

  void setExerciseProgress(
      String exerciseId, List<Map<String, dynamic>> progress) {
    _exerciseProgress[exerciseId] = progress;
  }

  Future<List<Map<String, dynamic>>> getExerciseProgress(
    String exerciseId, {
    int limit = 20,
  }) async {
    return _exerciseProgress[exerciseId] ?? [];
  }
}

void main() {
  group('SetTrackingService', () {
    late SetTrackingService setTrackingService;
    late MockOfflineService mockOfflineService;
    late MockProgressService mockProgressService;

    setUp(() {
      mockOfflineService = MockOfflineService();
      mockProgressService = MockProgressService();

      setTrackingService = SetTrackingService(
        offlineService: mockOfflineService,
        progressService: mockProgressService,
      );
    });

    group('generateSetSuggestion', () {
      late Exercise testExercise;

      setUp(() {
        testExercise = Exercise(
          id: 'exercise-1',
          name: 'Bench Press',
          primaryMuscle: 'Chest',
          equipment: 'Barbell',
        );
      });

      test('should generate suggestion with no historical data', () async {
        // Arrange
        mockProgressService.setExerciseProgress('exercise-1', []);

        // Act
        final suggestion = await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 1,
        );

        // Assert
        expect(suggestion.suggestedWeight, equals(135.0)); // Chest default
        expect(suggestion.suggestedReps, equals(8));
        expect(suggestion.confidence, lessThan(0.5)); // Low confidence
        expect(suggestion.reasoning, contains('Starting recommendation'));
      });

      test('should generate suggestion based on historical data', () async {
        // Arrange
        final historicalData = [
          {'weight': 185.0, 'reps': 8, 'completed_at': '2024-01-15T10:00:00Z'},
          {'weight': 180.0, 'reps': 8, 'completed_at': '2024-01-12T10:00:00Z'},
          {'weight': 175.0, 'reps': 9, 'completed_at': '2024-01-10T10:00:00Z'},
        ];

        mockProgressService.setExerciseProgress('exercise-1', historicalData);

        // Act
        final suggestion = await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 1,
        );

        // Assert
        expect(suggestion.suggestedWeight,
            greaterThan(180.0)); // Should be higher for first set
        expect(suggestion.suggestedReps, equals(8));
        expect(suggestion.confidence, greaterThan(0.5));
        expect(suggestion.reasoning, contains('First set'));
      });

      test('should adjust suggestion based on fatigue level', () async {
        // Arrange
        final historicalData = [
          {'weight': 185.0, 'reps': 8, 'completed_at': '2024-01-15T10:00:00Z'},
        ];

        mockProgressService.setExerciseProgress('exercise-1', historicalData);

        // Create previous sets with high difficulty ratings (indicating fatigue)
        final previousSets = [
          OfflineSetData(
            setId: 'set-1',
            exerciseId: 'exercise-1',
            exerciseName: 'Bench Press',
            setNumber: 1,
            weight: 185.0,
            reps: 8,
            completedAt: DateTime.now().subtract(const Duration(minutes: 5)),
            difficultyRating: 5, // Very hard
          ),
          OfflineSetData(
            setId: 'set-2',
            exerciseId: 'exercise-1',
            exerciseName: 'Bench Press',
            setNumber: 2,
            weight: 180.0,
            reps: 7,
            completedAt: DateTime.now().subtract(const Duration(minutes: 2)),
            difficultyRating: 5, // Very hard
          ),
        ];

        // Act
        final suggestion = await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 3,
          previousSetsInSession: previousSets,
        );

        // Assert
        expect(suggestion.suggestedWeight,
            lessThan(180.0)); // Should be reduced due to fatigue
        expect(suggestion.reasoning, contains('fatigue'));
      });

      test('should suggest progressive overload for consistent performers',
          () async {
        // Arrange
        final consistentHistoricalData = List.generate(
            10,
            (index) => {
                  'weight': 185.0, // Very consistent weight
                  'reps': 8, // Very consistent reps
                  'completed_at': DateTime.now()
                      .subtract(Duration(days: index + 1))
                      .toIso8601String(),
                });

        mockProgressService.setExerciseProgress(
            'exercise-1', consistentHistoricalData);

        // Act
        final suggestion = await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 1,
        );

        // Assert
        expect(suggestion.suggestedWeight,
            greaterThan(185.0)); // Should suggest increase
        expect(
            suggestion.reasoning,
            anyOf([
              contains('progression'),
              contains('overload'),
              contains('Consistent performance'),
            ]));
      });

      test('should adjust based on previous set difficulty rating', () async {
        // Arrange
        final historicalData = [
          {'weight': 185.0, 'reps': 8, 'completed_at': '2024-01-15T10:00:00Z'},
        ];

        mockProgressService.setExerciseProgress('exercise-1', historicalData);

        final easyPreviousSet = OfflineSetData(
          setId: 'set-1',
          exerciseId: 'exercise-1',
          exerciseName: 'Bench Press',
          setNumber: 1,
          weight: 185.0,
          reps: 8,
          completedAt: DateTime.now().subtract(const Duration(minutes: 2)),
          difficultyRating: 1, // Very easy
        );

        // Act
        final suggestion = await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 2,
          previousSetsInSession: [easyPreviousSet],
        );

        // Assert
        expect(suggestion.suggestedWeight,
            greaterThan(185.0)); // Should increase weight
        expect(suggestion.reasoning, contains('easy'));
      });

      test('should handle different set numbers appropriately', () async {
        // Arrange
        final historicalData = [
          {'weight': 185.0, 'reps': 8, 'completed_at': '2024-01-15T10:00:00Z'},
        ];

        mockProgressService.setExerciseProgress('exercise-1', historicalData);

        // Act
        final firstSetSuggestion =
            await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 1,
        );

        final laterSetSuggestion =
            await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 5,
        );

        // Assert
        expect(firstSetSuggestion.suggestedWeight,
            greaterThan(laterSetSuggestion.suggestedWeight));
        expect(firstSetSuggestion.reasoning, contains('First set'));
        expect(
            laterSetSuggestion.reasoning,
            anyOf([
              contains('Later set'),
              contains('fatigue'),
            ]));
      });
    });

    group('fatigue calculation', () {
      test('should calculate zero fatigue with no previous sets', () async {
        // Arrange
        final testExercise = Exercise(
          id: 'exercise-1',
          name: 'Test Exercise',
        );

        mockProgressService.setExerciseProgress('exercise-1', []);

        // Act
        final suggestion = await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 1,
          previousSetsInSession: [],
        );

        // Assert - With no previous sets, fatigue should be minimal
        expect(suggestion.confidence, greaterThan(0.2));
      });

      test('should increase fatigue with more sets', () async {
        // Arrange
        final testExercise = Exercise(
          id: 'exercise-1',
          name: 'Test Exercise',
        );

        mockProgressService.setExerciseProgress('exercise-1', [
          {'weight': 100.0, 'reps': 8, 'completed_at': '2024-01-15T10:00:00Z'},
        ]);

        final manySets = List.generate(
            8,
            (index) => OfflineSetData(
                  setId: 'set-$index',
                  exerciseId: 'exercise-1',
                  exerciseName: 'Test Exercise',
                  setNumber: index + 1,
                  weight: 100.0,
                  reps: 8,
                  completedAt:
                      DateTime.now().subtract(Duration(minutes: index * 2)),
                  difficultyRating: 3,
                ));

        // Act
        final suggestion = await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 9,
          previousSetsInSession: manySets,
        );

        // Assert - With many sets, weight should be reduced due to fatigue
        expect(suggestion.suggestedWeight, lessThan(100.0));
      });

      test('should account for high difficulty ratings in fatigue', () async {
        // Arrange
        final testExercise = Exercise(
          id: 'exercise-1',
          name: 'Test Exercise',
        );

        mockProgressService.setExerciseProgress('exercise-1', [
          {'weight': 100.0, 'reps': 8, 'completed_at': '2024-01-15T10:00:00Z'},
        ]);

        final hardSets = List.generate(
            3,
            (index) => OfflineSetData(
                  setId: 'set-$index',
                  exerciseId: 'exercise-1',
                  exerciseName: 'Test Exercise',
                  setNumber: index + 1,
                  weight: 100.0,
                  reps: 8,
                  completedAt:
                      DateTime.now().subtract(Duration(minutes: index * 2)),
                  difficultyRating: 5, // Very hard
                ));

        // Act
        final suggestion = await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 4,
          previousSetsInSession: hardSets,
        );

        // Assert - High difficulty should result in significant weight reduction
        expect(suggestion.suggestedWeight,
            lessThan(95.0)); // Should be reduced significantly
      });
    });

    group('exercise-specific defaults', () {
      test('should provide appropriate defaults for different muscle groups',
          () async {
        // Test different muscle groups
        final testCases = [
          {'muscle': 'Chest', 'expectedWeight': 135.0, 'expectedReps': 8},
          {'muscle': 'Back', 'expectedWeight': 115.0, 'expectedReps': 8},
          {'muscle': 'Shoulders', 'expectedWeight': 85.0, 'expectedReps': 10},
          {'muscle': 'Arms', 'expectedWeight': 65.0, 'expectedReps': 10},
          {'muscle': 'Legs', 'expectedWeight': 185.0, 'expectedReps': 8},
          {'muscle': 'Core', 'expectedWeight': 0.0, 'expectedReps': 15},
        ];

        for (final testCase in testCases) {
          // Arrange
          final exercise = Exercise(
            id: 'exercise-${testCase['muscle']}',
            name: '${testCase['muscle']} Exercise',
            primaryMuscle: testCase['muscle'] as String,
          );

          mockProgressService.setExerciseProgress(exercise.id, []);

          // Act
          final suggestion = await setTrackingService.generateSetSuggestion(
            exercise: exercise,
            setNumber: 1,
          );

          // Assert
          expect(
            suggestion.suggestedWeight,
            equals(testCase['expectedWeight']),
            reason:
                'Weight for ${testCase['muscle']} should be ${testCase['expectedWeight']}',
          );
          expect(
            suggestion.suggestedReps,
            equals(testCase['expectedReps']),
            reason:
                'Reps for ${testCase['muscle']} should be ${testCase['expectedReps']}',
          );
        }
      });
    });

    group('saveSetData', () {
      test('should save to offline service', () async {
        // Arrange
        final setData = OfflineSetData(
          setId: 'test-set-id',
          exerciseId: 'exercise-1',
          exerciseName: 'Test Exercise',
          setNumber: 1,
          weight: 100.0,
          reps: 8,
          completedAt: DateTime.now(),
        );

        // Act
        await setTrackingService.saveSetData(
          sessionId: 'test-session-id',
          setData: setData,
        );

        // Assert - Check that the set was added to the mock service
        expect(mockOfflineService._sessionSets['test-session-id'], isNotNull);
        expect(mockOfflineService._sessionSets['test-session-id']!.length,
            equals(1));
        final savedSet =
            mockOfflineService._sessionSets['test-session-id']!.first;
        expect(savedSet.exerciseId, equals('exercise-1'));
        expect(savedSet.weight, equals(100.0));
        expect(savedSet.reps, equals(8));
      });
    });

    group('performance trend calculation', () {
      test('should detect improving trend', () async {
        // Arrange - Progressive improvement in volume
        final improvingData = [
          {
            'weight': 110.0,
            'reps': 8,
            'completed_at': '2024-01-15T10:00:00Z'
          }, // Most recent
          {'weight': 105.0, 'reps': 8, 'completed_at': '2024-01-12T10:00:00Z'},
          {'weight': 100.0, 'reps': 8, 'completed_at': '2024-01-10T10:00:00Z'},
          {'weight': 95.0, 'reps': 8, 'completed_at': '2024-01-08T10:00:00Z'},
          {
            'weight': 90.0,
            'reps': 8,
            'completed_at': '2024-01-05T10:00:00Z'
          }, // Oldest
        ];

        final testExercise = Exercise(
          id: 'exercise-1',
          name: 'Test Exercise',
        );

        mockProgressService.setExerciseProgress('exercise-1', improvingData);

        // Act
        final suggestion = await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 1,
        );

        // Assert - Should suggest progressive overload
        expect(suggestion.suggestedWeight, greaterThan(110.0));
        expect(
            suggestion.reasoning,
            anyOf([
              contains('improving'),
              contains('overload'),
            ]));
      });

      test('should detect declining trend', () async {
        // Arrange - Declining performance
        final decliningData = [
          {
            'weight': 90.0,
            'reps': 8,
            'completed_at': '2024-01-15T10:00:00Z'
          }, // Most recent
          {'weight': 95.0, 'reps': 8, 'completed_at': '2024-01-12T10:00:00Z'},
          {'weight': 100.0, 'reps': 8, 'completed_at': '2024-01-10T10:00:00Z'},
          {'weight': 105.0, 'reps': 8, 'completed_at': '2024-01-08T10:00:00Z'},
          {
            'weight': 110.0,
            'reps': 8,
            'completed_at': '2024-01-05T10:00:00Z'
          }, // Oldest
        ];

        final testExercise = Exercise(
          id: 'exercise-1',
          name: 'Test Exercise',
        );

        mockProgressService.setExerciseProgress('exercise-1', decliningData);

        // Act
        final suggestion = await setTrackingService.generateSetSuggestion(
          exercise: testExercise,
          setNumber: 1,
        );

        // Assert - Should be conservative with suggestions
        expect(suggestion.suggestedWeight,
            lessThanOrEqualTo(95.0)); // Should not exceed recent performance
      });
    });
  });
}
