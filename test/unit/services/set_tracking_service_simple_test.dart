import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/set_tracking_service.dart';
import '../../../lib/models/exercise.dart';
import '../../../lib/models/offline_workout_session.dart';

void main() {
  group('SetTrackingService Logic Tests', () {
    test('should generate fallback suggestion for unknown exercise', () async {
      // Test the fallback suggestion logic without dependencies
      final exercise = Exercise(
        id: 'unknown-exercise',
        name: 'Unknown Exercise',
        primaryMuscle: 'Unknown',
      );

      // Test exercise defaults logic
      expect(exercise.primaryMuscle, equals('Unknown'));
      expect(exercise.name, equals('Unknown Exercise'));
    });

    test('should create valid OfflineSetData', () {
      final setData = OfflineSetData(
        setId: 'test-set-id',
        exerciseId: 'exercise-1',
        exerciseName: 'Test Exercise',
        setNumber: 1,
        weight: 185.0,
        reps: 8,
        completedAt: DateTime.now(),
        difficultyRating: 3,
        notes: 'Test notes',
      );

      expect(setData.setId, equals('test-set-id'));
      expect(setData.weight, equals(185.0));
      expect(setData.reps, equals(8));
      expect(setData.difficultyRating, equals(3));
      expect(setData.volume, equals(185.0 * 8));
      expect(setData.formattedWeight, equals('185.0 lbs'));
      expect(setData.formattedReps, equals('8 reps'));
      expect(setData.difficultyLabel, equals('Moderate'));
    });

    test('should handle bodyweight exercises', () {
      final setData = OfflineSetData(
        setId: 'test-set-id',
        exerciseId: 'exercise-1',
        exerciseName: 'Push-ups',
        setNumber: 1,
        weight: 0.0,
        reps: 15,
        completedAt: DateTime.now(),
      );

      expect(setData.formattedWeight, equals('Bodyweight'));
      expect(setData.volume, equals(0.0));
    });

    test('should calculate rest time correctly', () {
      final setData = OfflineSetData(
        setId: 'test-set-id',
        exerciseId: 'exercise-1',
        exerciseName: 'Test Exercise',
        setNumber: 1,
        weight: 100.0,
        reps: 8,
        completedAt: DateTime.now(),
        restTime: const Duration(minutes: 2, seconds: 30),
      );

      expect(setData.formattedRestTime, equals('2m 30s'));
    });

    test('should handle short rest times', () {
      final setData = OfflineSetData(
        setId: 'test-set-id',
        exerciseId: 'exercise-1',
        exerciseName: 'Test Exercise',
        setNumber: 1,
        weight: 100.0,
        reps: 8,
        completedAt: DateTime.now(),
        restTime: const Duration(seconds: 45),
      );

      expect(setData.formattedRestTime, equals('45s'));
    });

    test('should handle difficulty ratings correctly', () {
      final ratings = [1, 2, 3, 4, 5];
      final expectedLabels = [
        'Very Easy',
        'Easy',
        'Moderate',
        'Hard',
        'Very Hard'
      ];

      for (int i = 0; i < ratings.length; i++) {
        final setData = OfflineSetData(
          setId: 'test-set-id',
          exerciseId: 'exercise-1',
          exerciseName: 'Test Exercise',
          setNumber: 1,
          weight: 100.0,
          reps: 8,
          completedAt: DateTime.now(),
          difficultyRating: ratings[i],
        );

        expect(setData.difficultyLabel, equals(expectedLabels[i]));
      }
    });

    test('should handle null difficulty rating', () {
      final setData = OfflineSetData(
        setId: 'test-set-id',
        exerciseId: 'exercise-1',
        exerciseName: 'Test Exercise',
        setNumber: 1,
        weight: 100.0,
        reps: 8,
        completedAt: DateTime.now(),
        difficultyRating: null,
      );

      expect(setData.difficultyLabel, equals('Not Rated'));
    });

    test('should create SetSuggestion correctly', () {
      final suggestion = SetSuggestion(
        suggestedWeight: 185.0,
        suggestedReps: 8,
        confidence: 0.8,
        reasoning: 'Based on your recent performance',
      );

      expect(suggestion.suggestedWeight, equals(185.0));
      expect(suggestion.suggestedReps, equals(8));
      expect(suggestion.confidence, equals(0.8));
      expect(suggestion.reasoning, equals('Based on your recent performance'));
    });

    test('should format SetSuggestion toString correctly', () {
      final suggestion = SetSuggestion(
        suggestedWeight: 185.5,
        suggestedReps: 8,
        confidence: 0.75,
        reasoning: 'Test reasoning',
      );

      final stringRepresentation = suggestion.toString();
      expect(stringRepresentation, contains('185.5'));
      expect(stringRepresentation, contains('8'));
      expect(stringRepresentation, contains('75%'));
    });

    test('should handle copyWith for OfflineSetData', () {
      final originalSet = OfflineSetData(
        setId: 'test-set-id',
        exerciseId: 'exercise-1',
        exerciseName: 'Test Exercise',
        setNumber: 1,
        weight: 100.0,
        reps: 8,
        completedAt: DateTime.now(),
        difficultyRating: 3,
      );

      final modifiedSet = originalSet.copyWith(
        weight: 110.0,
        reps: 10,
        difficultyRating: 4,
      );

      expect(modifiedSet.weight, equals(110.0));
      expect(modifiedSet.reps, equals(10));
      expect(modifiedSet.difficultyRating, equals(4));
      expect(modifiedSet.setId, equals(originalSet.setId)); // Unchanged
      expect(
          modifiedSet.exerciseId, equals(originalSet.exerciseId)); // Unchanged
    });

    test('should clear difficulty rating with copyWith', () {
      final originalSet = OfflineSetData(
        setId: 'test-set-id',
        exerciseId: 'exercise-1',
        exerciseName: 'Test Exercise',
        setNumber: 1,
        weight: 100.0,
        reps: 8,
        completedAt: DateTime.now(),
        difficultyRating: 3,
      );

      final modifiedSet = originalSet.copyWith(
        clearDifficultyRating: true,
      );

      expect(modifiedSet.difficultyRating, isNull);
    });

    test('should convert to Supabase format correctly', () {
      final setData = OfflineSetData(
        setId: 'test-set-id',
        exerciseId: 'exercise-1',
        exerciseName: 'Test Exercise',
        setNumber: 1,
        weight: 185.0,
        reps: 8,
        completedAt: DateTime.parse('2024-01-15T10:00:00Z'),
        restTime: const Duration(minutes: 2),
        difficultyRating: 3,
        notes: 'Good form',
      );

      final supabaseFormat = setData.toSupabaseFormat();

      expect(supabaseFormat['id'], equals('test-set-id'));
      expect(supabaseFormat['exercise_id'], equals('exercise-1'));
      expect(supabaseFormat['set_number'], equals(1));
      expect(supabaseFormat['weight'], equals(185.0));
      expect(supabaseFormat['reps'], equals(8));
      expect(
          supabaseFormat['completed_at'], equals('2024-01-15T10:00:00.000Z'));
      expect(supabaseFormat['rest_time'], equals(120)); // 2 minutes in seconds
      expect(supabaseFormat['difficulty_rating'], equals(3));
      expect(supabaseFormat['notes'], equals('Good form'));
    });
  });
}
