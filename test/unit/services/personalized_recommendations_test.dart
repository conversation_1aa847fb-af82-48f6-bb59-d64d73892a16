import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:openfitv2/models/exercise.dart';
import 'package:openfitv2/services/recommendation_service.dart';
import 'package:openfitv2/widgets/personalized_recommendations.dart';

void main() {
  group('PersonalizedRecommendations Logic Tests', () {
    late RecommendationService recommendationService;

    setUp(() {
      recommendationService = RecommendationService();
    });

    group('Weight Suggestions', () {
      test('should suggest appropriate weight for beginner with barbell', () {
        // Set up beginner profile
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'beginner',
        );

        final exercise = Exercise(
          id: '1',
          name: 'Barbell Bench Press',
          equipment: 'Barbell',
          primaryMuscle: 'Chest',
        );

        // Test that the exercise is properly configured for weight suggestions
        expect(exercise.equipment, equals('Barbell'));
        expect(exercise.primaryMuscle, equals('Chest'));
        expect(recommendationService.userProfile?.fitnessLevel,
            equals('beginner'));
      });

      test('should suggest appropriate weight for intermediate with dumbbells',
          () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'intermediate',
        );

        final exercise = Exercise(
          id: '2',
          name: 'Dumbbell Press',
          equipment: 'Dumbbells',
          primaryMuscle: 'Chest',
        );

        expect(exercise.equipment, equals('Dumbbells'));
        expect(recommendationService.userProfile?.fitnessLevel,
            equals('intermediate'));
      });

      test('should not suggest weight for bodyweight exercises', () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'beginner',
        );

        final exercise = Exercise(
          id: '3',
          name: 'Push-ups',
          equipment: 'None',
          primaryMuscle: 'Chest',
        );

        expect(exercise.equipment, equals('None'));
        // Bodyweight exercises should not have weight suggestions
      });

      test('should calculate progressive overload from workout set logs', () {
        // Test progressive overload calculation logic
        const recentWeights = [100.0, 105.0, 105.0, 110.0];
        final maxWeight = recentWeights.reduce((a, b) => a > b ? a : b);
        final suggestedWeight = maxWeight * 1.025;

        expect(maxWeight, equals(110.0));
        expect(suggestedWeight, closeTo(112.75, 0.1));
      });

      test('should handle empty workout set logs gracefully', () {
        const List<double> recentWeights = [];

        // Should fall back to profile-based suggestions when no logs exist
        expect(recentWeights.isEmpty, isTrue);
      });
    });

    group('Rep Suggestions', () {
      test('should suggest strength-focused reps for strength goals', () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'intermediate',
          fitnessGoals: ['strength', 'build muscle'],
        );

        final exercise = Exercise(
          id: '1',
          name: 'Squat',
          primaryMuscle: 'Legs',
        );

        // Verify that strength goals are set
        expect(recommendationService.userProfile?.fitnessGoals,
            contains('strength'));

        // Test rep calculation for strength goals
        const recentReps = [8, 6, 7, 5];
        final maxReps = recentReps.reduce((a, b) => a > b ? a : b);
        final suggestedReps = (maxReps * 0.8).round().clamp(3, 8);

        expect(maxReps, equals(8));
        expect(suggestedReps, equals(6)); // 8 * 0.8 = 6.4, rounded to 6
      });

      test('should suggest endurance reps for endurance goals', () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'beginner',
          fitnessGoals: ['endurance', 'cardio'],
        );

        final exercise = Exercise(
          id: '2',
          name: 'Burpees',
          primaryMuscle: 'Full Body',
        );

        expect(recommendationService.userProfile?.fitnessGoals,
            contains('endurance'));

        // Test rep calculation for endurance goals
        const recentReps = [12, 15, 14, 16];
        final maxReps = recentReps.reduce((a, b) => a > b ? a : b);
        final suggestedReps = (maxReps * 1.2).round().clamp(12, 20);

        expect(maxReps, equals(16));
        expect(suggestedReps, equals(19)); // 16 * 1.2 = 19.2, rounded to 19
      });

      test('should suggest general fitness reps for no specific goals', () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'beginner',
          fitnessGoals: [],
        );

        final exercise = Exercise(
          id: '3',
          name: 'Push-ups',
          primaryMuscle: 'Chest',
        );

        expect(recommendationService.userProfile?.fitnessGoals, isEmpty);

        // Test rep calculation for general fitness
        const recentReps = [10, 12, 11, 10];
        final avgReps = recentReps.reduce((a, b) => a + b) / recentReps.length;
        final suggestedReps = avgReps.round().clamp(8, 12);

        expect(avgReps, equals(10.75));
        expect(suggestedReps, equals(11));
      });

      test('should handle empty rep history gracefully', () {
        const List<int> recentReps = [];

        // Should fall back to profile-based suggestions when no logs exist
        expect(recentReps.isEmpty, isTrue);
      });
    });

    group('Exercise Alternatives', () {
      test('should generate alternatives for chest exercises', () {
        final exercise = Exercise(
          id: '1',
          name: 'Push-ups',
          primaryMuscle: 'Chest',
          equipment: 'None',
        );

        // Test that chest exercises have alternatives
        expect(exercise.primaryMuscle, equals('Chest'));

        // Test alternative generation logic
        final primaryMuscle = exercise.primaryMuscle?.toLowerCase();
        expect(primaryMuscle, equals('chest'));

        // Should generate easier and harder variations
        final expectedAlternatives = ['Incline Push-ups', 'Diamond Push-ups'];
        expect(expectedAlternatives.length, equals(2));
      });

      test('should generate alternatives for back exercises', () {
        final exercise = Exercise(
          id: '2',
          name: 'Pull-ups',
          primaryMuscle: 'Back',
          equipment: 'Pull-up Bar',
        );

        expect(exercise.primaryMuscle, equals('Back'));

        // Test alternative generation for back exercises
        final primaryMuscle = exercise.primaryMuscle?.toLowerCase();
        expect(primaryMuscle, equals('back'));
      });

      test('should generate generic alternatives for other muscle groups', () {
        final exercise = Exercise(
          id: '3',
          name: 'Shoulder Press',
          primaryMuscle: 'Shoulders',
          equipment: 'Dumbbells',
        );

        expect(exercise.primaryMuscle, equals('Shoulders'));

        // Should generate at least one generic alternative
        final primaryMuscle = exercise.primaryMuscle?.toLowerCase();
        expect(primaryMuscle, equals('shoulders'));
      });

      test('should estimate exercise difficulty correctly', () {
        // Test bodyweight exercise (easier)
        final bodyweightExercise = Exercise(
          id: '1',
          name: 'Push-ups',
          equipment: 'None',
          primaryMuscle: 'Chest',
        );
        expect(bodyweightExercise.equipment?.toLowerCase(), equals('none'));

        // Test machine exercise (easier than free weights)
        final machineExercise = Exercise(
          id: '2',
          name: 'Chest Press Machine',
          equipment: 'Machine',
          primaryMuscle: 'Chest',
        );
        expect(machineExercise.equipment?.toLowerCase(), equals('machine'));

        // Test barbell exercise (harder)
        final barbellExercise = Exercise(
          id: '3',
          name: 'Barbell Bench Press',
          equipment: 'Barbell',
          primaryMuscle: 'Chest',
        );
        expect(barbellExercise.equipment?.toLowerCase(), equals('barbell'));
      });

      test('should provide appropriate alternative reasons', () {
        // Test difficulty-based reasoning
        const reasons = {
          'easier': 'Easier variation to build up strength',
          'harder': 'Advanced variation for extra challenge',
          'similar': 'Similar difficulty with different movement pattern',
        };

        expect(reasons['easier'], contains('Easier'));
        expect(reasons['harder'], contains('Advanced'));
        expect(reasons['similar'], contains('Similar'));
      });
    });

    group('Difficulty Adjustments', () {
      test('should provide beginner-appropriate adjustments', () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'beginner',
        );

        expect(recommendationService.userProfile?.fitnessLevel,
            equals('beginner'));

        // Test beginner adjustment content
        final expectedBeginnerTips = [
          'Focus on proper form over speed or weight',
          'Take longer rest periods (60-90 seconds) between sets',
          'Start with fewer sets and gradually increase',
        ];

        expect(expectedBeginnerTips.length, equals(3));
        expect(expectedBeginnerTips.first, contains('proper form'));
      });

      test('should provide intermediate-level adjustments', () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'intermediate',
        );

        expect(recommendationService.userProfile?.fitnessLevel,
            equals('intermediate'));

        // Test intermediate adjustment content
        final expectedIntermediateTips = [
          'Try increasing weight by 5-10% when you can complete all reps easily',
          'Consider adding drop sets or supersets for intensity',
        ];

        expect(expectedIntermediateTips.length, equals(2));
        expect(expectedIntermediateTips.first, contains('increasing weight'));
      });

      test('should provide advanced-level adjustments', () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'advanced',
        );

        expect(recommendationService.userProfile?.fitnessLevel,
            equals('advanced'));

        // Test advanced adjustment content
        final expectedAdvancedTips = [
          'Experiment with tempo variations (slow negatives)',
          'Try advanced techniques like rest-pause or cluster sets',
        ];

        expect(expectedAdvancedTips.length, equals(2));
        expect(expectedAdvancedTips.first, contains('tempo variations'));
      });

      test('should handle null fitness level gracefully', () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          // No fitness level provided
        );

        expect(recommendationService.userProfile?.fitnessLevel, isNull);

        // Should default to beginner adjustments when level is null
        const defaultLevel = 'beginner';
        expect(defaultLevel, equals('beginner'));
      });
    });

    group('User Profile Integration', () {
      test('should handle null user profile gracefully', () {
        recommendationService.userProfile = null;

        expect(recommendationService.userProfile, isNull);
      });

      test('should use default values when profile data is missing', () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          // No fitness level or goals provided
        );

        expect(recommendationService.userProfile?.fitnessLevel, isNull);
        expect(recommendationService.userProfile?.fitnessGoals, isNull);
      });

      test('should handle empty fitness goals list', () {
        recommendationService.userProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'beginner',
          fitnessGoals: [],
        );

        expect(recommendationService.userProfile?.fitnessGoals, isEmpty);
      });
    });

    group('Progressive Overload Calculations', () {
      test('should calculate appropriate weight progression', () {
        const recentWeights = [100.0, 105.0, 105.0, 110.0, 110.0];
        final maxWeight = recentWeights.reduce((a, b) => a > b ? a : b);
        final suggestedWeight = maxWeight * 1.025; // 2.5% increase

        expect(maxWeight, equals(110.0));
        expect(suggestedWeight, closeTo(112.75, 0.1));
      });

      test('should handle plateau detection', () {
        // Same weight for multiple sessions indicates plateau
        const recentWeights = [100.0, 100.0, 100.0, 100.0];
        final isPlateaued =
            recentWeights.every((weight) => weight == recentWeights.first);

        expect(isPlateaued, isTrue);

        // Should suggest slight increase to break plateau
        final plateauBreaker = recentWeights.first * 1.025;
        expect(plateauBreaker, closeTo(102.5, 0.1));
      });

      test('should suggest deload when performance drops', () {
        const recentWeights = [110.0, 105.0, 100.0, 95.0];
        final isDecreasing = recentWeights.first > recentWeights.last;

        expect(isDecreasing, isTrue);

        // Should suggest returning to previous successful weight
        final deloadWeight = recentWeights.first * 0.9; // 10% deload
        expect(deloadWeight, equals(99.0));
      });
    });

    group('Exercise Difficulty Assessment', () {
      test('should correctly assess bodyweight exercise difficulty', () {
        final pushUp = Exercise(
          id: '1',
          name: 'Push-ups',
          equipment: 'None',
          primaryMuscle: 'Chest',
        );

        final inclinePushUp = Exercise(
          id: '2',
          name: 'Incline Push-ups',
          equipment: 'None',
          primaryMuscle: 'Chest',
        );

        final diamondPushUp = Exercise(
          id: '3',
          name: 'Diamond Push-ups',
          equipment: 'None',
          primaryMuscle: 'Chest',
        );

        // Test difficulty assessment logic
        expect(pushUp.equipment, equals('None'));
        expect(inclinePushUp.name.toLowerCase(), contains('incline'));
        expect(diamondPushUp.name.toLowerCase(), contains('diamond'));
      });

      test('should correctly assess equipment-based difficulty', () {
        final machinePress = Exercise(
          id: '1',
          name: 'Chest Press Machine',
          equipment: 'Machine',
          primaryMuscle: 'Chest',
        );

        final dumbbellPress = Exercise(
          id: '2',
          name: 'Dumbbell Bench Press',
          equipment: 'Dumbbells',
          primaryMuscle: 'Chest',
        );

        final barbellPress = Exercise(
          id: '3',
          name: 'Barbell Bench Press',
          equipment: 'Barbell',
          primaryMuscle: 'Chest',
        );

        // Machine < Dumbbell < Barbell in difficulty
        expect(machinePress.equipment?.toLowerCase(), contains('machine'));
        expect(dumbbellPress.equipment?.toLowerCase(), contains('dumbbell'));
        expect(barbellPress.equipment?.toLowerCase(), contains('barbell'));
      });
    });

    group('Personalization Algorithms', () {
      test('should adapt suggestions based on user goals', () {
        // Strength goal user
        final strengthUser = UserProfile(
          userId: 'test1',
          fitnessLevel: 'intermediate',
          fitnessGoals: ['strength'],
        );

        // Endurance goal user
        final enduranceUser = UserProfile(
          userId: 'test2',
          fitnessLevel: 'intermediate',
          fitnessGoals: ['endurance'],
        );

        expect(strengthUser.fitnessGoals, contains('strength'));
        expect(enduranceUser.fitnessGoals, contains('endurance'));

        // Test rep range adaptation
        const baseReps = 10;
        final strengthReps =
            (baseReps * 0.8).round(); // Lower reps for strength
        final enduranceReps =
            (baseReps * 1.5).round(); // Higher reps for endurance

        expect(strengthReps, equals(8));
        expect(enduranceReps, equals(15));
      });

      test('should consider user fitness level in suggestions', () {
        final beginnerProfile = UserProfile(
          userId: 'test1',
          fitnessLevel: 'beginner',
        );

        final advancedProfile = UserProfile(
          userId: 'test2',
          fitnessLevel: 'advanced',
        );

        expect(beginnerProfile.fitnessLevel, equals('beginner'));
        expect(advancedProfile.fitnessLevel, equals('advanced'));

        // Test weight multipliers based on level
        const baseWeight = 100.0;
        final beginnerMultiplier = 0.7; // Conservative for beginners
        final advancedMultiplier = 1.2; // Aggressive for advanced

        expect(baseWeight * beginnerMultiplier, equals(70.0));
        expect(baseWeight * advancedMultiplier, equals(120.0));
      });

      test('should handle missing workout set logs gracefully', () {
        // When no logs exist, should fall back to profile-based suggestions
        const List<Map<String, dynamic>> emptyLogs = [];

        expect(emptyLogs.isEmpty, isTrue);

        // Should use profile data as fallback
        final fallbackProfile = UserProfile(
          userId: 'test',
          fitnessLevel: 'beginner',
          fitnessGoals: ['general fitness'],
        );

        expect(fallbackProfile.fitnessLevel, isNotNull);
        expect(fallbackProfile.fitnessGoals, isNotEmpty);
      });
    });

    group('Performance Suggestion Models', () {
      test('should create performance suggestions with numeric values', () {
        final weightSuggestion = PerformanceSuggestion(
          value: '100 lbs',
          reason: 'Progressive overload from your recent best',
          numericValue: 100.0,
        );

        expect(weightSuggestion.value, equals('100 lbs'));
        expect(weightSuggestion.numericValue, equals(100.0));
        expect(weightSuggestion.reason, contains('Progressive overload'));
      });

      test('should handle suggestions without numeric values', () {
        final rangeSuggestion = PerformanceSuggestion(
          value: '8-12 reps',
          reason: 'Good for muscle building',
        );

        expect(rangeSuggestion.value, equals('8-12 reps'));
        expect(rangeSuggestion.numericValue, isNull);
      });
    });

    group('Exercise Difficulty Enum', () {
      test('should provide correct labels and colors', () {
        expect(ExerciseDifficulty.easier.label, equals('Easier'));
        expect(ExerciseDifficulty.similar.label, equals('Similar'));
        expect(ExerciseDifficulty.harder.label, equals('Harder'));

        // Colors should be different for each difficulty
        expect(ExerciseDifficulty.easier.color,
            isNot(equals(ExerciseDifficulty.harder.color)));
        expect(ExerciseDifficulty.similar.color,
            isNot(equals(ExerciseDifficulty.easier.color)));
      });

      test('should provide appropriate icons', () {
        expect(ExerciseDifficulty.easier.icon, equals(Icons.trending_down));
        expect(ExerciseDifficulty.similar.icon, equals(Icons.swap_horiz));
        expect(ExerciseDifficulty.harder.icon, equals(Icons.trending_up));
      });
    });
  });
}
