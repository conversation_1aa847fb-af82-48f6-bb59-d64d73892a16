import 'package:flutter_test/flutter_test.dart';
import 'package:openfitv2/services/workout_filter_service.dart';
import 'package:openfitv2/models/workout.dart';
import 'package:openfitv2/models/exercise.dart';

void main() {
  group('WorkoutFilterService Tests', () {
    late WorkoutFilterService filterService;
    late List<Workout> testWorkouts;

    setUp(() {
      filterService = WorkoutFilterService();
      testWorkouts = _createTestWorkouts();
      filterService.setWorkouts(testWorkouts);
    });

    group('Filter Criteria Management', () {
      test('initial criteria should be empty', () {
        final criteria = filterService.criteria;
        expect(criteria.hasActiveFilters, false);
        expect(criteria.activeFilterCount, 0);
        expect(criteria.searchQuery, '');
        expect(criteria.muscleGroups, isEmpty);
        expect(criteria.equipment, isEmpty);
        expect(criteria.difficulties, isEmpty);
        expect(criteria.categories, isEmpty);
      });

      test('should update muscle group filters correctly', () {
        filterService.toggleMuscleGroup('Chest');
        expect(filterService.criteria.muscleGroups.contains('Chest'), true);
        expect(filterService.criteria.hasActiveFilters, true);
        expect(filterService.criteria.activeFilterCount, 1);

        // Toggle again to remove
        filterService.toggleMuscleGroup('Chest');
        expect(filterService.criteria.muscleGroups.contains('Chest'), false);
        expect(filterService.criteria.hasActiveFilters, false);
      });

      test('should update equipment filters correctly', () {
        filterService.toggleEquipment('Dumbbells');
        expect(filterService.criteria.equipment.contains('Dumbbells'), true);
        expect(filterService.criteria.hasActiveFilters, true);

        filterService.toggleEquipment('Dumbbells');
        expect(filterService.criteria.equipment.contains('Dumbbells'), false);
      });

      test('should update difficulty filters correctly', () {
        filterService.toggleDifficulty(WorkoutDifficulty.beginner);
        expect(
            filterService.criteria.difficulties
                .contains(WorkoutDifficulty.beginner),
            true);
        expect(filterService.criteria.hasActiveFilters, true);

        filterService.toggleDifficulty(WorkoutDifficulty.beginner);
        expect(
            filterService.criteria.difficulties
                .contains(WorkoutDifficulty.beginner),
            false);
      });

      test('should update category filters correctly', () {
        filterService.toggleCategory(WorkoutCategory.strength);
        expect(
            filterService.criteria.categories
                .contains(WorkoutCategory.strength),
            true);
        expect(filterService.criteria.hasActiveFilters, true);

        filterService.toggleCategory(WorkoutCategory.strength);
        expect(
            filterService.criteria.categories
                .contains(WorkoutCategory.strength),
            false);
      });

      test('should update search query correctly', () {
        filterService.updateSearchQuery('Push');
        expect(filterService.criteria.searchQuery, 'Push');
        expect(filterService.criteria.hasActiveFilters, true);

        filterService.updateSearchQuery('');
        expect(filterService.criteria.searchQuery, '');
        expect(filterService.criteria.hasActiveFilters, false);
      });

      test('should set duration range correctly', () {
        filterService.setDurationRange(15, 30);
        expect(filterService.criteria.minDuration, 15);
        expect(filterService.criteria.maxDuration, 30);
        expect(filterService.criteria.hasActiveFilters, true);

        filterService.setDurationRange(null, null);
        expect(filterService.criteria.minDuration, null);
        expect(filterService.criteria.maxDuration, null);
        expect(filterService.criteria.hasActiveFilters, false);
      });

      test('should clear all filters correctly', () {
        // Apply multiple filters
        filterService.toggleMuscleGroup('Chest');
        filterService.toggleEquipment('Dumbbells');
        filterService.updateSearchQuery('test');
        filterService.setDurationRange(15, 30);

        expect(filterService.criteria.hasActiveFilters, true);
        expect(filterService.criteria.activeFilterCount, 4);

        // Clear all
        filterService.clearFilters();
        expect(filterService.criteria.hasActiveFilters, false);
        expect(filterService.criteria.activeFilterCount, 0);
      });
    });

    group('Workout Filtering', () {
      test('should return all workouts when no filters are applied', () {
        expect(filterService.filteredWorkouts.length, testWorkouts.length);
      });

      test('should filter by muscle group correctly', () {
        filterService.toggleMuscleGroup('Chest');
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 2); // Both test workouts target chest
        expect(filtered.every((w) => w.primaryMuscles.contains('Chest')), true);
      });

      test('should filter by equipment correctly', () {
        filterService.toggleEquipment('Dumbbells');
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 1);
        expect(filtered.first.name, 'Dumbbell Chest Workout');
      });

      test('should filter by bodyweight correctly', () {
        filterService.toggleEquipment('Bodyweight');
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 1);
        expect(filtered.first.name, 'Push Up Workout');
      });

      test('should filter by search query - workout name', () {
        filterService.updateSearchQuery('Push');
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 1);
        expect(filtered.first.name, 'Push Up Workout');
      });

      test('should filter by search query - exercise name', () {
        filterService.updateSearchQuery('Dumbbell Press');
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 1);
        expect(filtered.first.name, 'Dumbbell Chest Workout');
      });

      test('should filter by search query - case insensitive', () {
        filterService.updateSearchQuery('push');
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 1);
        expect(filtered.first.name, 'Push Up Workout');
      });

      test('should filter by difficulty correctly', () {
        filterService.toggleDifficulty(WorkoutDifficulty.beginner);
        final filtered = filterService.filteredWorkouts;

        // Both test workouts should be classified as beginner (3 exercises, 9 total sets)
        expect(filtered.length, 2);
      });

      test('should apply multiple filters correctly', () {
        filterService.toggleMuscleGroup('Chest');
        filterService.toggleEquipment('Dumbbells');
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 1);
        expect(filtered.first.name, 'Dumbbell Chest Workout');
      });

      test('should return empty list when no workouts match filters', () {
        filterService.toggleMuscleGroup('Legs'); // No leg workouts in test data
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 0);
      });

      test('should handle duration filtering correctly', () {
        // Set a very short duration range
        filterService.setDurationRange(1, 2);
        final filtered = filterService.filteredWorkouts;

        // Both workouts should be filtered out as they're estimated to be longer
        expect(filtered.length, 0);
      });
    });

    group('Search Suggestions', () {
      test('should generate search suggestions from workout data', () {
        final suggestions = filterService.searchSuggestions;

        expect(suggestions.contains('Push Up Workout'), true);
        expect(suggestions.contains('Dumbbell Chest Workout'), true);
        expect(suggestions.contains('Push Up'), true);
        expect(suggestions.contains('Dumbbell Press'), true);
        expect(suggestions.contains('Chest'), true);
        expect(suggestions.contains('Dumbbells'), true);
      });

      test('should return filtered suggestions based on query', () {
        final suggestions = filterService.getSearchSuggestions('Push');

        expect(suggestions.contains('Push Up Workout'), true);
        expect(suggestions.contains('Push Up'), true);
        expect(suggestions.contains('Dumbbell Chest Workout'), false);
      });

      test('should return empty suggestions for empty query', () {
        final suggestions = filterService.getSearchSuggestions('');
        expect(suggestions, isEmpty);
      });

      test('should limit suggestions to 5 items', () {
        final suggestions =
            filterService.getSearchSuggestions('e'); // Should match many items
        expect(suggestions.length, lessThanOrEqualTo(5));
      });
    });

    group('Workout Classification', () {
      test('should estimate workout difficulty correctly', () {
        // Create a complex workout
        final complexWorkout = _createComplexWorkout();
        filterService.setWorkouts([complexWorkout]);

        filterService.toggleDifficulty(WorkoutDifficulty.advanced);
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 1);
      });

      test('should estimate workout category correctly', () {
        filterService.toggleCategory(WorkoutCategory.strength);
        final filtered = filterService.filteredWorkouts;

        // At least one workout should be classified as strength
        expect(filtered.length, greaterThan(0));
      });

      test('should classify bodyweight workouts correctly', () {
        filterService.toggleCategory(WorkoutCategory.bodyweight);
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 1);
        expect(filtered.first.name, 'Push Up Workout');
      });
    });

    group('Relevance Scoring', () {
      test('should prioritize exact name matches', () {
        filterService.updateSearchQuery('Push Up Workout');
        final filtered = filterService.filteredWorkouts;

        expect(filtered.length, 1);
        expect(filtered.first.name, 'Push Up Workout');
      });

      test('should sort results by relevance', () {
        // Add a workout with "Push" in description but not name
        final workouts = List<Workout>.from(testWorkouts);
        workouts.add(
            _createWorkoutWithDescription('Chest Workout', 'Push your limits'));
        filterService.setWorkouts(workouts);

        filterService.updateSearchQuery('Push');
        final filtered = filterService.filteredWorkouts;

        // "Push Up Workout" should come first (name match)
        expect(filtered.first.name, 'Push Up Workout');
      });
    });
  });
}

List<Workout> _createTestWorkouts() {
  final exercise1 = Exercise(
    id: '1',
    name: 'Push Up',
    description: 'Basic push up exercise',
    primaryMuscle: 'Chest',
    equipment: null,
    category: 'Strength',
    instructions: 'Get in plank position, Lower body, Push up',
  );

  final exercise2 = Exercise(
    id: '2',
    name: 'Dumbbell Press',
    description: 'Chest press with dumbbells',
    primaryMuscle: 'Chest',
    equipment: 'Dumbbells',
    category: 'Strength',
    instructions: 'Lie on bench, Press dumbbells up',
  );

  final workoutExercise1 = WorkoutExercise(
    id: '1',
    workoutId: '1',
    exerciseId: '1',
    exercise: exercise1,
    sets: 3,
    reps: [10, 10, 10],
    orderIndex: 0,
    name: 'Push Up',
    completed: false,
  );

  final workoutExercise2 = WorkoutExercise(
    id: '2',
    workoutId: '2',
    exerciseId: '2',
    exercise: exercise2,
    sets: 3,
    reps: [12, 12, 12],
    orderIndex: 0,
    name: 'Dumbbell Press',
    completed: false,
  );

  return [
    Workout(
      id: '1',
      userId: 'user1',
      name: 'Push Up Workout',
      aiDescription: 'A bodyweight chest workout',
      createdAt: DateTime.now(),
      exercises: [workoutExercise1],
    ),
    Workout(
      id: '2',
      userId: 'user1',
      name: 'Dumbbell Chest Workout',
      aiDescription: 'A dumbbell-based chest workout',
      createdAt: DateTime.now(),
      exercises: [workoutExercise2],
    ),
  ];
}

Workout _createComplexWorkout() {
  final exercises = List.generate(8, (index) {
    final exercise = Exercise(
      id: 'ex$index',
      name: 'Exercise $index',
      description: 'Complex exercise $index',
      primaryMuscle: 'Chest',
      equipment: 'Dumbbells',
      category: 'Strength',
      instructions: 'Step 1, Step 2',
    );

    return WorkoutExercise(
      id: 'we$index',
      workoutId: 'complex',
      exerciseId: 'ex$index',
      exercise: exercise,
      sets: 4,
      reps: [15, 15, 15, 15],
      orderIndex: index,
      name: 'Exercise $index',
      completed: false,
    );
  });

  return Workout(
    id: 'complex',
    userId: 'user1',
    name: 'Complex Workout',
    aiDescription: 'A complex advanced workout',
    createdAt: DateTime.now(),
    exercises: exercises,
  );
}

Workout _createWorkoutWithDescription(String name, String description) {
  final exercise = Exercise(
    id: '3',
    name: 'Bench Press',
    description: 'Chest exercise',
    primaryMuscle: 'Chest',
    equipment: 'Barbell',
    category: 'Strength',
    instructions: 'Lie on bench, Press bar up',
  );

  final workoutExercise = WorkoutExercise(
    id: '3',
    workoutId: '3',
    exerciseId: '3',
    exercise: exercise,
    sets: 3,
    reps: [10, 10, 10],
    orderIndex: 0,
    name: 'Bench Press',
    completed: false,
  );

  return Workout(
    id: '3',
    userId: 'user1',
    name: name,
    aiDescription: description,
    createdAt: DateTime.now(),
    exercises: [workoutExercise],
  );
}
