import 'package:flutter/material.dart';
import 'app_spacing.dart';

/// ThemeExtension that exposes spacing scale via Theme.of(context).
@immutable
class SpacingTokens extends ThemeExtension<SpacingTokens> {
  final double xs;
  final double sm;
  final double md;
  final double lg;
  final double xl;
  final double xxl;
  final double xxxl;

  const SpacingTokens({
    this.xs = AppSpacing.xs,
    this.sm = AppSpacing.sm,
    this.md = AppSpacing.md,
    this.lg = AppSpacing.lg,
    this.xl = AppSpacing.xl,
    this.xxl = AppSpacing.xxl,
    this.xxxl = AppSpacing.xxxl,
  });

  // Convenience getters so you can write `spacing.md`.
  double get cardPadding => AppSpacing.cardPadding;
  double get screenPadding => AppSpacing.screenPadding;
  double get sectionSpacing => AppSpacing.sectionSpacing;
  double get itemSpacing => AppSpacing.itemSpacing;
  double get textSpacing => AppSpacing.textSpacing;

  @override
  SpacingTokens copyWith({
    double? xs,
    double? sm,
    double? md,
    double? lg,
    double? xl,
    double? xxl,
    double? xxxl,
  }) {
    return SpacingTokens(
      xs: xs ?? this.xs,
      sm: sm ?? this.sm,
      md: md ?? this.md,
      lg: lg ?? this.lg,
      xl: xl ?? this.xl,
      xxl: xxl ?? this.xxl,
      xxxl: xxxl ?? this.xxxl,
    );
  }

  @override
  SpacingTokens lerp(ThemeExtension<SpacingTokens>? other, double t) => this;
}

/// ThemeExtension that exposes typography styles via Theme.of(context).
@immutable
class TypographyTokens extends ThemeExtension<TypographyTokens> {
  final TextTheme textTheme;

  const TypographyTokens({required this.textTheme});

  // Quick access helpers
  TextStyle get display1 => textTheme.displayLarge!;
  TextStyle get display2 => textTheme.displayMedium!;
  TextStyle get heading1 => textTheme.headlineLarge!;
  TextStyle get heading2 => textTheme.headlineMedium!;
  TextStyle get heading3 => textTheme.headlineSmall!;
  TextStyle get body1 => textTheme.bodyLarge!;
  TextStyle get body2 => textTheme.bodyMedium!;
  TextStyle get caption => textTheme.bodySmall!;

  @override
  TypographyTokens copyWith({TextTheme? textTheme}) {
    return TypographyTokens(textTheme: textTheme ?? this.textTheme);
  }

  @override
  TypographyTokens lerp(ThemeExtension<TypographyTokens>? other, double t) =>
      this;
}
