import 'package:flutter/material.dart';

/// Design tokens for consistent shadows and elevation throughout the app
class AppShadows {
  // Shadow opacity for light theme
  static const double shadowOpacityLight = 0.08;
  // Shadow opacity for dark theme - enhanced for better depth perception
  static const double shadowOpacityDark = 0.6;

  // Enhanced shadow colors for dark theme
  static const Color darkShadowColor = Color(0xFF000000);
  static const Color lightShadowColor = Color(0xFF000000);

  // Get appropriate shadow for theme
  static List<BoxShadow> getElevation(BuildContext context, int level) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final opacity = isDark ? shadowOpacityDark : shadowOpacityLight;
    final shadowColor = isDark ? darkShadowColor : lightShadowColor;

    switch (level) {
      case 1:
        return [
          BoxShadow(
            color: shadowColor.withValues(alpha: opacity),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ];
      case 2:
        return [
          BoxShadow(
            color: shadowColor.withValues(alpha: opacity),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ];
      case 3:
        return [
          BoxShadow(
            color: shadowColor.withValues(alpha: opacity),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ];
      case 4:
        return [
          BoxShadow(
            color: shadowColor.withValues(alpha: opacity),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ];
      case 5:
        return [
          BoxShadow(
            color: shadowColor.withValues(alpha: opacity),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ];
      default:
        return [];
    }
  }

  // Legacy elevation levels (for backwards compatibility)
  static const List<BoxShadow> elevation1 = [
    BoxShadow(
      color: Color.fromRGBO(0, 0, 0, shadowOpacityLight),
      blurRadius: 4,
      offset: Offset(0, 1),
    ),
  ];

  static const List<BoxShadow> elevation2 = [
    BoxShadow(
      color: Color.fromRGBO(0, 0, 0, shadowOpacityLight),
      blurRadius: 8,
      offset: Offset(0, 2),
    ),
  ];

  static const List<BoxShadow> elevation3 = [
    BoxShadow(
      color: Color.fromRGBO(0, 0, 0, shadowOpacityLight),
      blurRadius: 12,
      offset: Offset(0, 4),
    ),
  ];

  static const List<BoxShadow> elevation4 = [
    BoxShadow(
      color: Color.fromRGBO(0, 0, 0, shadowOpacityLight),
      blurRadius: 16,
      offset: Offset(0, 6),
    ),
  ];

  static const List<BoxShadow> elevation5 = [
    BoxShadow(
      color: Color.fromRGBO(0, 0, 0, shadowOpacityLight),
      blurRadius: 20,
      offset: Offset(0, 8),
    ),
  ];

  // Specific use cases (legacy)
  static const List<BoxShadow> cardShadow = elevation2;
  static const List<BoxShadow> modalShadow = elevation4;
  static const List<BoxShadow> buttonShadow = elevation1;

  // Theme-aware shadows
  static List<BoxShadow> getCardShadow(BuildContext context) =>
      getElevation(context, 2);
  static List<BoxShadow> getModalShadow(BuildContext context) =>
      getElevation(context, 4);
  static List<BoxShadow> getButtonShadow(BuildContext context) =>
      getElevation(context, 1);
}
