import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// Specialized scaffold for workout screens with unique requirements.
/// 
/// Features:
/// * Uses AppWorkoutHeader instead of standard AppBar
/// * Supports Stack-based layouts for modals and overlays
/// * Conditional bottom navigation based on state
/// * Proper safe area handling for workout flows
/// * Consistent background colors from design system
class WorkoutScaffold extends StatelessWidget {
  /// The main body content of the screen
  final Widget body;
  
  /// Optional header trailing widget (e.g., help icon, close button)
  final Widget? headerTrailing;
  
  /// Callback for header close action
  final VoidCallback? onClose;
  
  /// Whether to show the bottom navigation bar
  final bool showBottomNavigation;
  
  /// Current navigation item for bottom nav
  final AppNavigationItem currentNavigationItem;
  
  /// Callback for navigation item taps
  final Function(AppNavigationItem)? onNavigationTap;
  
  /// Optional overlay widgets (e.g., modals, dialogs)
  final List<Widget> overlays;
  
  /// Whether to use safe area for the body
  final bool useSafeArea;
  
  /// Whether the body should be scrollable
  final bool scrollable;
  
  /// Custom padding for the body content
  final EdgeInsets? bodyPadding;

  const WorkoutScaffold({
    super.key,
    required this.body,
    this.headerTrailing,
    this.onClose,
    this.showBottomNavigation = true,
    this.currentNavigationItem = AppNavigationItem.workouts,
    this.onNavigationTap,
    this.overlays = const [],
    this.useSafeArea = true,
    this.scrollable = false,
    this.bodyPadding,
  });

  @override
  Widget build(BuildContext context) {
    Widget bodyContent = body;
    
    // Apply padding if specified
    if (bodyPadding != null) {
      bodyContent = Padding(
        padding: bodyPadding!,
        child: bodyContent,
      );
    }
    
    // Make scrollable if needed
    if (scrollable) {
      bodyContent = SingleChildScrollView(
        child: bodyContent,
      );
    }
    
    // Apply safe area if needed
    if (useSafeArea) {
      bodyContent = SafeArea(
        child: bodyContent,
      );
    }

    return Scaffold(
      backgroundColor: AppColorsTheme.background(context),
      body: Stack(
        children: [
          // Main content with header
          Column(
            children: [
              // Workout header
              AppWorkoutHeader(
                trailing: headerTrailing,
                onClose: onClose,
              ),
              // Body content
              Expanded(
                child: bodyContent,
              ),
            ],
          ),
          // Overlay widgets (modals, dialogs, etc.)
          ...overlays,
        ],
      ),
      bottomNavigationBar: showBottomNavigation
          ? AppBottomNavigation(
              currentItem: currentNavigationItem,
              onItemTapped: onNavigationTap ?? (item) => navigateToItem(context, item),
            )
          : null,
    );
  }
}
