import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// Navigation items for the app
enum AppNavigationItem {
  home,
  workouts,
  progress,
  profile,
}

/// A standardized bottom navigation bar component
class AppBottomNavigation extends StatelessWidget {
  final AppNavigationItem currentItem;
  final Function(AppNavigationItem) onItemTapped;

  const AppBottomNavigation({
    super.key,
    required this.currentItem,
    required this.onItemTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColorsTheme.surface(context),
        border: Border(
          top: BorderSide(
            color: AppColorsTheme.border(context),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Container(
          height: 60, // Fixed height within SafeArea
          padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: AppNavigationItem.values.map((item) {
              final isActive = item == currentItem;
              return _buildNavItem(
                context: context,
                icon: _getIconForItem(item),
                label: _getLabelForItem(item),
                isActive: isActive,
                onTap: () => onItemTapped(item),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        behavior: HitTestBehavior.opaque,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive
                  ? AppColors.primary
                  : AppColorsTheme.textSecondary(context),
              size: 22, // Slightly smaller icon to fit better
            ),
            const SizedBox(height: 2), // Reduced spacing
            Text(
              label,
              style: AppTypography.caption.copyWith(
                fontSize: 11, // Slightly smaller text
                color: isActive
                    ? AppColors.primary
                    : AppColorsTheme.textSecondary(context),
                fontWeight:
                    isActive ? AppTypography.semiBold : AppTypography.medium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconForItem(AppNavigationItem item) {
    switch (item) {
      case AppNavigationItem.home:
        return Icons.home_outlined;
      case AppNavigationItem.workouts:
        return Icons.fitness_center_outlined;
      case AppNavigationItem.progress:
        return Icons.trending_up_outlined;
      case AppNavigationItem.profile:
        return Icons.person_outline;
    }
  }

  String _getLabelForItem(AppNavigationItem item) {
    switch (item) {
      case AppNavigationItem.home:
        return 'Home';
      case AppNavigationItem.workouts:
        return 'Workouts';
      case AppNavigationItem.progress:
        return 'Progress';
      case AppNavigationItem.profile:
        return 'Profile';
    }
  }
}

/// Helper function to navigate to the appropriate screen
void navigateToItem(BuildContext context, AppNavigationItem item) {
  switch (item) {
    case AppNavigationItem.home:
      Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
      break;
    case AppNavigationItem.workouts:
      Navigator.pushNamedAndRemoveUntil(context, '/workouts', (route) => false);
      break;
    case AppNavigationItem.progress:
      Navigator.pushNamedAndRemoveUntil(context, '/progress', (route) => false);
      break;
    case AppNavigationItem.profile:
      Navigator.pushNamedAndRemoveUntil(context, '/profile', (route) => false);
      break;
  }
}
