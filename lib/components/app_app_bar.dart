import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// A standardized AppBar that consumes design-system tokens.
///
/// Usage:
/// ```dart
/// return AppScaffold(
///   title: 'Workouts',
///   actions: [IconButton(icon: Icon(Icons.settings), onPressed: _openSettings)],
///   body: ...,
/// );
/// ```
class AppAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// Optional title shown in the centre (Material 3 default).
  final String? title;

  /// Widgets displayed on the right (trailing) side.
  final List<Widget>? actions;

  /// Whether to show a back button automatically (defaults to using [Navigator.maybePop]).
  final bool canPop;

  const AppAppBar({
    super.key,
    this.title,
    this.actions,
    this.canPop = false,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColorsTheme.surface(context),
      foregroundColor: AppColorsTheme.textPrimary(context),
      elevation: 0,
      centerTitle: true,
      automaticallyImplyLeading: false,
      leading: canPop
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).maybePop(),
            )
          : null,
      title: title != null
          ? Text(
              title!,
              style: Theme.of(context).textTheme.titleMedium,
            )
          : null,
      actions: actions,
    );
  }
}
