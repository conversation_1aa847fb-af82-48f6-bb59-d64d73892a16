import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../services/theme_service.dart';
import '../design_system/design_system.dart';

/// A widget that allows users to toggle between light, dark, and system themes
class ThemeToggle extends StatelessWidget {
  const ThemeToggle({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return AppCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.palette_outlined,
                    size: 20,
                    color: AppColorsTheme.textSecondary(context),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Text(
                    'Theme',
                    style: AppTypography.heading3.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSpacing.lg),
              _buildThemeOption(
                context,
                themeProvider,
                AppThemeMode.light,
                'Light',
                'Light theme with bright colors',
                Icons.light_mode,
              ),
              const SizedBox(height: AppSpacing.md),
              _buildThemeOption(
                context,
                themeProvider,
                AppThemeMode.dark,
                'Dark',
                'Dark theme for low-light environments',
                Icons.dark_mode,
              ),
              const SizedBox(height: AppSpacing.md),
              _buildThemeOption(
                context,
                themeProvider,
                AppThemeMode.system,
                'System',
                'Follow your device settings',
                Icons.settings_brightness,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    ThemeProvider themeProvider,
    AppThemeMode themeMode,
    String title,
    String subtitle,
    IconData icon,
  ) {
    final isSelected = themeProvider.themeMode == themeMode;

    return GestureDetector(
      onTap: () => themeProvider.setThemeMode(themeMode),
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: AppBorderRadius.buttonRadius,
          border: Border.all(
            color: isSelected
                ? AppColors.primary
                : AppColorsTheme.borderLight(context),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primary
                    : AppColorsTheme.surface(context),
                borderRadius: AppBorderRadius.buttonRadius,
                border: !isSelected
                    ? Border.all(
                        color: AppColorsTheme.borderLight(context),
                      )
                    : null,
              ),
              child: Icon(
                icon,
                color: isSelected
                    ? AppColors.textOnPrimary
                    : AppColorsTheme.textSecondary(context),
                size: 20,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.body1.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                      fontWeight: isSelected
                          ? AppTypography.semiBold
                          : AppTypography.regular,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: AppTypography.caption.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}
