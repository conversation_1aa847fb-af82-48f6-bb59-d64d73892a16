import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// Specialized scaffold for authentication screens (login / signup).
/// Centred body, no bottom navigation, custom padding & scroll.
class AuthScaffold extends StatelessWidget {
  final Widget child;
  final String? title;
  final bool canPop;

  const AuthScaffold({
    super.key,
    required this.child,
    this.title,
    this.canPop = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColorsTheme.background(context),
      appBar: title != null || canPop
          ? AppBar(
              title: title != null ? Text(title!) : null,
              backgroundColor: AppColorsTheme.background(context),
              elevation: 0,
              scrolledUnderElevation: 0,
              automaticallyImplyLeading: canPop,
              foregroundColor: AppColorsTheme.textPrimary(context),
            )
          : null,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColorsTheme.background(context),
              AppColorsTheme.surface(context),
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppSpacing.screenPadding),
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 400),
                child: child,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
