import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Utility class to validate color contrast ratios for accessibility
class ContrastValidator {
  /// Calculate the contrast ratio between two colors
  /// Returns a value between 1 and 21, where 21 is the highest contrast
  static double calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);

    final lighter = math.max(luminance1, luminance2);
    final darker = math.min(luminance1, luminance2);

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculate the relative luminance of a color
  static double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent(color.red / 255.0);
    final g = _linearizeColorComponent(color.green / 255.0);
    final b = _linearizeColorComponent(color.blue / 255.0);

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearize a color component for luminance calculation
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return math.pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// Check if contrast ratio meets WCAG AA standard (4.5:1 for normal text)
  static bool meetsWCAGAA(Color foreground, Color background) {
    return calculateContrastRatio(foreground, background) >= 4.5;
  }

  /// Check if contrast ratio meets WCAG AAA standard (7:1 for normal text)
  static bool meetsWCAGAAA(Color foreground, Color background) {
    return calculateContrastRatio(foreground, background) >= 7.0;
  }

  /// Check if contrast ratio meets WCAG AA standard for large text (3:1)
  static bool meetsWCAGAALargeText(Color foreground, Color background) {
    return calculateContrastRatio(foreground, background) >= 3.0;
  }

  /// Validate dark mode colors and return a report
  static Map<String, dynamic> validateDarkModeColors() {
    const darkBackground = Color(0xFF0F0F0F);
    const darkSurface = Color(0xFF1C1C1C);
    const darkCardBackground = Color(0xFF1A1A1A);
    const darkTextPrimary = Color(0xFFFFFFFF);
    const darkTextSecondary = Color(0xFFB8B8B8);
    const primary = Color(0xFF6366F1);

    final results = <String, dynamic>{};

    // Test primary text on dark background
    final primaryTextRatio =
        calculateContrastRatio(darkTextPrimary, darkBackground);
    results['Primary text on dark background'] = {
      'ratio': primaryTextRatio.toStringAsFixed(2),
      'wcag_aa': meetsWCAGAA(darkTextPrimary, darkBackground),
      'wcag_aaa': meetsWCAGAAA(darkTextPrimary, darkBackground),
    };

    // Test secondary text on dark background
    final secondaryTextRatio =
        calculateContrastRatio(darkTextSecondary, darkBackground);
    results['Secondary text on dark background'] = {
      'ratio': secondaryTextRatio.toStringAsFixed(2),
      'wcag_aa': meetsWCAGAA(darkTextSecondary, darkBackground),
      'wcag_aaa': meetsWCAGAAA(darkTextSecondary, darkBackground),
    };

    // Test primary text on card background
    final primaryTextCardRatio =
        calculateContrastRatio(darkTextPrimary, darkCardBackground);
    results['Primary text on card background'] = {
      'ratio': primaryTextCardRatio.toStringAsFixed(2),
      'wcag_aa': meetsWCAGAA(darkTextPrimary, darkCardBackground),
      'wcag_aaa': meetsWCAGAAA(darkTextPrimary, darkCardBackground),
    };

    // Test secondary text on card background
    final secondaryTextCardRatio =
        calculateContrastRatio(darkTextSecondary, darkCardBackground);
    results['Secondary text on card background'] = {
      'ratio': secondaryTextCardRatio.toStringAsFixed(2),
      'wcag_aa': meetsWCAGAA(darkTextSecondary, darkCardBackground),
      'wcag_aaa': meetsWCAGAAA(darkTextSecondary, darkCardBackground),
    };

    // Test primary color on dark background
    final primaryColorRatio = calculateContrastRatio(primary, darkBackground);
    results['Primary color on dark background'] = {
      'ratio': primaryColorRatio.toStringAsFixed(2),
      'wcag_aa': meetsWCAGAA(primary, darkBackground),
      'wcag_aaa': meetsWCAGAAA(primary, darkBackground),
    };

    return results;
  }

  /// Print a formatted contrast report
  static void printContrastReport() {
    final results = validateDarkModeColors();

    print('\n🎨 DARK MODE CONTRAST VALIDATION REPORT');
    print('=' * 50);

    results.forEach((key, value) {
      final ratio = value['ratio'];
      final wcagAA = value['wcag_aa'] ? '✅' : '❌';
      final wcagAAA = value['wcag_aaa'] ? '✅' : '❌';

      print('\n$key:');
      print('  Contrast Ratio: $ratio:1');
      print('  WCAG AA (4.5:1): $wcagAA');
      print('  WCAG AAA (7:1): $wcagAAA');
    });

    print('\n' + '=' * 50);
  }
}
