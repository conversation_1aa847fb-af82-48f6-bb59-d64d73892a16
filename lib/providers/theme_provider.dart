import 'package:flutter/material.dart';
import '../services/theme_service.dart';
import '../design_system/app_colors.dart';
import '../design_system/theme_extensions.dart';

/// Provider for managing app theme state
class ThemeProvider extends ChangeNotifier {
  AppThemeMode _themeMode = AppThemeMode.system;

  AppThemeMode get themeMode => _themeMode;

  // Text themes extracted so they can be re-used in ThemeExtensions
  static const TextTheme _lightTextTheme = TextTheme(
    displayLarge: TextStyle(color: AppColors.lightTextPrimary),
    displayMedium: TextStyle(color: AppColors.lightTextPrimary),
    displaySmall: TextStyle(color: AppColors.lightTextPrimary),
    headlineLarge: TextStyle(color: AppColors.lightTextPrimary),
    headlineMedium: TextStyle(color: AppColors.lightTextPrimary),
    headlineSmall: TextStyle(color: AppColors.lightTextPrimary),
    titleLarge: TextStyle(color: AppColors.lightTextPrimary),
    titleMedium: TextStyle(color: AppColors.lightTextPrimary),
    titleSmall: TextStyle(color: AppColors.lightTextPrimary),
    bodyLarge: TextStyle(color: AppColors.lightTextPrimary),
    bodyMedium: TextStyle(color: AppColors.lightTextPrimary),
    bodySmall: TextStyle(color: AppColors.lightTextSecondary),
    labelLarge: TextStyle(color: AppColors.lightTextPrimary),
    labelMedium: TextStyle(color: AppColors.lightTextSecondary),
    labelSmall: TextStyle(color: AppColors.lightTextSecondary),
  );

  static const TextTheme _darkTextTheme = TextTheme(
    displayLarge: TextStyle(color: AppColors.darkTextPrimary),
    displayMedium: TextStyle(color: AppColors.darkTextPrimary),
    displaySmall: TextStyle(color: AppColors.darkTextPrimary),
    headlineLarge: TextStyle(color: AppColors.darkTextPrimary),
    headlineMedium: TextStyle(color: AppColors.darkTextPrimary),
    headlineSmall: TextStyle(color: AppColors.darkTextPrimary),
    titleLarge: TextStyle(color: AppColors.darkTextPrimary),
    titleMedium: TextStyle(color: AppColors.darkTextPrimary),
    titleSmall: TextStyle(color: AppColors.darkTextPrimary),
    bodyLarge: TextStyle(color: AppColors.darkTextPrimary),
    bodyMedium: TextStyle(color: AppColors.darkTextPrimary),
    bodySmall: TextStyle(color: AppColors.darkTextSecondary),
    labelLarge: TextStyle(color: AppColors.darkTextPrimary),
    labelMedium: TextStyle(color: AppColors.darkTextSecondary),
    labelSmall: TextStyle(color: AppColors.darkTextSecondary),
  );

  /// Initialize the theme provider by loading saved preferences
  Future<void> initialize() async {
    _themeMode = await ThemeService.getThemeMode();
    notifyListeners();
  }

  /// Set the theme mode and save to preferences with smooth transition
  Future<void> setThemeMode(AppThemeMode themeMode) async {
    if (_themeMode == themeMode) return;

    _themeMode = themeMode;
    await ThemeService.setThemeMode(themeMode);
    notifyListeners();
  }

  /// Get the actual ThemeMode for MaterialApp
  ThemeMode getThemeMode(BuildContext context) {
    return ThemeService.getActualThemeMode(_themeMode, context);
  }

  /// Check if current theme is dark
  bool isDarkMode(BuildContext context) {
    return ThemeService.isDarkMode(context, _themeMode);
  }

  /// Get light theme data
  ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
      ).copyWith(
        primary: AppColors.primary,
        secondary: AppColors.primaryLight,
        surface: AppColors.lightSurface,
        error: AppColors.error,
      ),
      scaffoldBackgroundColor: AppColors.lightBackground,
      cardColor: AppColors.lightCardBackground,
      dividerColor: AppColors.lightBorder,
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.lightSurface,
        foregroundColor: AppColors.lightTextPrimary,
        elevation: 0,
        centerTitle: true,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.lightSurface,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.lightTextSecondary,
        type: BottomNavigationBarType.fixed,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
        ),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(color: AppColors.lightTextPrimary),
        displayMedium: TextStyle(color: AppColors.lightTextPrimary),
        displaySmall: TextStyle(color: AppColors.lightTextPrimary),
        headlineLarge: TextStyle(color: AppColors.lightTextPrimary),
        headlineMedium: TextStyle(color: AppColors.lightTextPrimary),
        headlineSmall: TextStyle(color: AppColors.lightTextPrimary),
        titleLarge: TextStyle(color: AppColors.lightTextPrimary),
        titleMedium: TextStyle(color: AppColors.lightTextPrimary),
        titleSmall: TextStyle(color: AppColors.lightTextPrimary),
        bodyLarge: TextStyle(color: AppColors.lightTextPrimary),
        bodyMedium: TextStyle(color: AppColors.lightTextPrimary),
        bodySmall: TextStyle(color: AppColors.lightTextSecondary),
        labelLarge: TextStyle(color: AppColors.lightTextPrimary),
        labelMedium: TextStyle(color: AppColors.lightTextSecondary),
        labelSmall: TextStyle(color: AppColors.lightTextSecondary),
      ),
      extensions: const <ThemeExtension<dynamic>>[
        SpacingTokens(),
        TypographyTokens(textTheme: _lightTextTheme),
      ],
    );
  }

  /// Get dark theme data
  ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.dark,
      ).copyWith(
        primary: AppColors.primary,
        secondary: AppColors.primaryLight,
        surface: AppColors.darkSurface,
        error: AppColors.error,
      ),
      scaffoldBackgroundColor: AppColors.darkBackground,
      cardColor: AppColors.darkCardBackground,
      dividerColor: AppColors.darkBorder,
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.darkSurface,
        foregroundColor: AppColors.darkTextPrimary,
        elevation: 0,
        centerTitle: true,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.darkSurface,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.darkTextSecondary,
        type: BottomNavigationBarType.fixed,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
        ),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(color: AppColors.darkTextPrimary),
        displayMedium: TextStyle(color: AppColors.darkTextPrimary),
        displaySmall: TextStyle(color: AppColors.darkTextPrimary),
        headlineLarge: TextStyle(color: AppColors.darkTextPrimary),
        headlineMedium: TextStyle(color: AppColors.darkTextPrimary),
        headlineSmall: TextStyle(color: AppColors.darkTextPrimary),
        titleLarge: TextStyle(color: AppColors.darkTextPrimary),
        titleMedium: TextStyle(color: AppColors.darkTextPrimary),
        titleSmall: TextStyle(color: AppColors.darkTextPrimary),
        bodyLarge: TextStyle(color: AppColors.darkTextPrimary),
        bodyMedium: TextStyle(color: AppColors.darkTextPrimary),
        bodySmall: TextStyle(color: AppColors.darkTextSecondary),
        labelLarge: TextStyle(color: AppColors.darkTextPrimary),
        labelMedium: TextStyle(color: AppColors.darkTextSecondary),
        labelSmall: TextStyle(color: AppColors.darkTextSecondary),
      ),
      extensions: const <ThemeExtension<dynamic>>[
        SpacingTokens(),
        TypographyTokens(textTheme: _darkTextTheme),
      ],
    );
  }
}
