import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/offline_workout_session.dart';

class OfflineService extends ChangeNotifier {
  static Database? _database;
  static const String _databaseName = 'openfit_offline.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String _sessionsTable = 'offline_sessions';
  static const String _setsTable = 'offline_sets';

  // Initialize database
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, _databaseName);

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createTables,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _createTables(Database db, int version) async {
    // Create offline sessions table
    await db.execute('''
      CREATE TABLE $_sessionsTable (
        session_id TEXT PRIMARY KEY,
        workout_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT,
        session_metadata TEXT,
        sync_status TEXT NOT NULL DEFAULT 'pending',
        last_modified TEXT NOT NULL,
        duration INTEGER,
        notes TEXT,
        rating INTEGER,
        exercise_order TEXT,
        last_state TEXT,
        retry_count INTEGER DEFAULT 0,
        last_sync_attempt TEXT,
        sync_error TEXT
      )
    ''');

    // Create offline sets table
    await db.execute('''
      CREATE TABLE $_setsTable (
        set_id TEXT PRIMARY KEY,
        session_id TEXT NOT NULL,
        exercise_id TEXT NOT NULL,
        exercise_name TEXT NOT NULL,
        set_number INTEGER NOT NULL,
        weight REAL NOT NULL,
        reps INTEGER NOT NULL,
        completed_at TEXT NOT NULL,
        rest_time_seconds INTEGER DEFAULT 0,
        difficulty_rating INTEGER,
        notes TEXT,
        metadata TEXT,
        FOREIGN KEY (session_id) REFERENCES $_sessionsTable (session_id) ON DELETE CASCADE
      )
    ''');

    // Create indexes for better performance
    await db.execute(
        'CREATE INDEX idx_sessions_sync_status ON $_sessionsTable (sync_status)');
    await db.execute(
        'CREATE INDEX idx_sessions_user_id ON $_sessionsTable (user_id)');
    await db.execute(
        'CREATE INDEX idx_sets_session_id ON $_setsTable (session_id)');
    await db.execute(
        'CREATE INDEX idx_sets_exercise_id ON $_setsTable (exercise_id)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database schema upgrades here
    if (oldVersion < newVersion) {
      // Add migration logic for future versions
      debugPrint('Upgrading database from version $oldVersion to $newVersion');
    }
  }

  // Session management methods
  Future<String> createSession({
    required String workoutId,
    required String userId,
    Map<String, dynamic>? metadata,
    List<String>? exerciseOrder,
  }) async {
    final db = await database;
    final sessionId = 'offline_${DateTime.now().millisecondsSinceEpoch}';

    final session = OfflineWorkoutSession(
      sessionId: sessionId,
      workoutId: workoutId,
      userId: userId,
      startTime: DateTime.now(),
      sessionMetadata: metadata ?? {},
      exerciseOrder: exerciseOrder ?? [],
      lastModified: DateTime.now(),
    );

    await db.insert(_sessionsTable, _sessionToMap(session));
    notifyListeners();

    return sessionId;
  }

  Future<OfflineWorkoutSession?> getSession(String sessionId) async {
    final db = await database;

    final sessionMaps = await db.query(
      _sessionsTable,
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    if (sessionMaps.isEmpty) return null;

    final setMaps = await db.query(
      _setsTable,
      where: 'session_id = ?',
      whereArgs: [sessionId],
      orderBy: 'set_number ASC',
    );

    final sets = setMaps.map((map) => _mapToSet(map)).toList();
    final session = _mapToSession(sessionMaps.first);

    return session.copyWith(completedSets: sets);
  }

  Future<List<OfflineWorkoutSession>> getAllSessions({
    String? userId,
    SyncStatus? syncStatus,
    int? limit,
  }) async {
    final db = await database;

    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (userId != null) {
      whereClause = 'user_id = ?';
      whereArgs.add(userId);
    }

    if (syncStatus != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'sync_status = ?';
      whereArgs.add(syncStatus.name);
    }

    final sessionMaps = await db.query(
      _sessionsTable,
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'start_time DESC',
      limit: limit,
    );

    final sessions = <OfflineWorkoutSession>[];

    for (final sessionMap in sessionMaps) {
      final sessionId = sessionMap['session_id'] as String;
      final setMaps = await db.query(
        _setsTable,
        where: 'session_id = ?',
        whereArgs: [sessionId],
        orderBy: 'set_number ASC',
      );

      final sets = setMaps.map((map) => _mapToSet(map)).toList();
      final session = _mapToSession(sessionMap);
      sessions.add(session.copyWith(completedSets: sets));
    }

    return sessions;
  }

  Future<void> updateSession(OfflineWorkoutSession session) async {
    final db = await database;

    await db.update(
      _sessionsTable,
      _sessionToMap(session),
      where: 'session_id = ?',
      whereArgs: [session.sessionId],
    );

    notifyListeners();
  }

  Future<void> deleteSession(String sessionId) async {
    final db = await database;

    // Delete sets first (foreign key constraint)
    await db.delete(
      _setsTable,
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    // Delete session
    await db.delete(
      _sessionsTable,
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    notifyListeners();
  }

  // Set management methods
  Future<String> addSet({
    required String sessionId,
    required String exerciseId,
    required String exerciseName,
    required int setNumber,
    required double weight,
    required int reps,
    Duration restTime = Duration.zero,
    int? difficultyRating,
    String? notes,
    Map<String, dynamic>? metadata,
  }) async {
    final db = await database;
    final setId = 'set_${DateTime.now().millisecondsSinceEpoch}';

    final setData = OfflineSetData(
      setId: setId,
      exerciseId: exerciseId,
      exerciseName: exerciseName,
      setNumber: setNumber,
      weight: weight,
      reps: reps,
      completedAt: DateTime.now(),
      restTime: restTime,
      difficultyRating: difficultyRating,
      notes: notes,
      metadata: metadata ?? {},
    );

    await db.insert(_setsTable, _setToMap(setData, sessionId));

    // Update session's last modified time
    await db.update(
      _sessionsTable,
      {'last_modified': DateTime.now().toIso8601String()},
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    notifyListeners();
    return setId;
  }

  Future<void> updateSet(String sessionId, OfflineSetData setData) async {
    final db = await database;

    await db.update(
      _setsTable,
      _setToMap(setData, sessionId),
      where: 'set_id = ?',
      whereArgs: [setData.setId],
    );

    // Update session's last modified time
    await db.update(
      _sessionsTable,
      {'last_modified': DateTime.now().toIso8601String()},
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    notifyListeners();
  }

  Future<void> deleteSet(String sessionId, String setId) async {
    final db = await database;

    await db.delete(
      _setsTable,
      where: 'set_id = ?',
      whereArgs: [setId],
    );

    // Update session's last modified time
    await db.update(
      _sessionsTable,
      {'last_modified': DateTime.now().toIso8601String()},
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    notifyListeners();
  }

  // Sync status management
  Future<void> markSessionForSync(String sessionId) async {
    final db = await database;

    await db.update(
      _sessionsTable,
      {
        'sync_status': SyncStatus.pending.name,
        'last_modified': DateTime.now().toIso8601String(),
      },
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    notifyListeners();
  }

  Future<void> markSessionSyncing(String sessionId) async {
    final db = await database;

    await db.update(
      _sessionsTable,
      {
        'sync_status': SyncStatus.syncing.name,
        'last_sync_attempt': DateTime.now().toIso8601String(),
        'last_modified': DateTime.now().toIso8601String(),
      },
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    notifyListeners();
  }

  Future<void> markSessionSynced(String sessionId) async {
    final db = await database;

    await db.update(
      _sessionsTable,
      {
        'sync_status': SyncStatus.synced.name,
        'sync_error': null,
        'last_modified': DateTime.now().toIso8601String(),
      },
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    notifyListeners();
  }

  Future<void> markSessionSyncFailed(String sessionId, String error) async {
    final db = await database;

    // Get current retry count
    final sessionMaps = await db.query(
      _sessionsTable,
      columns: ['retry_count'],
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    final currentRetryCount = sessionMaps.isNotEmpty
        ? (sessionMaps.first['retry_count'] as int? ?? 0)
        : 0;

    await db.update(
      _sessionsTable,
      {
        'sync_status': SyncStatus.failed.name,
        'sync_error': error,
        'retry_count': currentRetryCount + 1,
        'last_sync_attempt': DateTime.now().toIso8601String(),
        'last_modified': DateTime.now().toIso8601String(),
      },
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );

    notifyListeners();
  }

  // Query methods
  Future<List<OfflineWorkoutSession>> getPendingSyncSessions() async {
    return await getAllSessions(syncStatus: SyncStatus.pending);
  }

  Future<List<OfflineWorkoutSession>> getFailedSyncSessions() async {
    return await getAllSessions(syncStatus: SyncStatus.failed);
  }

  Future<OfflineSessionSummary> getSessionSummary({String? userId}) async {
    final db = await database;

    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (userId != null) {
      whereClause = 'user_id = ?';
      whereArgs.add(userId);
    }

    // Get total sessions count
    final totalResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_sessionsTable${whereClause.isNotEmpty ? ' WHERE $whereClause' : ''}',
      whereArgs,
    );
    final totalSessions = totalResult.first['count'] as int;

    // Get pending sync sessions count
    final pendingArgs = List<dynamic>.from(whereArgs);
    String pendingWhere = whereClause;
    if (pendingWhere.isNotEmpty) pendingWhere += ' AND ';
    pendingWhere += 'sync_status = ?';
    pendingArgs.add(SyncStatus.pending.name);

    final pendingResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_sessionsTable WHERE $pendingWhere',
      pendingArgs,
    );
    final pendingSyncSessions = pendingResult.first['count'] as int;

    // Get failed sync sessions count
    final failedArgs = List<dynamic>.from(whereArgs);
    String failedWhere = whereClause;
    if (failedWhere.isNotEmpty) failedWhere += ' AND ';
    failedWhere += 'sync_status = ?';
    failedArgs.add(SyncStatus.failed.name);

    final failedResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_sessionsTable WHERE $failedWhere',
      failedArgs,
    );
    final failedSyncSessions = failedResult.first['count'] as int;

    // Get oldest pending session
    DateTime? oldestPendingSession;
    final oldestArgs = List<dynamic>.from(whereArgs);
    String oldestWhere = whereClause;
    if (oldestWhere.isNotEmpty) oldestWhere += ' AND ';
    oldestWhere += 'sync_status IN (?, ?)';
    oldestArgs.addAll([SyncStatus.pending.name, SyncStatus.failed.name]);

    final oldestResult = await db.query(
      _sessionsTable,
      columns: ['start_time'],
      where: oldestWhere,
      whereArgs: oldestArgs,
      orderBy: 'start_time ASC',
      limit: 1,
    );

    if (oldestResult.isNotEmpty) {
      oldestPendingSession =
          DateTime.parse(oldestResult.first['start_time'] as String);
    }

    // Calculate total offline volume and time
    final sessions = await getAllSessions(userId: userId);
    double totalOfflineVolume = 0.0;
    Duration totalOfflineTime = Duration.zero;

    for (final session in sessions) {
      totalOfflineVolume += session.totalVolume;
      totalOfflineTime += session.sessionDuration;
    }

    return OfflineSessionSummary(
      totalSessions: totalSessions,
      pendingSyncSessions: pendingSyncSessions,
      failedSyncSessions: failedSyncSessions,
      oldestPendingSession: oldestPendingSession,
      totalOfflineVolume: totalOfflineVolume,
      totalOfflineTime: totalOfflineTime,
    );
  }

  // Cleanup methods
  Future<void> cleanupOldSessions({Duration? olderThan}) async {
    final db = await database;
    final cutoffDate =
        DateTime.now().subtract(olderThan ?? const Duration(days: 30));

    // Only delete synced sessions older than cutoff
    await db.delete(
      _sessionsTable,
      where: 'sync_status = ? AND start_time < ?',
      whereArgs: [SyncStatus.synced.name, cutoffDate.toIso8601String()],
    );

    notifyListeners();
  }

  Future<void> clearAllData() async {
    final db = await database;

    await db.delete(_setsTable);
    await db.delete(_sessionsTable);

    notifyListeners();
  }

  // Helper methods for data conversion
  Map<String, dynamic> _sessionToMap(OfflineWorkoutSession session) {
    return {
      'session_id': session.sessionId,
      'workout_id': session.workoutId,
      'user_id': session.userId,
      'start_time': session.startTime.toIso8601String(),
      'end_time': session.endTime?.toIso8601String(),
      'session_metadata': session.sessionMetadata.isNotEmpty
          ? session.sessionMetadata.toString()
          : null,
      'sync_status': session.syncStatus.name,
      'last_modified': session.lastModified.toIso8601String(),
      'duration': session.duration,
      'notes': session.notes,
      'rating': session.rating,
      'exercise_order': session.exerciseOrder.isNotEmpty
          ? session.exerciseOrder.join(',')
          : null,
      'last_state': session.lastState?.toString(),
      'retry_count': session.retryCount,
      'last_sync_attempt': session.lastSyncAttempt?.toIso8601String(),
      'sync_error': session.syncError,
    };
  }

  OfflineWorkoutSession _mapToSession(Map<String, dynamic> map) {
    return OfflineWorkoutSession(
      sessionId: map['session_id'],
      workoutId: map['workout_id'],
      userId: map['user_id'],
      startTime: DateTime.parse(map['start_time']),
      endTime: map['end_time'] != null ? DateTime.parse(map['end_time']) : null,
      sessionMetadata: map['session_metadata'] != null
          ? <String,
              dynamic>{} // Would need proper JSON parsing in real implementation
          : {},
      syncStatus: SyncStatus.fromString(map['sync_status']),
      lastModified: DateTime.parse(map['last_modified']),
      duration: map['duration'],
      notes: map['notes'],
      rating: map['rating'],
      exerciseOrder: map['exercise_order'] != null
          ? (map['exercise_order'] as String).split(',')
          : [],
      lastState: map['last_state'] != null
          ? <String,
              dynamic>{} // Would need proper JSON parsing in real implementation
          : null,
      retryCount: map['retry_count'] ?? 0,
      lastSyncAttempt: map['last_sync_attempt'] != null
          ? DateTime.parse(map['last_sync_attempt'])
          : null,
      syncError: map['sync_error'],
    );
  }

  Map<String, dynamic> _setToMap(OfflineSetData setData, String sessionId) {
    return {
      'set_id': setData.setId,
      'session_id': sessionId,
      'exercise_id': setData.exerciseId,
      'exercise_name': setData.exerciseName,
      'set_number': setData.setNumber,
      'weight': setData.weight,
      'reps': setData.reps,
      'completed_at': setData.completedAt.toIso8601String(),
      'rest_time_seconds': setData.restTime.inSeconds,
      'difficulty_rating': setData.difficultyRating,
      'notes': setData.notes,
      'metadata':
          setData.metadata.isNotEmpty ? setData.metadata.toString() : null,
    };
  }

  OfflineSetData _mapToSet(Map<String, dynamic> map) {
    return OfflineSetData(
      setId: map['set_id'],
      exerciseId: map['exercise_id'],
      exerciseName: map['exercise_name'],
      setNumber: map['set_number'],
      weight: map['weight'],
      reps: map['reps'],
      completedAt: DateTime.parse(map['completed_at']),
      restTime: Duration(seconds: map['rest_time_seconds'] ?? 0),
      difficultyRating: map['difficulty_rating'],
      notes: map['notes'],
      metadata: map['metadata'] != null
          ? <String,
              dynamic>{} // Would need proper JSON parsing in real implementation
          : {},
    );
  }

  // Database management
  Future<void> closeDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  @override
  void dispose() {
    closeDatabase();
    super.dispose();
  }
}
