import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/enhanced_workout.dart';
import '../models/session_analytics.dart';

class ProgressService extends ChangeNotifier {
  final SupabaseClient _supabase;

  ProgressService({SupabaseClient? supabaseClient})
      : _supabase = supabaseClient ?? Supabase.instance.client;

  // Cache for frequently accessed data
  final Map<String, WorkoutProgress> _progressCache = {};
  final Map<String, List<SessionAnalytics>> _sessionCache = {};

  // Analytics calculations
  Future<WorkoutProgress?> getWorkoutProgress(String workoutId) async {
    try {
      // Check cache first
      if (_progressCache.containsKey(workoutId)) {
        return _progressCache[workoutId];
      }

      // Fetch completed workouts for this workout
      final completedWorkoutsResponse = await _supabase
          .from('completed_workouts')
          .select('*, workout_set_logs(*)')
          .eq('workout_id', workoutId)
          .order('completed_at', ascending: true);

      if (completedWorkoutsResponse.isEmpty) {
        return null;
      }

      final completedWorkouts = completedWorkoutsResponse as List<dynamic>;

      // Calculate overall progress metrics
      final totalCompletions = completedWorkouts.length;
      final averageRating = completedWorkouts
              .where((w) => w['rating'] != null)
              .map((w) => w['rating'] as int)
              .fold<double>(0.0, (sum, rating) => sum + rating) /
          completedWorkouts.where((w) => w['rating'] != null).length;

      // Calculate total sets, reps, and volume
      int totalSets = 0;
      int totalReps = 0;
      double totalVolume = 0.0;

      for (final workout in completedWorkouts) {
        final setLogs = workout['workout_set_logs'] as List<dynamic>? ?? [];
        totalSets += setLogs.length;

        for (final setLog in setLogs) {
          final reps = setLog['reps'] as int? ?? 0;
          final weight = (setLog['weight'] as num?)?.toDouble() ?? 0.0;
          totalReps += reps;
          totalVolume += weight * reps;
        }
      }

      // Get exercise-specific progress
      final exerciseProgress =
          await _calculateExerciseProgress(workoutId, completedWorkouts);

      // Get personal records
      final personalRecords =
          await _calculatePersonalRecords(workoutId, completedWorkouts);

      final progress = WorkoutProgress(
        workoutId: workoutId,
        totalCompletions: totalCompletions,
        averageRating: averageRating.isNaN ? 0.0 : averageRating,
        totalSets: totalSets,
        totalReps: totalReps,
        totalVolume: totalVolume,
        firstCompleted: DateTime.parse(completedWorkouts.first['completed_at']),
        lastCompleted: DateTime.parse(completedWorkouts.last['completed_at']),
        exerciseProgress: exerciseProgress,
        personalRecords: personalRecords,
      );

      // Cache the result
      _progressCache[workoutId] = progress;

      return progress;
    } catch (e) {
      debugPrint('Error fetching workout progress: $e');
      return null;
    }
  }

  Future<List<ExerciseProgress>> _calculateExerciseProgress(
    String workoutId,
    List<dynamic> completedWorkouts,
  ) async {
    try {
      // Get all unique exercises from this workout
      final exerciseIds = <String>{};
      final exerciseNames = <String, String>{};

      for (final workout in completedWorkouts) {
        final setLogs = workout['workout_set_logs'] as List<dynamic>? ?? [];
        for (final setLog in setLogs) {
          final exerciseId = setLog['exercise_id'] as String?;
          if (exerciseId != null) {
            exerciseIds.add(exerciseId);
          }
        }
      }

      // Fetch exercise names
      if (exerciseIds.isNotEmpty) {
        final exercisesResponse = await _supabase
            .from('exercises')
            .select('id, name')
            .inFilter('id', exerciseIds.toList());

        for (final exercise in exercisesResponse) {
          exerciseNames[exercise['id']] = exercise['name'];
        }
      }

      final progressList = <ExerciseProgress>[];

      for (final exerciseId in exerciseIds) {
        final exerciseName = exerciseNames[exerciseId] ?? 'Unknown Exercise';

        // Collect all sets for this exercise
        final allSets = <Map<String, dynamic>>[];
        for (final workout in completedWorkouts) {
          final setLogs = workout['workout_set_logs'] as List<dynamic>? ?? [];
          final exerciseSets = setLogs
              .where((set) => set['exercise_id'] == exerciseId)
              .cast<Map<String, dynamic>>();
          allSets.addAll(exerciseSets);
        }

        if (allSets.isEmpty) continue;

        // Calculate metrics
        final totalSets = allSets.length;
        final totalReps = allSets.fold<int>(
            0, (sum, set) => sum + (set['reps'] as int? ?? 0));
        final totalVolume = allSets.fold<double>(0.0, (sum, set) {
          final reps = set['reps'] as int? ?? 0;
          final weight = (set['weight'] as num?)?.toDouble() ?? 0.0;
          return sum + (weight * reps);
        });

        final maxWeight = allSets.fold<double>(0.0, (max, set) {
          final weight = (set['weight'] as num?)?.toDouble() ?? 0.0;
          return weight > max ? weight : max;
        });

        final maxReps = allSets.fold<int>(0, (max, set) {
          final reps = set['reps'] as int? ?? 0;
          return reps > max ? reps : max;
        });

        // Create performance records
        final records = <PerformanceRecord>[];
        for (final workout in completedWorkouts) {
          final workoutDate = DateTime.parse(workout['completed_at']);
          final setLogs = workout['workout_set_logs'] as List<dynamic>? ?? [];
          final exerciseSets = setLogs
              .where((set) => set['exercise_id'] == exerciseId)
              .cast<Map<String, dynamic>>();

          if (exerciseSets.isNotEmpty) {
            // Calculate best performance for this workout session
            final sessionWeight = exerciseSets.fold<double>(0.0, (max, set) {
              final weight = (set['weight'] as num?)?.toDouble() ?? 0.0;
              return weight > max ? weight : max;
            });

            final sessionReps = exerciseSets.fold<int>(
                0, (sum, set) => sum + (set['reps'] as int? ?? 0));
            final sessionVolume = exerciseSets.fold<double>(0.0, (sum, set) {
              final reps = set['reps'] as int? ?? 0;
              final weight = (set['weight'] as num?)?.toDouble() ?? 0.0;
              return sum + (weight * reps);
            });

            records.add(PerformanceRecord(
              date: workoutDate,
              weight: sessionWeight,
              reps: sessionReps,
              volume: sessionVolume,
            ));
          }
        }

        progressList.add(ExerciseProgress(
          exerciseId: exerciseId,
          exerciseName: exerciseName,
          totalSets: totalSets,
          totalReps: totalReps,
          totalVolume: totalVolume,
          maxWeight: maxWeight,
          maxReps: maxReps,
          firstPerformed:
              DateTime.parse(completedWorkouts.first['completed_at']),
          lastPerformed: DateTime.parse(completedWorkouts.last['completed_at']),
          records: records,
        ));
      }

      return progressList;
    } catch (e) {
      debugPrint('Error calculating exercise progress: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>> _calculatePersonalRecords(
    String workoutId,
    List<dynamic> completedWorkouts,
  ) async {
    final personalRecords = <String, dynamic>{};

    try {
      // Get all set logs for personal record detection
      final allSetLogs = <Map<String, dynamic>>[];
      for (final workout in completedWorkouts) {
        final setLogs = workout['workout_set_logs'] as List<dynamic>? ?? [];
        allSetLogs.addAll(setLogs.cast<Map<String, dynamic>>());
      }

      // Group by exercise
      final exerciseGroups = <String, List<Map<String, dynamic>>>{};
      for (final setLog in allSetLogs) {
        final exerciseId = setLog['exercise_id'] as String?;
        if (exerciseId != null) {
          exerciseGroups.putIfAbsent(exerciseId, () => []).add(setLog);
        }
      }

      // Calculate records for each exercise
      for (final entry in exerciseGroups.entries) {
        final exerciseId = entry.key;
        final sets = entry.value;

        // Max weight record
        final maxWeight = sets.fold<double>(0.0, (max, set) {
          final weight = (set['weight'] as num?)?.toDouble() ?? 0.0;
          return weight > max ? weight : max;
        });
        if (maxWeight > 0) {
          personalRecords['max_weight_$exerciseId'] = maxWeight;
        }

        // Max reps record
        final maxReps = sets.fold<int>(0, (max, set) {
          final reps = set['reps'] as int? ?? 0;
          return reps > max ? reps : max;
        });
        if (maxReps > 0) {
          personalRecords['max_reps_$exerciseId'] = maxReps;
        }

        // Max volume record (single set)
        final maxVolume = sets.fold<double>(0.0, (max, set) {
          final reps = set['reps'] as int? ?? 0;
          final weight = (set['weight'] as num?)?.toDouble() ?? 0.0;
          final volume = weight * reps;
          return volume > max ? volume : max;
        });
        if (maxVolume > 0) {
          personalRecords['max_volume_$exerciseId'] = maxVolume;
        }
      }
    } catch (e) {
      debugPrint('Error calculating personal records: $e');
    }

    return personalRecords;
  }

  // Get exercise progress data for suggestions
  Future<List<Map<String, dynamic>>> getExerciseProgress(
    String exerciseId, {
    int limit = 20,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return [];

      final setLogsResponse = await _supabase
          .from('workout_set_logs')
          .select('weight, reps, completed_at')
          .eq('exercise_id', exerciseId)
          .eq('user_id', user.id)
          .order('completed_at', ascending: false)
          .limit(limit);

      return List<Map<String, dynamic>>.from(setLogsResponse);
    } catch (e) {
      debugPrint('Error fetching exercise progress: $e');
      return [];
    }
  }

  // Strength progression analysis
  Future<Map<String, List<double>>> getStrengthProgression(
    String exerciseId, {
    Duration? timeRange,
  }) async {
    try {
      final cutoffDate = timeRange != null
          ? DateTime.now().subtract(timeRange)
          : DateTime.now().subtract(const Duration(days: 365));

      final setLogsResponse = await _supabase
          .from('workout_set_logs')
          .select('weight, reps, completed_at')
          .eq('exercise_id', exerciseId)
          .gte('completed_at', cutoffDate.toIso8601String())
          .order('completed_at', ascending: true);

      final setLogs = setLogsResponse as List<dynamic>;

      final weights = <double>[];
      final volumes = <double>[];
      final dates = <DateTime>[];

      for (final setLog in setLogs) {
        final weight = (setLog['weight'] as num?)?.toDouble() ?? 0.0;
        final reps = setLog['reps'] as int? ?? 0;
        final date = DateTime.parse(setLog['completed_at']);

        weights.add(weight);
        volumes.add(weight * reps);
        dates.add(date);
      }

      return {
        'weights': weights,
        'volumes': volumes,
        'timestamps':
            dates.map((d) => d.millisecondsSinceEpoch.toDouble()).toList(),
      };
    } catch (e) {
      debugPrint('Error fetching strength progression: $e');
      return {};
    }
  }

  // Volume tracking over time
  Future<Map<String, double>> getVolumeTracking(
    String userId, {
    Duration? timeRange,
  }) async {
    try {
      final cutoffDate = timeRange != null
          ? DateTime.now().subtract(timeRange)
          : DateTime.now().subtract(const Duration(days: 90));

      final completedWorkoutsResponse = await _supabase
          .from('completed_workouts')
          .select('completed_at, workout_set_logs(weight, reps)')
          .eq('user_id', userId)
          .gte('completed_at', cutoffDate.toIso8601String())
          .order('completed_at', ascending: true);

      final completedWorkouts = completedWorkoutsResponse as List<dynamic>;
      final volumeByDate = <String, double>{};

      for (final workout in completedWorkouts) {
        final date = DateTime.parse(workout['completed_at']);
        final dateKey =
            '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

        final setLogs = workout['workout_set_logs'] as List<dynamic>? ?? [];
        final workoutVolume = setLogs.fold<double>(0.0, (sum, setLog) {
          final weight = (setLog['weight'] as num?)?.toDouble() ?? 0.0;
          final reps = setLog['reps'] as int? ?? 0;
          return sum + (weight * reps);
        });

        volumeByDate[dateKey] = (volumeByDate[dateKey] ?? 0.0) + workoutVolume;
      }

      return volumeByDate;
    } catch (e) {
      debugPrint('Error fetching volume tracking: $e');
      return {};
    }
  }

  // Consistency metrics
  Future<Map<String, dynamic>> getConsistencyMetrics(String userId) async {
    try {
      final threeMonthsAgo = DateTime.now().subtract(const Duration(days: 90));

      final completedWorkoutsResponse = await _supabase
          .from('completed_workouts')
          .select('completed_at')
          .eq('user_id', userId)
          .gte('completed_at', threeMonthsAgo.toIso8601String())
          .order('completed_at', ascending: true);

      final completedWorkouts = completedWorkoutsResponse as List<dynamic>;

      if (completedWorkouts.isEmpty) {
        return {
          'workoutsPerWeek': 0.0,
          'currentStreak': 0,
          'longestStreak': 0,
          'consistencyScore': 0.0,
        };
      }

      // Calculate workouts per week
      final totalWorkouts = completedWorkouts.length;
      final workoutsPerWeek = totalWorkouts / 12.0; // 90 days / 7 days per week

      // Calculate streaks (consecutive days with workouts)
      final workoutDates = completedWorkouts
          .map((w) => DateTime.parse(w['completed_at']))
          .map((d) => DateTime(d.year, d.month, d.day))
          .toSet()
          .toList()
        ..sort();

      int currentStreak = 0;
      int longestStreak = 0;
      int tempStreak = 1;

      for (int i = 1; i < workoutDates.length; i++) {
        final prevDate = workoutDates[i - 1];
        final currentDate = workoutDates[i];

        if (currentDate.difference(prevDate).inDays == 1) {
          tempStreak++;
        } else {
          longestStreak =
              tempStreak > longestStreak ? tempStreak : longestStreak;
          tempStreak = 1;
        }
      }

      longestStreak = tempStreak > longestStreak ? tempStreak : longestStreak;

      // Calculate current streak
      final today = DateTime.now();
      final todayDate = DateTime(today.year, today.month, today.day);

      if (workoutDates.isNotEmpty) {
        final lastWorkoutDate = workoutDates.last;
        final daysSinceLastWorkout =
            todayDate.difference(lastWorkoutDate).inDays;

        if (daysSinceLastWorkout <= 1) {
          // Count backwards from last workout
          for (int i = workoutDates.length - 1; i > 0; i--) {
            final currentDate = workoutDates[i];
            final prevDate = workoutDates[i - 1];

            if (currentDate.difference(prevDate).inDays == 1) {
              currentStreak++;
            } else {
              break;
            }
          }
          currentStreak++; // Include the last workout day
        }
      }

      // Calculate consistency score (0-100)
      final targetWorkoutsPerWeek = 3.0;
      final weeklyConsistency =
          (workoutsPerWeek / targetWorkoutsPerWeek).clamp(0.0, 1.0);
      final streakBonus = (currentStreak / 30.0)
          .clamp(0.0, 0.3); // Max 30% bonus for 30+ day streak
      final consistencyScore =
          ((weeklyConsistency * 0.7) + (streakBonus * 0.3)) * 100;

      return {
        'workoutsPerWeek': workoutsPerWeek,
        'currentStreak': currentStreak,
        'longestStreak': longestStreak,
        'consistencyScore': consistencyScore,
        'totalWorkouts': totalWorkouts,
        'activeDays': workoutDates.length,
      };
    } catch (e) {
      debugPrint('Error calculating consistency metrics: $e');
      return {
        'workoutsPerWeek': 0.0,
        'currentStreak': 0,
        'longestStreak': 0,
        'consistencyScore': 0.0,
      };
    }
  }

  // Personal record detection
  Future<List<PersonalRecord>> detectNewPersonalRecords(
    String userId,
    SessionAnalytics sessionAnalytics,
  ) async {
    final newRecords = <PersonalRecord>[];

    try {
      for (final entry in sessionAnalytics.exercisePerformance.entries) {
        final exerciseId = entry.key;
        final performance = entry.value;

        // Check for max weight PR
        final previousMaxWeightResponse = await _supabase
            .from('workout_set_logs')
            .select('weight')
            .eq('exercise_id', exerciseId)
            .lt('completed_at', sessionAnalytics.sessionDate.toIso8601String())
            .order('weight', ascending: false)
            .limit(1);

        if (previousMaxWeightResponse.isNotEmpty) {
          final previousMaxWeight =
              (previousMaxWeightResponse.first['weight'] as num?)?.toDouble() ??
                  0.0;
          if (performance.maxWeight > previousMaxWeight) {
            newRecords.add(PersonalRecord(
              exerciseId: exerciseId,
              exerciseName: performance.exerciseName,
              type: PersonalRecordType.maxWeight,
              value: performance.maxWeight,
              achievedDate: sessionAnalytics.sessionDate,
              previousValue: previousMaxWeight,
            ));
          }
        } else if (performance.maxWeight > 0) {
          // First time doing this exercise
          newRecords.add(PersonalRecord(
            exerciseId: exerciseId,
            exerciseName: performance.exerciseName,
            type: PersonalRecordType.maxWeight,
            value: performance.maxWeight,
            achievedDate: sessionAnalytics.sessionDate,
          ));
        }

        // Check for max reps PR
        final previousMaxRepsResponse = await _supabase
            .from('workout_set_logs')
            .select('reps')
            .eq('exercise_id', exerciseId)
            .lt('completed_at', sessionAnalytics.sessionDate.toIso8601String())
            .order('reps', ascending: false)
            .limit(1);

        if (previousMaxRepsResponse.isNotEmpty) {
          final previousMaxReps =
              previousMaxRepsResponse.first['reps'] as int? ?? 0;
          if (performance.maxReps > previousMaxReps) {
            newRecords.add(PersonalRecord(
              exerciseId: exerciseId,
              exerciseName: performance.exerciseName,
              type: PersonalRecordType.maxReps,
              value: performance.maxReps.toDouble(),
              achievedDate: sessionAnalytics.sessionDate,
              previousValue: previousMaxReps.toDouble(),
            ));
          }
        } else if (performance.maxReps > 0) {
          newRecords.add(PersonalRecord(
            exerciseId: exerciseId,
            exerciseName: performance.exerciseName,
            type: PersonalRecordType.maxReps,
            value: performance.maxReps.toDouble(),
            achievedDate: sessionAnalytics.sessionDate,
          ));
        }

        // Check for max volume PR (single set)
        final maxSetVolume = performance.setDetails.isNotEmpty
            ? performance.setDetails
                .map((s) => s.volume)
                .reduce((a, b) => a > b ? a : b)
            : 0.0;

        if (maxSetVolume > 0) {
          final previousMaxVolumeResponse =
              await _supabase.rpc('get_max_set_volume', params: {
            'exercise_id_param': exerciseId,
            'before_date': sessionAnalytics.sessionDate.toIso8601String(),
          });

          final previousMaxVolume =
              (previousMaxVolumeResponse as num?)?.toDouble() ?? 0.0;
          if (maxSetVolume > previousMaxVolume) {
            newRecords.add(PersonalRecord(
              exerciseId: exerciseId,
              exerciseName: performance.exerciseName,
              type: PersonalRecordType.maxVolume,
              value: maxSetVolume,
              achievedDate: sessionAnalytics.sessionDate,
              previousValue: previousMaxVolume > 0 ? previousMaxVolume : null,
            ));
          }
        }
      }
    } catch (e) {
      debugPrint('Error detecting personal records: $e');
    }

    return newRecords;
  }

  // Get recent personal records
  Future<List<PersonalRecord>> getRecentPersonalRecords(
    String userId, {
    Duration timeRange = const Duration(days: 30),
  }) async {
    final records = <PersonalRecord>[];

    try {
      final cutoffDate = DateTime.now().subtract(timeRange);

      // Get recent completed workouts with set logs
      final completedWorkoutsResponse = await _supabase
          .from('completed_workouts')
          .select('*, workout_set_logs(*)')
          .eq('user_id', userId)
          .gte('completed_at', cutoffDate.toIso8601String())
          .order('completed_at', ascending: false);

      final completedWorkouts = completedWorkoutsResponse as List<dynamic>;

      // Group set logs by exercise and date to detect records
      final exerciseDataByDate =
          <String, Map<String, List<Map<String, dynamic>>>>{};

      for (final workout in completedWorkouts) {
        final workoutDate = DateTime.parse(workout['completed_at']);
        final setLogs = workout['workout_set_logs'] as List<dynamic>? ?? [];

        for (final setLog in setLogs) {
          final exerciseId = setLog['exercise_id'] as String?;
          if (exerciseId != null) {
            final dateKey =
                '${workoutDate.year}-${workoutDate.month}-${workoutDate.day}';
            exerciseDataByDate.putIfAbsent(exerciseId, () => {});
            exerciseDataByDate[exerciseId]!.putIfAbsent(dateKey, () => []);
            exerciseDataByDate[exerciseId]![dateKey]!.add({
              ...setLog,
              'workout_date': workoutDate,
            });
          }
        }
      }

      // Get exercise names
      final exerciseIds = exerciseDataByDate.keys.toList();
      final exerciseNames = <String, String>{};

      if (exerciseIds.isNotEmpty) {
        final exercisesResponse = await _supabase
            .from('exercises')
            .select('id, name')
            .inFilter('id', exerciseIds);

        for (final exercise in exercisesResponse) {
          exerciseNames[exercise['id']] = exercise['name'];
        }
      }

      // Check each exercise for personal records
      for (final entry in exerciseDataByDate.entries) {
        final exerciseId = entry.key;
        final exerciseName = exerciseNames[exerciseId] ?? 'Unknown Exercise';
        final dateData = entry.value;

        // Sort dates to check chronologically
        final sortedDates = dateData.keys.toList()..sort();

        for (int i = 0; i < sortedDates.length; i++) {
          final currentDate = sortedDates[i];
          final currentSets = dateData[currentDate]!;
          final workoutDate = currentSets.first['workout_date'] as DateTime;

          // Calculate current session metrics
          final maxWeight = currentSets.fold<double>(0.0, (max, set) {
            final weight = (set['weight'] as num?)?.toDouble() ?? 0.0;
            return weight > max ? weight : max;
          });

          final maxReps = currentSets.fold<int>(0, (max, set) {
            final reps = set['reps'] as int? ?? 0;
            return reps > max ? reps : max;
          });

          final totalVolume = currentSets.fold<double>(0.0, (sum, set) {
            final reps = set['reps'] as int? ?? 0;
            final weight = (set['weight'] as num?)?.toDouble() ?? 0.0;
            return sum + (weight * reps);
          });

          // Compare with previous sessions
          double previousMaxWeight = 0.0;
          int previousMaxReps = 0;
          double previousMaxVolume = 0.0;

          for (int j = 0; j < i; j++) {
            final prevDate = sortedDates[j];
            final prevSets = dateData[prevDate]!;

            final prevWeight = prevSets.fold<double>(0.0, (max, set) {
              final weight = (set['weight'] as num?)?.toDouble() ?? 0.0;
              return weight > max ? weight : max;
            });

            final prevReps = prevSets.fold<int>(0, (max, set) {
              final reps = set['reps'] as int? ?? 0;
              return reps > max ? reps : max;
            });

            final prevVolume = prevSets.fold<double>(0.0, (sum, set) {
              final reps = set['reps'] as int? ?? 0;
              final weight = (set['weight'] as num?)?.toDouble() ?? 0.0;
              return sum + (weight * reps);
            });

            if (prevWeight > previousMaxWeight) previousMaxWeight = prevWeight;
            if (prevReps > previousMaxReps) previousMaxReps = prevReps;
            if (prevVolume > previousMaxVolume) previousMaxVolume = prevVolume;
          }

          // Check for new records
          if (maxWeight > previousMaxWeight && maxWeight > 0) {
            records.add(PersonalRecord(
              exerciseId: exerciseId,
              exerciseName: exerciseName,
              type: PersonalRecordType.maxWeight,
              value: maxWeight,
              achievedDate: workoutDate,
              previousValue: previousMaxWeight > 0 ? previousMaxWeight : null,
            ));
          }

          if (maxReps > previousMaxReps && maxReps > 0) {
            records.add(PersonalRecord(
              exerciseId: exerciseId,
              exerciseName: exerciseName,
              type: PersonalRecordType.maxReps,
              value: maxReps.toDouble(),
              achievedDate: workoutDate,
              previousValue:
                  previousMaxReps > 0 ? previousMaxReps.toDouble() : null,
            ));
          }

          if (totalVolume > previousMaxVolume && totalVolume > 0) {
            records.add(PersonalRecord(
              exerciseId: exerciseId,
              exerciseName: exerciseName,
              type: PersonalRecordType.maxVolume,
              value: totalVolume,
              achievedDate: workoutDate,
              previousValue: previousMaxVolume > 0 ? previousMaxVolume : null,
            ));
          }
        }
      }

      // Sort records by date (most recent first)
      records.sort((a, b) => b.achievedDate.compareTo(a.achievedDate));
    } catch (e) {
      debugPrint('Error fetching recent personal records: $e');
    }

    return records;
  }

  // Achievement tracking
  Future<List<Map<String, dynamic>>> getAchievements(String userId) async {
    final achievements = <Map<String, dynamic>>[];

    try {
      final consistencyMetrics = await getConsistencyMetrics(userId);

      // Consistency achievements
      final currentStreak = consistencyMetrics['currentStreak'] as int;
      if (currentStreak >= 7) {
        achievements.add({
          'type': 'streak',
          'title': 'Week Warrior',
          'description': '7 day workout streak',
          'achieved': true,
          'value': currentStreak,
        });
      }

      if (currentStreak >= 30) {
        achievements.add({
          'type': 'streak',
          'title': 'Monthly Master',
          'description': '30 day workout streak',
          'achieved': true,
          'value': currentStreak,
        });
      }

      // Volume achievements
      final volumeData =
          await getVolumeTracking(userId, timeRange: const Duration(days: 30));
      final totalVolume =
          volumeData.values.fold<double>(0.0, (sum, volume) => sum + volume);

      if (totalVolume >= 10000) {
        achievements.add({
          'type': 'volume',
          'title': 'Heavy Lifter',
          'description': '10,000 lbs lifted this month',
          'achieved': true,
          'value': totalVolume,
        });
      }

      // Workout count achievements
      final totalWorkouts = consistencyMetrics['totalWorkouts'] as int;
      if (totalWorkouts >= 50) {
        achievements.add({
          'type': 'count',
          'title': 'Fitness Enthusiast',
          'description': '50 workouts completed',
          'achieved': true,
          'value': totalWorkouts,
        });
      }

      if (totalWorkouts >= 100) {
        achievements.add({
          'type': 'count',
          'title': 'Fitness Fanatic',
          'description': '100 workouts completed',
          'achieved': true,
          'value': totalWorkouts,
        });
      }
    } catch (e) {
      debugPrint('Error fetching achievements: $e');
    }

    return achievements;
  }

  // Clear cache methods
  void clearProgressCache([String? workoutId]) {
    if (workoutId != null) {
      _progressCache.remove(workoutId);
    } else {
      _progressCache.clear();
    }
    notifyListeners();
  }

  void clearSessionCache([String? sessionId]) {
    if (sessionId != null) {
      _sessionCache.remove(sessionId);
    } else {
      _sessionCache.clear();
    }
    notifyListeners();
  }

  void clearAllCaches() {
    _progressCache.clear();
    _sessionCache.clear();
    notifyListeners();
  }
}
