import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/offline_workout_session.dart';
import '../models/exercise.dart';
import 'offline_service.dart';
import 'progress_service.dart';

/// Service for managing intelligent set tracking with auto-suggestions
/// and real-time data saving to workout_set_logs table with offline support
class SetTrackingService extends ChangeNotifier {
  final SupabaseClient _supabase = Supabase.instance.client;
  final OfflineService _offlineService;
  final ProgressService _progressService;

  SetTrackingService({
    required OfflineService offlineService,
    required ProgressService progressService,
  })  : _offlineService = offlineService,
        _progressService = progressService;

  /// Generate intelligent set suggestions based on previous performance and fatigue
  Future<SetSuggestion> generateSetSuggestion({
    required Exercise exercise,
    required int setNumber,
    List<OfflineSetData> previousSetsInSession = const [],
    OfflineSetData? previousSetData,
  }) async {
    try {
      // Get historical performance data
      final exerciseProgress = await _progressService.getExerciseProgress(
        exercise.id,
        limit: 20, // Get more data for better analysis
      );

      // Calculate current fatigue level
      final fatigueLevel = _calculateSessionFatigue(previousSetsInSession);

      // Get base performance metrics
      final baseMetrics = _calculateBaseMetrics(exerciseProgress);

      // Generate suggestion based on multiple factors
      final suggestion = _generateIntelligentSuggestion(
        baseMetrics: baseMetrics,
        fatigueLevel: fatigueLevel,
        setNumber: setNumber,
        previousSetsInSession: previousSetsInSession,
        previousSetData: previousSetData,
        exercise: exercise,
      );

      return suggestion;
    } catch (e) {
      debugPrint('Error generating set suggestion: $e');
      return _getFallbackSuggestion(exercise, setNumber, previousSetData);
    }
  }

  /// Calculate fatigue level based on previous sets in current session
  double _calculateSessionFatigue(List<OfflineSetData> previousSets) {
    if (previousSets.isEmpty) return 0.0;

    double fatigue = 0.0;

    // Factor 1: Number of sets completed (progressive fatigue)
    fatigue += (previousSets.length * 0.08).clamp(0.0, 0.6);

    // Factor 2: Average difficulty rating
    final ratingsWithValues = previousSets
        .where((set) => set.difficultyRating != null)
        .map((set) => set.difficultyRating!)
        .toList();

    if (ratingsWithValues.isNotEmpty) {
      final avgDifficulty =
          ratingsWithValues.reduce((a, b) => a + b) / ratingsWithValues.length;

      // High difficulty increases fatigue more
      if (avgDifficulty >= 4.0) {
        fatigue += 0.25;
      } else if (avgDifficulty >= 3.5) {
        fatigue += 0.15;
      } else if (avgDifficulty <= 2.0) {
        fatigue -= 0.1; // Low difficulty reduces perceived fatigue
      }
    }

    // Factor 3: Volume accumulation (weight × reps)
    final totalVolume = previousSets.fold<double>(
      0.0,
      (sum, set) => sum + (set.weight * set.reps),
    );

    // Normalize volume impact (assuming average set volume of 1000 lbs)
    final volumeFatigue = (totalVolume / 5000.0).clamp(0.0, 0.3);
    fatigue += volumeFatigue;

    // Factor 4: Time-based fatigue (longer sessions = more fatigue)
    if (previousSets.isNotEmpty) {
      final sessionDuration =
          DateTime.now().difference(previousSets.first.completedAt);
      final hoursFatigue = (sessionDuration.inMinutes / 120.0).clamp(0.0, 0.2);
      fatigue += hoursFatigue;
    }

    return fatigue.clamp(0.0, 1.0);
  }

  /// Calculate base performance metrics from historical data
  BasePerformanceMetrics _calculateBaseMetrics(
      List<Map<String, dynamic>> exerciseProgress) {
    if (exerciseProgress.isEmpty) {
      return BasePerformanceMetrics(
        averageWeight: 0.0,
        averageReps: 8,
        maxWeight: 0.0,
        maxReps: 0,
        recentTrend: PerformanceTrend.stable,
        consistency: 0.0,
      );
    }

    // Calculate averages from recent sessions
    final weights = exerciseProgress
        .map((p) => (p['weight'] as num?)?.toDouble() ?? 0.0)
        .where((w) => w > 0)
        .toList();

    final reps = exerciseProgress
        .map((p) => (p['reps'] as num?)?.toInt() ?? 0)
        .where((r) => r > 0)
        .toList();

    final averageWeight = weights.isNotEmpty
        ? weights.reduce((a, b) => a + b) / weights.length
        : 0.0;

    final averageReps = reps.isNotEmpty
        ? (reps.reduce((a, b) => a + b) / reps.length).round()
        : 8;

    final maxWeight =
        weights.isNotEmpty ? weights.reduce((a, b) => a > b ? a : b) : 0.0;
    final maxReps = reps.isNotEmpty ? reps.reduce((a, b) => a > b ? a : b) : 0;

    // Calculate performance trend
    final trend = _calculatePerformanceTrend(exerciseProgress);

    // Calculate consistency (how consistent are the performances)
    final consistency = _calculateConsistency(weights, reps);

    return BasePerformanceMetrics(
      averageWeight: averageWeight,
      averageReps: averageReps,
      maxWeight: maxWeight,
      maxReps: maxReps,
      recentTrend: trend,
      consistency: consistency,
    );
  }

  /// Calculate performance trend from recent data
  PerformanceTrend _calculatePerformanceTrend(
      List<Map<String, dynamic>> exerciseProgress) {
    if (exerciseProgress.length < 3) return PerformanceTrend.stable;

    // Take the most recent 5 sessions for trend analysis
    final recentSessions = exerciseProgress.take(5).toList();
    final volumes = recentSessions
        .map((p) =>
            ((p['weight'] as num?)?.toDouble() ?? 0.0) *
            ((p['reps'] as num?)?.toInt() ?? 0))
        .toList();

    if (volumes.length < 3) return PerformanceTrend.stable;

    // Calculate trend using linear regression slope
    double sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
    for (int i = 0; i < volumes.length; i++) {
      sumX += i;
      sumY += volumes[i];
      sumXY += i * volumes[i];
      sumXX += i * i;
    }

    final n = volumes.length;
    final slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);

    // Determine trend based on slope
    if (slope > 50) return PerformanceTrend.improving;
    if (slope < -50) return PerformanceTrend.declining;
    return PerformanceTrend.stable;
  }

  /// Calculate consistency score (0.0 to 1.0)
  double _calculateConsistency(List<double> weights, List<int> reps) {
    if (weights.length < 2 || reps.length < 2) return 0.5;

    // Calculate coefficient of variation for weights and reps
    final weightMean = weights.reduce((a, b) => a + b) / weights.length;
    final repsMean = reps.reduce((a, b) => a + b) / reps.length;

    final weightVariance = weights
            .map((w) => (w - weightMean) * (w - weightMean))
            .reduce((a, b) => a + b) /
        weights.length;

    final repsVariance = reps
            .map((r) => (r - repsMean) * (r - repsMean))
            .reduce((a, b) => a + b) /
        reps.length;

    final weightCV =
        weightMean > 0 ? (weightVariance / (weightMean * weightMean)) : 0.0;
    final repsCV = repsMean > 0 ? (repsVariance / (repsMean * repsMean)) : 0.0;

    // Lower coefficient of variation = higher consistency
    final avgCV = (weightCV + repsCV) / 2;
    return (1.0 - avgCV.clamp(0.0, 1.0)).clamp(0.0, 1.0);
  }

  /// Generate intelligent suggestion based on all factors
  SetSuggestion _generateIntelligentSuggestion({
    required BasePerformanceMetrics baseMetrics,
    required double fatigueLevel,
    required int setNumber,
    required List<OfflineSetData> previousSetsInSession,
    OfflineSetData? previousSetData,
    required Exercise exercise,
  }) {
    double suggestedWeight = baseMetrics.averageWeight;
    int suggestedReps = baseMetrics.averageReps;
    String reasoning = 'Based on your performance history';

    // Adjust for set number (first sets typically stronger)
    if (setNumber == 1) {
      // First set - suggest closer to max performance
      suggestedWeight =
          _lerp(baseMetrics.averageWeight, baseMetrics.maxWeight, 0.3);
      reasoning = 'First set - aiming for strong performance';
    } else if (setNumber <= 3) {
      // Early sets - maintain good performance
      suggestedWeight = baseMetrics.averageWeight;
    } else {
      // Later sets - account for fatigue
      suggestedWeight = _lerp(
          baseMetrics.averageWeight, baseMetrics.averageWeight * 0.85, 0.5);
      reasoning = 'Later set - accounting for accumulated fatigue';
    }

    // Adjust for fatigue level
    if (fatigueLevel > 0.6) {
      suggestedWeight *= (1.0 - fatigueLevel * 0.2); // Up to 20% reduction
      suggestedReps = (suggestedReps * 0.9).round(); // Reduce reps slightly
      reasoning = 'High fatigue detected - reducing intensity';
    } else if (fatigueLevel < 0.2) {
      // Low fatigue - opportunity for progressive overload
      if (baseMetrics.recentTrend == PerformanceTrend.improving) {
        suggestedWeight *= 1.05; // 5% increase
        reasoning = 'Low fatigue and improving trend - progressive overload';
      }
    }

    // Adjust based on previous sets in current session
    if (previousSetsInSession.isNotEmpty) {
      final lastSet = previousSetsInSession.last;

      if (lastSet.difficultyRating != null) {
        switch (lastSet.difficultyRating!) {
          case 1: // Very easy
            suggestedWeight *= 1.1; // 10% increase
            reasoning = 'Previous set was very easy - increasing weight';
            break;
          case 2: // Easy
            suggestedWeight *= 1.05; // 5% increase
            reasoning = 'Previous set was easy - slight increase';
            break;
          case 4: // Hard
            suggestedWeight *= 0.95; // 5% decrease
            reasoning = 'Previous set was hard - reducing weight';
            break;
          case 5: // Very hard
            suggestedWeight *= 0.9; // 10% decrease
            suggestedReps = (suggestedReps * 0.9).round();
            reasoning = 'Previous set was very hard - significant reduction';
            break;
        }
      }

      // Check for performance pattern in current session
      if (previousSetsInSession.length >= 2) {
        final recentSets = previousSetsInSession.takeLast(2).toList();
        final isDecreasingPerformance = recentSets.length == 2 &&
            recentSets[1].weight < recentSets[0].weight;

        if (isDecreasingPerformance) {
          suggestedWeight *= 0.95; // Further reduction
          reasoning = 'Decreasing performance pattern detected';
        }
      }
    }

    // Apply progressive overload for consistent performers
    if (baseMetrics.consistency > 0.7 &&
        baseMetrics.recentTrend == PerformanceTrend.stable &&
        fatigueLevel < 0.4) {
      suggestedWeight *= 1.025; // 2.5% increase
      reasoning = 'Consistent performance - time for progression';
    }

    // Ensure reasonable bounds
    suggestedWeight = suggestedWeight.clamp(0.0, baseMetrics.maxWeight * 1.2);
    suggestedReps = suggestedReps.clamp(1, 20);

    // Calculate confidence based on data quality and consistency
    double confidence = 0.5; // Base confidence
    confidence += baseMetrics.consistency * 0.3; // Up to 30% from consistency
    confidence += (1.0 - fatigueLevel) * 0.2; // Up to 20% from low fatigue
    confidence = confidence.clamp(0.0, 1.0);

    return SetSuggestion(
      suggestedWeight: suggestedWeight,
      suggestedReps: suggestedReps,
      confidence: confidence,
      reasoning: reasoning,
    );
  }

  /// Get fallback suggestion when no historical data is available
  SetSuggestion _getFallbackSuggestion(
    Exercise exercise,
    int setNumber,
    OfflineSetData? previousSetData,
  ) {
    double suggestedWeight = 0.0;
    int suggestedReps = 8;
    String reasoning = 'General recommendation';

    if (previousSetData != null) {
      suggestedWeight = previousSetData.weight;
      suggestedReps = previousSetData.reps;
      reasoning = 'Based on your previous set';
    } else {
      // Provide exercise-specific defaults based on muscle group
      final defaults = _getExerciseDefaults(exercise);
      suggestedWeight = defaults.weight;
      suggestedReps = defaults.reps;
      reasoning =
          'Starting recommendation for ${exercise.primaryMuscle ?? 'this exercise'}';
    }

    return SetSuggestion(
      suggestedWeight: suggestedWeight,
      suggestedReps: suggestedReps,
      confidence: 0.3, // Low confidence for fallback
      reasoning: reasoning,
    );
  }

  /// Get exercise-specific default values
  ExerciseDefaults _getExerciseDefaults(Exercise exercise) {
    final muscleGroup = exercise.primaryMuscle?.toLowerCase() ?? '';

    // Provide reasonable defaults based on muscle group
    switch (muscleGroup) {
      case 'chest':
        return ExerciseDefaults(weight: 135.0, reps: 8);
      case 'back':
        return ExerciseDefaults(weight: 115.0, reps: 8);
      case 'shoulders':
        return ExerciseDefaults(weight: 85.0, reps: 10);
      case 'arms':
      case 'biceps':
      case 'triceps':
        return ExerciseDefaults(weight: 65.0, reps: 10);
      case 'legs':
      case 'quadriceps':
      case 'hamstrings':
        return ExerciseDefaults(weight: 185.0, reps: 8);
      case 'glutes':
        return ExerciseDefaults(weight: 155.0, reps: 10);
      case 'calves':
        return ExerciseDefaults(weight: 135.0, reps: 12);
      case 'core':
      case 'abs':
        return ExerciseDefaults(weight: 0.0, reps: 15); // Bodyweight
      default:
        return ExerciseDefaults(weight: 95.0, reps: 8);
    }
  }

  /// Save set data to both offline storage and attempt online sync
  Future<void> saveSetData({
    required String sessionId,
    required OfflineSetData setData,
  }) async {
    try {
      // Always save to offline storage first
      await _offlineService.addSet(
        sessionId: sessionId,
        exerciseId: setData.exerciseId,
        exerciseName: setData.exerciseName,
        setNumber: setData.setNumber,
        weight: setData.weight,
        reps: setData.reps,
        restTime: setData.restTime,
        difficultyRating: setData.difficultyRating,
        notes: setData.notes,
        metadata: setData.metadata,
      );

      // Attempt to sync to Supabase if online
      await _attemptOnlineSync(setData);

      notifyListeners();
    } catch (e) {
      debugPrint('Error saving set data: $e');
      rethrow;
    }
  }

  /// Attempt to sync set data to Supabase
  Future<void> _attemptOnlineSync(OfflineSetData setData) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return;

      // Prepare data for workout_set_logs table
      final setLogData = {
        'id': setData.setId,
        'user_id': user.id,
        'exercise_id': setData.exerciseId,
        'set_number': setData.setNumber,
        'weight': setData.weight,
        'reps': setData.reps,
        'completed_at': setData.completedAt.toIso8601String(),
        'rest_time': setData.restTime.inSeconds,
        'difficulty_rating': setData.difficultyRating,
        'notes': setData.notes,
        'created_at': DateTime.now().toIso8601String(),
      };

      await _supabase.from('workout_set_logs').upsert(setLogData);
    } catch (e) {
      // Silently fail - data is already saved offline
      debugPrint('Failed to sync set data online: $e');
    }
  }

  /// Linear interpolation helper
  double _lerp(double a, double b, double t) {
    return a + (b - a) * t;
  }
}

/// Data class for set suggestions
class SetSuggestion {
  final double suggestedWeight;
  final int suggestedReps;
  final double confidence;
  final String reasoning;

  SetSuggestion({
    required this.suggestedWeight,
    required this.suggestedReps,
    required this.confidence,
    required this.reasoning,
  });

  @override
  String toString() {
    return 'SetSuggestion(weight: ${suggestedWeight.toStringAsFixed(1)}, '
        'reps: $suggestedReps, confidence: ${(confidence * 100).toStringAsFixed(0)}%)';
  }
}

/// Base performance metrics calculated from historical data
class BasePerformanceMetrics {
  final double averageWeight;
  final int averageReps;
  final double maxWeight;
  final int maxReps;
  final PerformanceTrend recentTrend;
  final double consistency;

  BasePerformanceMetrics({
    required this.averageWeight,
    required this.averageReps,
    required this.maxWeight,
    required this.maxReps,
    required this.recentTrend,
    required this.consistency,
  });
}

/// Performance trend enumeration
enum PerformanceTrend {
  improving,
  stable,
  declining,
}

/// Exercise-specific default values
class ExerciseDefaults {
  final double weight;
  final int reps;

  ExerciseDefaults({
    required this.weight,
    required this.reps,
  });
}

/// Extension to get last N elements from a list
extension ListExtension<T> on List<T> {
  List<T> takeLast(int count) {
    if (count >= length) return this;
    return sublist(length - count);
  }
}
