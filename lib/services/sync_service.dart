import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';
import '../models/offline_workout_session.dart';
import 'offline_service.dart';

class SyncService extends ChangeNotifier {
  final SupabaseClient _supabase;
  final OfflineService _offlineService;
  final Connectivity _connectivity = Connectivity();

  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  Timer? _syncTimer;
  bool _isSyncing = false;
  bool _autoSyncEnabled = true;

  // Sync statistics
  int _totalSyncAttempts = 0;
  int _successfulSyncs = 0;
  int _failedSyncs = 0;
  DateTime? _lastSyncAttempt;
  DateTime? _lastSuccessfulSync;

  SyncService({
    SupabaseClient? supabaseClient,
    required OfflineService offlineService,
  })  : _supabase = supabaseClient ?? Supabase.instance.client,
        _offlineService = offlineService {
    _initializeConnectivityListener();
    _startPeriodicSync();
  }

  // Getters for sync status
  bool get isSyncing => _isSyncing;
  bool get autoSyncEnabled => _autoSyncEnabled;
  int get totalSyncAttempts => _totalSyncAttempts;
  int get successfulSyncs => _successfulSyncs;
  int get failedSyncs => _failedSyncs;
  DateTime? get lastSyncAttempt => _lastSyncAttempt;
  DateTime? get lastSuccessfulSync => _lastSuccessfulSync;

  void _initializeConnectivityListener() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        if (results.isNotEmpty &&
            results.any((result) => result != ConnectivityResult.none) &&
            _autoSyncEnabled) {
          // Delay sync to allow connection to stabilize
          Timer(const Duration(seconds: 2), () {
            syncPendingData();
          });
        }
      },
    );
  }

  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (_autoSyncEnabled && !_isSyncing) {
        syncPendingData();
      }
    });
  }

  // Main sync method
  Future<SyncResult> syncPendingData() async {
    if (_isSyncing) {
      return SyncResult(
        success: false,
        message: 'Sync already in progress',
        syncedSessions: 0,
        failedSessions: 0,
      );
    }

    _isSyncing = true;
    _totalSyncAttempts++;
    _lastSyncAttempt = DateTime.now();
    notifyListeners();

    try {
      // Check connectivity
      final connectivityResults = await _connectivity.checkConnectivity();
      if (connectivityResults.isEmpty ||
          connectivityResults
              .every((result) => result == ConnectivityResult.none)) {
        return SyncResult(
          success: false,
          message: 'No internet connection',
          syncedSessions: 0,
          failedSessions: 0,
        );
      }

      // Get pending and failed sessions
      final pendingSessions = await _offlineService.getPendingSyncSessions();
      final failedSessions = await _offlineService.getFailedSyncSessions();

      // Filter failed sessions that can be retried
      final retryableSessions =
          failedSessions.where((session) => session.canRetrySync).toList();

      final allSessionsToSync = [...pendingSessions, ...retryableSessions];

      if (allSessionsToSync.isEmpty) {
        return SyncResult(
          success: true,
          message: 'No sessions to sync',
          syncedSessions: 0,
          failedSessions: 0,
        );
      }

      int syncedCount = 0;
      int failedCount = 0;
      final errors = <String>[];

      for (final session in allSessionsToSync) {
        try {
          await _offlineService.markSessionSyncing(session.sessionId);

          final success = await _syncSession(session);

          if (success) {
            await _offlineService.markSessionSynced(session.sessionId);
            syncedCount++;
          } else {
            await _offlineService.markSessionSyncFailed(
              session.sessionId,
              'Failed to sync session data',
            );
            failedCount++;
            errors.add('Session ${session.sessionId}: Sync failed');
          }
        } catch (e) {
          await _offlineService.markSessionSyncFailed(
            session.sessionId,
            e.toString(),
          );
          failedCount++;
          errors.add('Session ${session.sessionId}: ${e.toString()}');
          debugPrint('Error syncing session ${session.sessionId}: $e');
        }
      }

      if (syncedCount > 0) {
        _successfulSyncs += syncedCount;
        _lastSuccessfulSync = DateTime.now();
      }

      if (failedCount > 0) {
        _failedSyncs += failedCount;
      }

      final success = failedCount == 0;
      final message = success
          ? 'Successfully synced $syncedCount sessions'
          : 'Synced $syncedCount sessions, failed $failedCount sessions';

      return SyncResult(
        success: success,
        message: message,
        syncedSessions: syncedCount,
        failedSessions: failedCount,
        errors: errors,
      );
    } catch (e) {
      debugPrint('Error during sync: $e');
      return SyncResult(
        success: false,
        message: 'Sync failed: ${e.toString()}',
        syncedSessions: 0,
        failedSessions: 0,
        errors: [e.toString()],
      );
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  Future<bool> _syncSession(OfflineWorkoutSession session) async {
    try {
      // Check if this is a completed workout session
      if (session.isCompleted) {
        return await _syncCompletedWorkout(session);
      } else {
        return await _syncActiveWorkout(session);
      }
    } catch (e) {
      debugPrint('Error syncing session ${session.sessionId}: $e');
      return false;
    }
  }

  Future<bool> _syncCompletedWorkout(OfflineWorkoutSession session) async {
    try {
      // Create completed workout record
      final completedWorkoutData = {
        'id': session.sessionId,
        'user_id': session.userId,
        'workout_id': session.workoutId,
        'completed_at': session.endTime!.toIso8601String(),
        'duration': session.duration,
        'notes': session.notes,
        'rating': session.rating,
        'total_sets': session.totalSets,
        'total_reps': session.totalReps,
        'total_volume': session.totalVolume,
      };

      // Insert completed workout
      await _supabase.from('completed_workouts').upsert(completedWorkoutData);

      // Sync all sets
      for (final setData in session.completedSets) {
        final setLogData = {
          'id': setData.setId,
          'completed_workout_id': session.sessionId,
          'exercise_id': setData.exerciseId,
          'set_number': setData.setNumber,
          'weight': setData.weight,
          'reps': setData.reps,
          'completed_at': setData.completedAt.toIso8601String(),
          'rest_time': setData.restTime.inSeconds,
          'difficulty_rating': setData.difficultyRating,
          'notes': setData.notes,
        };

        await _supabase.from('workout_set_logs').upsert(setLogData);
      }

      return true;
    } catch (e) {
      debugPrint('Error syncing completed workout: $e');
      return false;
    }
  }

  Future<bool> _syncActiveWorkout(OfflineWorkoutSession session) async {
    try {
      // Update or create workout record
      final workoutData = {
        'id': session.sessionId,
        'user_id': session.userId,
        'name': 'Offline Workout - ${session.startTime.toIso8601String()}',
        'start_time': session.startTime.toIso8601String(),
        'is_active': !session.isCompleted,
        'last_state': session.lastState,
        'is_minimized': false,
        'session_order': 1,
        'created_at': session.startTime.toIso8601String(),
        'updated_at': session.lastModified.toIso8601String(),
      };

      await _supabase.from('workouts').upsert(workoutData);

      // Sync completed sets as workout logs
      for (final setData in session.completedSets) {
        final logData = {
          'id': setData.setId,
          'workout_id': session.sessionId,
          'exercise_id': setData.exerciseId,
          'set_number': setData.setNumber,
          'weight': setData.weight,
          'reps': setData.reps,
          'completed_at': setData.completedAt.toIso8601String(),
          'rest_time': setData.restTime.inSeconds,
          'difficulty_rating': setData.difficultyRating,
          'notes': setData.notes,
        };

        await _supabase.from('workout_logs').upsert(logData);
      }

      return true;
    } catch (e) {
      debugPrint('Error syncing active workout: $e');
      return false;
    }
  }

  // Conflict resolution
  Future<ConflictResolution> resolveConflict(
    OfflineWorkoutSession localSession,
    Map<String, dynamic> serverData,
  ) async {
    // Always prioritize local workout session data as per requirements
    try {
      // Check if server data is newer
      final serverUpdatedAt = DateTime.parse(
          serverData['updated_at'] ?? serverData['completed_at']);
      final localUpdatedAt = localSession.lastModified;

      if (serverUpdatedAt.isAfter(localUpdatedAt)) {
        // Server is newer, but we still prioritize local data
        // Log the conflict for user awareness
        debugPrint(
            'Conflict detected: Server data is newer but prioritizing local data');

        return ConflictResolution(
          resolution: ConflictResolutionType.useLocal,
          message: 'Local workout data takes precedence over server data',
          localSession: localSession,
          serverData: serverData,
        );
      } else {
        // Local data is newer or same, proceed normally
        return ConflictResolution(
          resolution: ConflictResolutionType.useLocal,
          message: 'Local data is current',
          localSession: localSession,
          serverData: serverData,
        );
      }
    } catch (e) {
      debugPrint('Error resolving conflict: $e');
      return ConflictResolution(
        resolution: ConflictResolutionType.useLocal,
        message: 'Error resolving conflict, using local data',
        localSession: localSession,
        serverData: serverData,
      );
    }
  }

  // Manual sync methods
  Future<SyncResult> syncSpecificSession(String sessionId) async {
    final session = await _offlineService.getSession(sessionId);
    if (session == null) {
      return SyncResult(
        success: false,
        message: 'Session not found',
        syncedSessions: 0,
        failedSessions: 1,
      );
    }

    if (session.syncStatus == SyncStatus.synced) {
      return SyncResult(
        success: true,
        message: 'Session already synced',
        syncedSessions: 0,
        failedSessions: 0,
      );
    }

    try {
      await _offlineService.markSessionSyncing(sessionId);
      final success = await _syncSession(session);

      if (success) {
        await _offlineService.markSessionSynced(sessionId);
        return SyncResult(
          success: true,
          message: 'Session synced successfully',
          syncedSessions: 1,
          failedSessions: 0,
        );
      } else {
        await _offlineService.markSessionSyncFailed(
            sessionId, 'Manual sync failed');
        return SyncResult(
          success: false,
          message: 'Failed to sync session',
          syncedSessions: 0,
          failedSessions: 1,
        );
      }
    } catch (e) {
      await _offlineService.markSessionSyncFailed(sessionId, e.toString());
      return SyncResult(
        success: false,
        message: 'Sync error: ${e.toString()}',
        syncedSessions: 0,
        failedSessions: 1,
      );
    }
  }

  // Settings
  void setAutoSync(bool enabled) {
    _autoSyncEnabled = enabled;
    notifyListeners();

    if (enabled && !_isSyncing) {
      syncPendingData();
    }
  }

  void setSyncInterval(Duration interval) {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(interval, (timer) {
      if (_autoSyncEnabled && !_isSyncing) {
        syncPendingData();
      }
    });
  }

  // Statistics
  Map<String, dynamic> getSyncStatistics() {
    return {
      'totalSyncAttempts': _totalSyncAttempts,
      'successfulSyncs': _successfulSyncs,
      'failedSyncs': _failedSyncs,
      'successRate': _totalSyncAttempts > 0
          ? (_successfulSyncs / _totalSyncAttempts * 100).toStringAsFixed(1)
          : '0.0',
      'lastSyncAttempt': _lastSyncAttempt?.toIso8601String(),
      'lastSuccessfulSync': _lastSuccessfulSync?.toIso8601String(),
      'isSyncing': _isSyncing,
      'autoSyncEnabled': _autoSyncEnabled,
    };
  }

  void resetStatistics() {
    _totalSyncAttempts = 0;
    _successfulSyncs = 0;
    _failedSyncs = 0;
    _lastSyncAttempt = null;
    _lastSuccessfulSync = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncTimer?.cancel();
    super.dispose();
  }
}

// Data classes for sync results and conflict resolution
class SyncResult {
  final bool success;
  final String message;
  final int syncedSessions;
  final int failedSessions;
  final List<String> errors;

  SyncResult({
    required this.success,
    required this.message,
    required this.syncedSessions,
    required this.failedSessions,
    this.errors = const [],
  });

  @override
  String toString() {
    return 'SyncResult(success: $success, message: $message, synced: $syncedSessions, failed: $failedSessions)';
  }
}

class ConflictResolution {
  final ConflictResolutionType resolution;
  final String message;
  final OfflineWorkoutSession localSession;
  final Map<String, dynamic> serverData;

  ConflictResolution({
    required this.resolution,
    required this.message,
    required this.localSession,
    required this.serverData,
  });
}

enum ConflictResolutionType {
  useLocal,
  useServer,
  merge,
  askUser,
}
