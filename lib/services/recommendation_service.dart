import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/workout.dart';
import '../services/workout_filter_service.dart';

enum RecommendationType {
  recentActivity,
  fitnessGoals,
  difficulty,
  muscleBalance,
  popular,
  similar,
}

class WorkoutRecommendation {
  final Workout workout;
  final RecommendationType type;
  final String reason;
  final double confidence;

  const WorkoutRecommendation({
    required this.workout,
    required this.type,
    required this.reason,
    required this.confidence,
  });
}

class UserProfile {
  final String userId;
  final String? fitnessLevel;
  final List<String>? fitnessGoals;
  final List<String>? preferredMuscleGroups;
  final List<String>? availableEquipment;
  final int? workoutsPerWeek;
  final int? sessionDuration;

  const UserProfile({
    required this.userId,
    this.fitnessLevel,
    this.fitnessGoals,
    this.preferredMuscleGroups,
    this.availableEquipment,
    this.workoutsPerWeek,
    this.sessionDuration,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      userId: json['user_id'] ?? json['id'],
      fitnessLevel: json['fitness_level'],
      fitnessGoals: json['fitness_goals'] != null
          ? List<String>.from(json['fitness_goals'])
          : null,
      preferredMuscleGroups: json['preferred_muscle_groups'] != null
          ? List<String>.from(json['preferred_muscle_groups'])
          : null,
      availableEquipment: json['available_equipment'] != null
          ? List<String>.from(json['available_equipment'])
          : null,
      workoutsPerWeek: json['workouts_per_week'],
      sessionDuration: json['session_duration'],
    );
  }
}

class WorkoutHistory {
  final String workoutId;
  final DateTime completedAt;
  final int duration;
  final double? rating;
  final List<String> muscleGroups;
  final List<String> equipment;

  const WorkoutHistory({
    required this.workoutId,
    required this.completedAt,
    required this.duration,
    this.rating,
    required this.muscleGroups,
    required this.equipment,
  });
}

class RecommendationService extends ChangeNotifier {
  final SupabaseClient? _supabase;

  RecommendationService({SupabaseClient? supabaseClient})
      : _supabase = supabaseClient;

  factory RecommendationService.instance() {
    return RecommendationService(supabaseClient: Supabase.instance.client);
  }

  UserProfile? _userProfile;
  List<WorkoutHistory> _workoutHistory = [];
  List<WorkoutRecommendation> _recommendations = [];
  bool _isLoading = false;

  UserProfile? get userProfile => _userProfile;
  List<WorkoutRecommendation> get recommendations => _recommendations;
  bool get isLoading => _isLoading;

  @visibleForTesting
  set userProfile(UserProfile? profile) => _userProfile = profile;

  @visibleForTesting
  set workoutHistory(List<WorkoutHistory> history) => _workoutHistory = history;

  Future<void> loadUserData() async {
    _isLoading = true;
    notifyListeners();

    try {
      if (_supabase == null) return;

      final user = _supabase!.auth.currentUser;
      if (user == null) return;

      // Load user profile
      await _loadUserProfile(user.id);

      // Load workout history
      await _loadWorkoutHistory(user.id);
    } catch (e) {
      debugPrint('Error loading user data: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<List<WorkoutRecommendation>> generateRecommendations(
    List<Workout> availableWorkouts,
  ) async {
    if (_userProfile == null) {
      await loadUserData();
    }

    final recommendations = <WorkoutRecommendation>[];

    // Generate different types of recommendations
    recommendations
        .addAll(_getRecentActivityRecommendations(availableWorkouts));
    recommendations.addAll(getFitnessGoalRecommendations(availableWorkouts));
    recommendations.addAll(getDifficultyRecommendations(availableWorkouts));
    recommendations.addAll(getMuscleBalanceRecommendations(availableWorkouts));
    recommendations.addAll(getPopularRecommendations(availableWorkouts));
    recommendations
        .addAll(_getSimilarWorkoutRecommendations(availableWorkouts));

    // Sort by confidence and remove duplicates
    final uniqueRecommendations = <String, WorkoutRecommendation>{};
    for (final rec in recommendations) {
      final existing = uniqueRecommendations[rec.workout.id];
      if (existing == null || rec.confidence > existing.confidence) {
        uniqueRecommendations[rec.workout.id] = rec;
      }
    }

    _recommendations = uniqueRecommendations.values.toList()
      ..sort((a, b) => b.confidence.compareTo(a.confidence));

    notifyListeners();
    return _recommendations;
  }

  List<WorkoutRecommendation> _getRecentActivityRecommendations(
    List<Workout> availableWorkouts,
  ) {
    if (_workoutHistory.isEmpty) return [];

    final recommendations = <WorkoutRecommendation>[];
    final recentWorkouts = _workoutHistory
        .where((h) => DateTime.now().difference(h.completedAt).inDays <= 7)
        .toList();

    if (recentWorkouts.isEmpty) return recommendations;

    // Find workouts targeting similar muscle groups
    final recentMuscleGroups =
        recentWorkouts.expand((h) => h.muscleGroups).toSet();

    for (final workout in availableWorkouts) {
      final workoutMuscles = workout.primaryMuscles.toSet();
      final overlap = recentMuscleGroups.intersection(workoutMuscles);

      if (overlap.isNotEmpty) {
        final confidence = (overlap.length / workoutMuscles.length) * 0.8;
        recommendations.add(WorkoutRecommendation(
          workout: workout,
          type: RecommendationType.recentActivity,
          reason: 'Based on your recent ${overlap.join(", ")} workouts',
          confidence: confidence,
        ));
      }
    }

    return recommendations;
  }

  @visibleForTesting
  List<WorkoutRecommendation> getFitnessGoalRecommendations(
    List<Workout> availableWorkouts,
  ) {
    if (_userProfile?.fitnessGoals == null) return [];

    final recommendations = <WorkoutRecommendation>[];
    final goals = _userProfile!.fitnessGoals!;

    for (final workout in availableWorkouts) {
      double confidence = 0.0;
      final reasons = <String>[];

      // Match goals to workout characteristics
      for (final goal in goals) {
        switch (goal.toLowerCase()) {
          case 'strength':
          case 'build muscle':
            if (workout.equipmentNeeded.isNotEmpty) {
              confidence += 0.3;
              reasons.add('strength building');
            }
            break;
          case 'weight loss':
          case 'cardio':
            if (estimateWorkoutIntensity(workout) > 0.7) {
              confidence += 0.3;
              reasons.add('high intensity for weight loss');
            }
            break;
          case 'flexibility':
            if (workout.primaryMuscles
                .any((m) => ['core', 'back'].contains(m.toLowerCase()))) {
              confidence += 0.2;
              reasons.add('flexibility and mobility');
            }
            break;
          case 'endurance':
            if (workout.exercises.length > 5) {
              confidence += 0.2;
              reasons.add('endurance building');
            }
            break;
        }
      }

      if (confidence > 0 && reasons.isNotEmpty) {
        recommendations.add(WorkoutRecommendation(
          workout: workout,
          type: RecommendationType.fitnessGoals,
          reason: 'Matches your ${reasons.join(" and ")} goals',
          confidence: confidence,
        ));
      }
    }

    return recommendations;
  }

  @visibleForTesting
  List<WorkoutRecommendation> getDifficultyRecommendations(
    List<Workout> availableWorkouts,
  ) {
    if (_userProfile?.fitnessLevel == null) return [];

    final recommendations = <WorkoutRecommendation>[];
    final userLevel = _userProfile!.fitnessLevel!.toLowerCase();

    for (final workout in availableWorkouts) {
      final workoutDifficulty = estimateWorkoutDifficulty(workout);
      double confidence = 0.0;
      String reason = '';

      switch (userLevel) {
        case 'beginner':
          if (workoutDifficulty == WorkoutDifficulty.beginner) {
            confidence = 0.7;
            reason = 'Perfect for beginners';
          } else if (workoutDifficulty == WorkoutDifficulty.intermediate) {
            confidence = 0.3;
            reason = 'Ready to level up?';
          }
          break;
        case 'intermediate':
          if (workoutDifficulty == WorkoutDifficulty.intermediate) {
            confidence = 0.7;
            reason = 'Matches your fitness level';
          } else if (workoutDifficulty == WorkoutDifficulty.beginner) {
            confidence = 0.4;
            reason = 'Good for active recovery';
          } else if (workoutDifficulty == WorkoutDifficulty.advanced) {
            confidence = 0.3;
            reason = 'Challenge yourself';
          }
          break;
        case 'advanced':
          if (workoutDifficulty == WorkoutDifficulty.advanced) {
            confidence = 0.7;
            reason = 'Advanced challenge';
          } else if (workoutDifficulty == WorkoutDifficulty.intermediate) {
            confidence = 0.4;
            reason = 'Good for recovery days';
          }
          break;
      }

      if (confidence > 0) {
        recommendations.add(WorkoutRecommendation(
          workout: workout,
          type: RecommendationType.difficulty,
          reason: reason,
          confidence: confidence,
        ));
      }
    }

    return recommendations;
  }

  @visibleForTesting
  List<WorkoutRecommendation> getMuscleBalanceRecommendations(
    List<Workout> availableWorkouts,
  ) {
    if (_workoutHistory.isEmpty) return [];

    final recommendations = <WorkoutRecommendation>[];

    // Analyze muscle group frequency in recent history
    final recentHistory = _workoutHistory
        .where((h) => DateTime.now().difference(h.completedAt).inDays <= 14)
        .toList();

    if (recentHistory.isEmpty) return recommendations;

    final muscleFrequency = <String, int>{};
    for (final history in recentHistory) {
      for (final muscle in history.muscleGroups) {
        muscleFrequency[muscle] = (muscleFrequency[muscle] ?? 0) + 1;
      }
    }

    // Find underworked muscle groups
    final allMuscles = ['Chest', 'Back', 'Shoulders', 'Arms', 'Legs', 'Core'];
    final underworkedMuscles = allMuscles
        .where((muscle) => (muscleFrequency[muscle] ?? 0) < 2)
        .toList();

    for (final workout in availableWorkouts) {
      final workoutMuscles = workout.primaryMuscles;
      final targetedUnderworked = workoutMuscles
          .where((muscle) => underworkedMuscles.contains(muscle))
          .toList();

      if (targetedUnderworked.isNotEmpty) {
        final confidence =
            (targetedUnderworked.length / workoutMuscles.length) * 0.6;
        recommendations.add(WorkoutRecommendation(
          workout: workout,
          type: RecommendationType.muscleBalance,
          reason:
              'Balance your training with ${targetedUnderworked.join(", ")}',
          confidence: confidence,
        ));
      }
    }

    return recommendations;
  }

  @visibleForTesting
  List<WorkoutRecommendation> getPopularRecommendations(
    List<Workout> availableWorkouts,
  ) {
    // Simple popularity based on workout complexity and variety
    final recommendations = <WorkoutRecommendation>[];

    for (final workout in availableWorkouts) {
      double popularity = 0.0;

      // Favor workouts with good exercise variety
      if (workout.exercises.length >= 4 && workout.exercises.length <= 8) {
        popularity += 0.3;
      }

      // Favor workouts with balanced muscle targeting
      final uniqueMuscles = workout.primaryMuscles.toSet();
      if (uniqueMuscles.length >= 2) {
        popularity += 0.2;
      }

      // Favor workouts with reasonable duration
      final estimatedDuration = estimateWorkoutDuration(workout);
      if (estimatedDuration >= 20 && estimatedDuration <= 60) {
        popularity += 0.2;
      }

      if (popularity > 0.4) {
        recommendations.add(WorkoutRecommendation(
          workout: workout,
          type: RecommendationType.popular,
          reason: 'Popular balanced workout',
          confidence: popularity,
        ));
      }
    }

    return recommendations;
  }

  List<WorkoutRecommendation> _getSimilarWorkoutRecommendations(
    List<Workout> availableWorkouts,
  ) {
    if (_workoutHistory.isEmpty) return [];

    final recommendations = <WorkoutRecommendation>[];

    // Find highly rated recent workouts
    final goodWorkouts = _workoutHistory
        .where((h) => (h.rating ?? 0) >= 4.0)
        .where((h) => DateTime.now().difference(h.completedAt).inDays <= 30)
        .toList();

    if (goodWorkouts.isEmpty) return recommendations;

    for (final workout in availableWorkouts) {
      for (final goodWorkout in goodWorkouts) {
        final similarity = calculateWorkoutSimilarity(workout, goodWorkout);

        if (similarity > 0.5) {
          recommendations.add(WorkoutRecommendation(
            workout: workout,
            type: RecommendationType.similar,
            reason: 'Similar to workouts you enjoyed',
            confidence: similarity * 0.8,
          ));
          break; // Only add once per workout
        }
      }
    }

    return recommendations;
  }

  Future<void> _loadUserProfile(String userId) async {
    if (_supabase == null) {
      _userProfile = UserProfile(userId: userId);
      return;
    }

    try {
      final response =
          await _supabase!.from('profiles').select().eq('id', userId).single();

      _userProfile = UserProfile.fromJson(response);
    } catch (e) {
      debugPrint('Error loading user profile: $e');
      // Create default profile
      _userProfile = UserProfile(userId: userId);
    }
  }

  Future<void> _loadWorkoutHistory(String userId) async {
    if (_supabase == null) {
      _workoutHistory = [];
      return;
    }

    try {
      final response = await _supabase!
          .from('completed_workouts')
          .select('''
            workout_id,
            completed_at,
            duration,
            rating,
            workouts!inner(
              workout_exercises!inner(
                exercises!inner(
                  primary_muscle,
                  equipment
                )
              )
            )
          ''')
          .eq('user_id', userId)
          .order('completed_at', ascending: false)
          .limit(50);

      _workoutHistory = response.map<WorkoutHistory>((item) {
        final workout = item['workouts'];
        final exercises = workout['workout_exercises'] as List;

        final muscleGroups = exercises
            .map((e) => e['exercises']['primary_muscle'] as String?)
            .where((m) => m != null)
            .cast<String>()
            .toSet()
            .toList();

        final equipment = exercises
            .map((e) => e['exercises']['equipment'] as String?)
            .where((e) => e != null && e != 'None')
            .cast<String>()
            .toSet()
            .toList();

        return WorkoutHistory(
          workoutId: item['workout_id'],
          completedAt: DateTime.parse(item['completed_at']),
          duration: item['duration'] ?? 0,
          rating: item['rating']?.toDouble(),
          muscleGroups: muscleGroups,
          equipment: equipment,
        );
      }).toList();
    } catch (e) {
      debugPrint('Error loading workout history: $e');
      _workoutHistory = [];
    }
  }

  @visibleForTesting
  WorkoutDifficulty estimateWorkoutDifficulty(Workout workout) {
    final exerciseCount = workout.exercises.length;
    final totalSets =
        workout.exercises.fold<int>(0, (sum, ex) => sum + ex.sets);

    if (exerciseCount <= 3 && totalSets <= 9) {
      return WorkoutDifficulty.beginner;
    } else if (exerciseCount <= 6 && totalSets <= 18) {
      return WorkoutDifficulty.intermediate;
    } else {
      return WorkoutDifficulty.advanced;
    }
  }

  @visibleForTesting
  double estimateWorkoutIntensity(Workout workout) {
    // Simple intensity estimation based on exercise count and equipment
    double intensity = 0.0;

    // More exercises = higher intensity
    intensity += (workout.exercises.length / 10.0).clamp(0.0, 0.5);

    // Equipment usage suggests higher intensity
    if (workout.equipmentNeeded.isNotEmpty) {
      intensity += 0.3;
    }

    // Compound movements (multiple muscle groups) = higher intensity
    final uniqueMuscles = workout.primaryMuscles.toSet();
    if (uniqueMuscles.length > 2) {
      intensity += 0.2;
    }

    return intensity.clamp(0.0, 1.0);
  }

  @visibleForTesting
  int estimateWorkoutDuration(Workout workout) {
    // Estimate duration based on exercises and sets
    final totalSets =
        workout.exercises.fold<int>(0, (sum, ex) => sum + ex.sets);
    return (totalSets * 1.75).round(); // 1.75 minutes per set on average
  }

  @visibleForTesting
  double calculateWorkoutSimilarity(Workout workout, WorkoutHistory history) {
    double similarity = 0.0;

    // Compare muscle groups
    final workoutMuscles = workout.primaryMuscles.toSet();
    final historyMuscles = history.muscleGroups.toSet();
    final muscleOverlap = workoutMuscles.intersection(historyMuscles);
    if (workoutMuscles.isNotEmpty) {
      similarity += (muscleOverlap.length / workoutMuscles.length) * 0.5;
    }

    // Compare equipment
    final workoutEquipment = workout.equipmentNeeded.toSet();
    final historyEquipment = history.equipment.toSet();
    final equipmentOverlap = workoutEquipment.intersection(historyEquipment);
    if (workoutEquipment.isNotEmpty || historyEquipment.isNotEmpty) {
      final totalEquipment = workoutEquipment.union(historyEquipment);
      similarity += (equipmentOverlap.length / totalEquipment.length) * 0.3;
    }

    // Compare estimated duration
    final workoutDuration = estimateWorkoutDuration(workout);
    final durationDiff = (workoutDuration - history.duration).abs();
    if (durationDiff <= 10) {
      similarity += 0.2;
    } else if (durationDiff <= 20) {
      similarity += 0.1;
    }

    return similarity.clamp(0.0, 1.0);
  }
}
