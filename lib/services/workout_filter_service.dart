import 'package:flutter/foundation.dart';
import '../models/workout.dart';

enum WorkoutDifficulty { beginner, intermediate, advanced }

enum WorkoutCategory { strength, cardio, flexibility, hiit, bodyweight }

class WorkoutFilterCriteria {
  final Set<String> muscleGroups;
  final Set<String> equipment;
  final Set<WorkoutDifficulty> difficulties;
  final Set<WorkoutCategory> categories;
  final int? minDuration;
  final int? maxDuration;
  final String searchQuery;

  const WorkoutFilterCriteria({
    this.muscleGroups = const {},
    this.equipment = const {},
    this.difficulties = const {},
    this.categories = const {},
    this.minDuration,
    this.maxDuration,
    this.searchQuery = '',
  });

  WorkoutFilterCriteria copyWith({
    Set<String>? muscleGroups,
    Set<String>? equipment,
    Set<WorkoutDifficulty>? difficulties,
    Set<WorkoutCategory>? categories,
    int? minDuration,
    int? maxDuration,
    String? searchQuery,
  }) {
    return WorkoutFilterCriteria(
      muscleGroups: muscleGroups ?? this.muscleGroups,
      equipment: equipment ?? this.equipment,
      difficulties: difficulties ?? this.difficulties,
      categories: categories ?? this.categories,
      minDuration: minDuration ?? this.minDuration,
      maxDuration: maxDuration ?? this.maxDuration,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  bool get hasActiveFilters {
    return muscleGroups.isNotEmpty ||
        equipment.isNotEmpty ||
        difficulties.isNotEmpty ||
        categories.isNotEmpty ||
        minDuration != null ||
        maxDuration != null ||
        searchQuery.isNotEmpty;
  }

  int get activeFilterCount {
    int count = 0;
    if (muscleGroups.isNotEmpty) count++;
    if (equipment.isNotEmpty) count++;
    if (difficulties.isNotEmpty) count++;
    if (categories.isNotEmpty) count++;
    if (minDuration != null || maxDuration != null) count++;
    if (searchQuery.isNotEmpty) count++;
    return count;
  }
}

class WorkoutFilterService extends ChangeNotifier {
  WorkoutFilterCriteria _criteria = const WorkoutFilterCriteria();
  List<Workout> _allWorkouts = [];
  List<Workout> _filteredWorkouts = [];
  List<String> _searchSuggestions = [];

  WorkoutFilterCriteria get criteria => _criteria;
  List<Workout> get filteredWorkouts => _filteredWorkouts;
  List<String> get searchSuggestions => _searchSuggestions;

  // Available filter options
  static const List<String> availableMuscleGroups = [
    'Chest',
    'Back',
    'Shoulders',
    'Arms',
    'Legs',
    'Core',
    'Glutes',
    'Biceps',
    'Triceps',
    'Quadriceps',
    'Hamstrings',
    'Calves',
  ];

  static const List<String> availableEquipment = [
    'Dumbbells',
    'Barbell',
    'Resistance Bands',
    'Pull-up Bar',
    'Bench',
    'Kettlebell',
    'Cable Machine',
    'Bodyweight',
    'Medicine Ball',
    'Foam Roller',
  ];

  void setWorkouts(List<Workout> workouts) {
    _allWorkouts = workouts;
    _generateSearchSuggestions();
    _applyFilters();
  }

  void updateCriteria(WorkoutFilterCriteria newCriteria) {
    _criteria = newCriteria;
    _applyFilters();
    notifyListeners();
  }

  void clearFilters() {
    _criteria = const WorkoutFilterCriteria();
    _applyFilters();
    notifyListeners();
  }

  void toggleMuscleGroup(String muscleGroup) {
    final newMuscleGroups = Set<String>.from(_criteria.muscleGroups);
    if (newMuscleGroups.contains(muscleGroup)) {
      newMuscleGroups.remove(muscleGroup);
    } else {
      newMuscleGroups.add(muscleGroup);
    }
    updateCriteria(_criteria.copyWith(muscleGroups: newMuscleGroups));
  }

  void toggleEquipment(String equipment) {
    final newEquipment = Set<String>.from(_criteria.equipment);
    if (newEquipment.contains(equipment)) {
      newEquipment.remove(equipment);
    } else {
      newEquipment.add(equipment);
    }
    updateCriteria(_criteria.copyWith(equipment: newEquipment));
  }

  void toggleDifficulty(WorkoutDifficulty difficulty) {
    final newDifficulties = Set<WorkoutDifficulty>.from(_criteria.difficulties);
    if (newDifficulties.contains(difficulty)) {
      newDifficulties.remove(difficulty);
    } else {
      newDifficulties.add(difficulty);
    }
    updateCriteria(_criteria.copyWith(difficulties: newDifficulties));
  }

  void toggleCategory(WorkoutCategory category) {
    final newCategories = Set<WorkoutCategory>.from(_criteria.categories);
    if (newCategories.contains(category)) {
      newCategories.remove(category);
    } else {
      newCategories.add(category);
    }
    updateCriteria(_criteria.copyWith(categories: newCategories));
  }

  void updateSearchQuery(String query) {
    updateCriteria(_criteria.copyWith(searchQuery: query));
  }

  void setDurationRange(int? minDuration, int? maxDuration) {
    _criteria = WorkoutFilterCriteria(
      muscleGroups: _criteria.muscleGroups,
      equipment: _criteria.equipment,
      difficulties: _criteria.difficulties,
      categories: _criteria.categories,
      minDuration: minDuration,
      maxDuration: maxDuration,
      searchQuery: _criteria.searchQuery,
    );
    _applyFilters();
    notifyListeners();
  }

  void _applyFilters() {
    _filteredWorkouts = _allWorkouts.where((workout) {
      return _matchesSearchQuery(workout) &&
          _matchesMuscleGroups(workout) &&
          _matchesEquipment(workout) &&
          _matchesDifficulty(workout) &&
          _matchesCategory(workout) &&
          _matchesDuration(workout);
    }).toList();

    // Sort by relevance (exact matches first, then partial matches)
    if (_criteria.searchQuery.isNotEmpty) {
      _filteredWorkouts.sort((a, b) =>
          _calculateRelevanceScore(b, _criteria.searchQuery)
              .compareTo(_calculateRelevanceScore(a, _criteria.searchQuery)));
    }
  }

  bool _matchesSearchQuery(Workout workout) {
    if (_criteria.searchQuery.isEmpty) return true;

    final query = _criteria.searchQuery.toLowerCase();
    final workoutName = workout.name.toLowerCase();
    final workoutDescription = workout.aiDescription?.toLowerCase() ?? '';

    // Check workout name and description
    if (workoutName.contains(query) || workoutDescription.contains(query)) {
      return true;
    }

    // Check exercise names
    for (final exercise in workout.exercises) {
      if (exercise.exercise.name.toLowerCase().contains(query)) {
        return true;
      }
    }

    return false;
  }

  bool _matchesMuscleGroups(Workout workout) {
    if (_criteria.muscleGroups.isEmpty) return true;

    final workoutMuscles =
        workout.primaryMuscles.map((m) => m.toLowerCase()).toSet();
    return _criteria.muscleGroups
        .any((muscle) => workoutMuscles.contains(muscle.toLowerCase()));
  }

  bool _matchesEquipment(Workout workout) {
    if (_criteria.equipment.isEmpty) return true;

    final workoutEquipment =
        workout.equipmentNeeded.map((e) => e.toLowerCase()).toSet();

    // Special case for bodyweight workouts
    if (_criteria.equipment.contains('Bodyweight') &&
        workoutEquipment.isEmpty) {
      return true;
    }

    return _criteria.equipment
        .any((equipment) => workoutEquipment.contains(equipment.toLowerCase()));
  }

  bool _matchesDifficulty(Workout workout) {
    if (_criteria.difficulties.isEmpty) return true;

    final difficulty = _estimateWorkoutDifficulty(workout);
    return _criteria.difficulties.contains(difficulty);
  }

  bool _matchesCategory(Workout workout) {
    if (_criteria.categories.isEmpty) return true;

    final category = _estimateWorkoutCategory(workout);
    return _criteria.categories.contains(category);
  }

  bool _matchesDuration(Workout workout) {
    if (_criteria.minDuration == null && _criteria.maxDuration == null) {
      return true;
    }

    final estimatedDuration = _estimateWorkoutDuration(workout);

    if (_criteria.minDuration != null &&
        estimatedDuration < _criteria.minDuration!) {
      return false;
    }

    if (_criteria.maxDuration != null &&
        estimatedDuration > _criteria.maxDuration!) {
      return false;
    }

    return true;
  }

  WorkoutDifficulty _estimateWorkoutDifficulty(Workout workout) {
    // Simple heuristic based on exercise count and complexity
    final exerciseCount = workout.exercises.length;
    final totalSets =
        workout.exercises.fold<int>(0, (sum, ex) => sum + ex.sets);

    if (exerciseCount <= 3 && totalSets <= 9) {
      return WorkoutDifficulty.beginner;
    } else if (exerciseCount <= 6 && totalSets <= 18) {
      return WorkoutDifficulty.intermediate;
    } else {
      return WorkoutDifficulty.advanced;
    }
  }

  WorkoutCategory _estimateWorkoutCategory(Workout workout) {
    final muscles = workout.primaryMuscles.map((m) => m.toLowerCase()).toList();
    final hasEquipment = workout.equipmentNeeded.isNotEmpty;

    // Simple categorization logic
    if (!hasEquipment) {
      return WorkoutCategory.bodyweight;
    } else if (muscles
        .any((m) => ['chest', 'back', 'shoulders', 'arms'].contains(m))) {
      return WorkoutCategory.strength;
    } else {
      return WorkoutCategory.strength; // Default to strength
    }
  }

  int _estimateWorkoutDuration(Workout workout) {
    // Estimate duration based on exercises and sets
    // Assume 45 seconds per set + 60 seconds rest
    final totalSets =
        workout.exercises.fold<int>(0, (sum, ex) => sum + ex.sets);
    return (totalSets * 1.75).round(); // 1.75 minutes per set on average
  }

  int _calculateRelevanceScore(Workout workout, String query) {
    int score = 0;
    final lowerQuery = query.toLowerCase();

    // Exact name match gets highest score
    if (workout.name.toLowerCase() == lowerQuery) {
      score += 100;
    } else if (workout.name.toLowerCase().contains(lowerQuery)) {
      score += 50;
    }

    // Exercise name matches
    for (final exercise in workout.exercises) {
      if (exercise.exercise.name.toLowerCase().contains(lowerQuery)) {
        score += 25;
      }
    }

    // Description matches
    if (workout.aiDescription?.toLowerCase().contains(lowerQuery) == true) {
      score += 10;
    }

    return score;
  }

  void _generateSearchSuggestions() {
    final suggestions = <String>{};

    // Add workout names
    for (final workout in _allWorkouts) {
      suggestions.add(workout.name);

      // Add exercise names
      for (final exercise in workout.exercises) {
        suggestions.add(exercise.exercise.name);
      }

      // Add muscle groups
      suggestions.addAll(workout.primaryMuscles);

      // Add equipment
      suggestions.addAll(workout.equipmentNeeded);
    }

    _searchSuggestions = suggestions.toList()..sort();
  }

  List<String> getSearchSuggestions(String query) {
    if (query.isEmpty) return [];

    final lowerQuery = query.toLowerCase();
    return _searchSuggestions
        .where((suggestion) => suggestion.toLowerCase().contains(lowerQuery))
        .take(5)
        .toList();
  }
}
