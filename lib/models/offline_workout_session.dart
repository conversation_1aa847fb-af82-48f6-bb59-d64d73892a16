enum SyncStatus {
  pending,
  syncing,
  synced,
  failed;

  String get label {
    switch (this) {
      case SyncStatus.pending:
        return 'Pending';
      case SyncStatus.syncing:
        return 'Syncing';
      case SyncStatus.synced:
        return 'Synced';
      case SyncStatus.failed:
        return 'Failed';
    }
  }

  static SyncStatus fromString(String? value) {
    switch (value?.toLowerCase()) {
      case 'pending':
        return SyncStatus.pending;
      case 'syncing':
        return SyncStatus.syncing;
      case 'synced':
        return SyncStatus.synced;
      case 'failed':
        return SyncStatus.failed;
      default:
        return SyncStatus.pending;
    }
  }
}

class OfflineWorkoutSession {
  final String sessionId;
  final String workoutId;
  final String userId;
  final DateTime startTime;
  final DateTime? endTime;
  final List<OfflineSetData> completedSets;
  final Map<String, dynamic> sessionMetadata;
  final SyncStatus syncStatus;
  final DateTime lastModified;
  final int? duration;
  final String? notes;
  final int? rating;
  final List<String> exerciseOrder;
  final Map<String, dynamic>? lastState;
  final int retryCount;
  final DateTime? lastSyncAttempt;
  final String? syncError;

  OfflineWorkoutSession({
    required this.sessionId,
    required this.workoutId,
    required this.userId,
    required this.startTime,
    this.endTime,
    this.completedSets = const [],
    this.sessionMetadata = const {},
    this.syncStatus = SyncStatus.pending,
    required this.lastModified,
    this.duration,
    this.notes,
    this.rating,
    this.exerciseOrder = const [],
    this.lastState,
    this.retryCount = 0,
    this.lastSyncAttempt,
    this.syncError,
  });

  factory OfflineWorkoutSession.fromJson(Map<String, dynamic> json) {
    return OfflineWorkoutSession(
      sessionId: json['session_id'],
      workoutId: json['workout_id'],
      userId: json['user_id'],
      startTime: DateTime.parse(json['start_time']),
      endTime:
          json['end_time'] != null ? DateTime.parse(json['end_time']) : null,
      completedSets: json['completed_sets'] != null
          ? (json['completed_sets'] as List)
              .map((e) => OfflineSetData.fromJson(e))
              .toList()
          : [],
      sessionMetadata: json['session_metadata'] ?? {},
      syncStatus: SyncStatus.fromString(json['sync_status']),
      lastModified: DateTime.parse(json['last_modified']),
      duration: json['duration'],
      notes: json['notes'],
      rating: json['rating'],
      exerciseOrder: json['exercise_order'] != null
          ? List<String>.from(json['exercise_order'])
          : [],
      lastState: json['last_state'],
      retryCount: json['retry_count'] ?? 0,
      lastSyncAttempt: json['last_sync_attempt'] != null
          ? DateTime.parse(json['last_sync_attempt'])
          : null,
      syncError: json['sync_error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'session_id': sessionId,
      'workout_id': workoutId,
      'user_id': userId,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'completed_sets': completedSets.map((e) => e.toJson()).toList(),
      'session_metadata': sessionMetadata,
      'sync_status': syncStatus.name,
      'last_modified': lastModified.toIso8601String(),
      'duration': duration,
      'notes': notes,
      'rating': rating,
      'exercise_order': exerciseOrder,
      'last_state': lastState,
      'retry_count': retryCount,
      'last_sync_attempt': lastSyncAttempt?.toIso8601String(),
      'sync_error': syncError,
    };
  }

  // Computed properties
  bool get isCompleted => endTime != null;

  bool get needsSync =>
      syncStatus == SyncStatus.pending || syncStatus == SyncStatus.failed;

  bool get isSynced => syncStatus == SyncStatus.synced;

  Duration get sessionDuration {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }

  int get totalSets => completedSets.length;

  int get totalReps => completedSets.fold<int>(0, (sum, set) => sum + set.reps);

  double get totalVolume => completedSets.fold<double>(
        0.0,
        (sum, set) => sum + (set.weight * set.reps),
      );

  List<String> get uniqueExerciseIds =>
      completedSets.map((set) => set.exerciseId).toSet().toList();

  int get exerciseCount => uniqueExerciseIds.length;

  String get formattedDuration {
    final duration = sessionDuration;
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  bool get canRetrySync {
    if (syncStatus != SyncStatus.failed) return false;
    if (retryCount >= 5) return false; // Max 5 retries

    // Allow retry after exponential backoff
    if (lastSyncAttempt == null) return true;

    final backoffMinutes = [1, 2, 5, 10, 30][retryCount.clamp(0, 4)];
    final nextRetryTime =
        lastSyncAttempt!.add(Duration(minutes: backoffMinutes));

    return DateTime.now().isAfter(nextRetryTime);
  }

  // Helper methods
  OfflineWorkoutSession copyWith({
    String? sessionId,
    String? workoutId,
    String? userId,
    DateTime? startTime,
    DateTime? endTime,
    List<OfflineSetData>? completedSets,
    Map<String, dynamic>? sessionMetadata,
    SyncStatus? syncStatus,
    DateTime? lastModified,
    int? duration,
    String? notes,
    int? rating,
    List<String>? exerciseOrder,
    Map<String, dynamic>? lastState,
    int? retryCount,
    DateTime? lastSyncAttempt,
    String? syncError,
  }) {
    return OfflineWorkoutSession(
      sessionId: sessionId ?? this.sessionId,
      workoutId: workoutId ?? this.workoutId,
      userId: userId ?? this.userId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      completedSets: completedSets ?? this.completedSets,
      sessionMetadata: sessionMetadata ?? this.sessionMetadata,
      syncStatus: syncStatus ?? this.syncStatus,
      lastModified: lastModified ?? this.lastModified,
      duration: duration ?? this.duration,
      notes: notes ?? this.notes,
      rating: rating ?? this.rating,
      exerciseOrder: exerciseOrder ?? this.exerciseOrder,
      lastState: lastState ?? this.lastState,
      retryCount: retryCount ?? this.retryCount,
      lastSyncAttempt: lastSyncAttempt ?? this.lastSyncAttempt,
      syncError: syncError ?? this.syncError,
    );
  }

  OfflineWorkoutSession addSet(OfflineSetData setData) {
    final updatedSets = List<OfflineSetData>.from(completedSets)..add(setData);
    return copyWith(
      completedSets: updatedSets,
      lastModified: DateTime.now(),
    );
  }

  OfflineWorkoutSession updateSet(int index, OfflineSetData setData) {
    if (index < 0 || index >= completedSets.length) return this;

    final updatedSets = List<OfflineSetData>.from(completedSets);
    updatedSets[index] = setData;

    return copyWith(
      completedSets: updatedSets,
      lastModified: DateTime.now(),
    );
  }

  OfflineWorkoutSession markAsCompleted({
    String? notes,
    int? rating,
  }) {
    return copyWith(
      endTime: DateTime.now(),
      duration: sessionDuration.inSeconds,
      notes: notes ?? this.notes,
      rating: rating ?? this.rating,
      lastModified: DateTime.now(),
    );
  }

  OfflineWorkoutSession markSyncFailed(String error) {
    return copyWith(
      syncStatus: SyncStatus.failed,
      syncError: error,
      retryCount: retryCount + 1,
      lastSyncAttempt: DateTime.now(),
      lastModified: DateTime.now(),
    );
  }

  OfflineWorkoutSession markSyncing() {
    return copyWith(
      syncStatus: SyncStatus.syncing,
      syncError: null,
      lastSyncAttempt: DateTime.now(),
      lastModified: DateTime.now(),
    );
  }

  OfflineWorkoutSession markSynced() {
    return copyWith(
      syncStatus: SyncStatus.synced,
      syncError: null,
      lastModified: DateTime.now(),
    );
  }
}

class OfflineSetData {
  final String setId;
  final String exerciseId;
  final String exerciseName;
  final int setNumber;
  final double weight;
  final int reps;
  final DateTime completedAt;
  final Duration restTime;
  final int? difficultyRating;
  final String? notes;
  final Map<String, dynamic> metadata;

  OfflineSetData({
    required this.setId,
    required this.exerciseId,
    required this.exerciseName,
    required this.setNumber,
    required this.weight,
    required this.reps,
    required this.completedAt,
    this.restTime = Duration.zero,
    this.difficultyRating,
    this.notes,
    this.metadata = const {},
  });

  factory OfflineSetData.fromJson(Map<String, dynamic> json) {
    return OfflineSetData(
      setId: json['set_id'],
      exerciseId: json['exercise_id'],
      exerciseName: json['exercise_name'],
      setNumber: json['set_number'] ?? 1,
      weight: json['weight']?.toDouble() ?? 0.0,
      reps: json['reps'] ?? 0,
      completedAt: DateTime.parse(json['completed_at']),
      restTime: Duration(seconds: json['rest_time_seconds'] ?? 0),
      difficultyRating: json['difficulty_rating'],
      notes: json['notes'],
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'set_id': setId,
      'exercise_id': exerciseId,
      'exercise_name': exerciseName,
      'set_number': setNumber,
      'weight': weight,
      'reps': reps,
      'completed_at': completedAt.toIso8601String(),
      'rest_time_seconds': restTime.inSeconds,
      'difficulty_rating': difficultyRating,
      'notes': notes,
      'metadata': metadata,
    };
  }

  // Computed properties
  double get volume => weight * reps;

  String get formattedWeight =>
      weight > 0 ? '${weight.toStringAsFixed(1)} lbs' : 'Bodyweight';

  String get formattedReps => '$reps reps';

  String get formattedVolume => '${volume.toStringAsFixed(1)} lbs';

  String get formattedRestTime {
    final minutes = restTime.inMinutes;
    final seconds = restTime.inSeconds % 60;

    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  String get difficultyLabel {
    switch (difficultyRating) {
      case 1:
        return 'Very Easy';
      case 2:
        return 'Easy';
      case 3:
        return 'Moderate';
      case 4:
        return 'Hard';
      case 5:
        return 'Very Hard';
      default:
        return 'Not Rated';
    }
  }

  // Helper methods
  OfflineSetData copyWith({
    String? setId,
    String? exerciseId,
    String? exerciseName,
    int? setNumber,
    double? weight,
    int? reps,
    DateTime? completedAt,
    Duration? restTime,
    int? difficultyRating,
    bool clearDifficultyRating = false,
    String? notes,
    Map<String, dynamic>? metadata,
  }) {
    return OfflineSetData(
      setId: setId ?? this.setId,
      exerciseId: exerciseId ?? this.exerciseId,
      exerciseName: exerciseName ?? this.exerciseName,
      setNumber: setNumber ?? this.setNumber,
      weight: weight ?? this.weight,
      reps: reps ?? this.reps,
      completedAt: completedAt ?? this.completedAt,
      restTime: restTime ?? this.restTime,
      difficultyRating: clearDifficultyRating
          ? null
          : (difficultyRating ?? this.difficultyRating),
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
    );
  }

  // Convert to format suitable for Supabase sync
  Map<String, dynamic> toSupabaseFormat() {
    return {
      'id': setId,
      'exercise_id': exerciseId,
      'set_number': setNumber,
      'weight': weight,
      'reps': reps,
      'completed_at': completedAt.toIso8601String(),
      'rest_time': restTime.inSeconds,
      'difficulty_rating': difficultyRating,
      'notes': notes,
      // Add any additional fields required by Supabase schema
    };
  }
}

class OfflineSessionSummary {
  final int totalSessions;
  final int pendingSyncSessions;
  final int failedSyncSessions;
  final DateTime? oldestPendingSession;
  final double totalOfflineVolume;
  final Duration totalOfflineTime;

  OfflineSessionSummary({
    required this.totalSessions,
    required this.pendingSyncSessions,
    required this.failedSyncSessions,
    this.oldestPendingSession,
    required this.totalOfflineVolume,
    required this.totalOfflineTime,
  });

  // Computed properties
  bool get hasUnsyncedData => pendingSyncSessions > 0 || failedSyncSessions > 0;

  int get unsyncedSessions => pendingSyncSessions + failedSyncSessions;

  String get syncStatusSummary {
    if (!hasUnsyncedData) return 'All data synced';
    return '$unsyncedSessions sessions need sync';
  }

  String get formattedTotalTime {
    final hours = totalOfflineTime.inHours;
    final minutes = totalOfflineTime.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
