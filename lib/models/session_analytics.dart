class SessionAnalytics {
  final String sessionId;
  final Duration totalDuration;
  final int setsCompleted;
  final int totalReps;
  final double totalVolume;
  final int estimatedCalories;
  final Map<String, ExercisePerformance> exercisePerformance;
  final List<PersonalRecord> newRecords;
  final double improvementScore;
  final DateTime sessionDate;
  final String? notes;
  final int? overallRating;

  SessionAnalytics({
    required this.sessionId,
    required this.totalDuration,
    required this.setsCompleted,
    required this.totalReps,
    required this.totalVolume,
    required this.estimatedCalories,
    this.exercisePerformance = const {},
    this.newRecords = const [],
    required this.improvementScore,
    required this.sessionDate,
    this.notes,
    this.overallRating,
  });

  factory SessionAnalytics.fromJson(Map<String, dynamic> json) {
    return SessionAnalytics(
      sessionId: json['session_id'],
      totalDuration: Duration(seconds: json['total_duration_seconds'] ?? 0),
      setsCompleted: json['sets_completed'] ?? 0,
      totalReps: json['total_reps'] ?? 0,
      totalVolume: json['total_volume']?.toDouble() ?? 0.0,
      estimatedCalories: json['estimated_calories'] ?? 0,
      exercisePerformance: json['exercise_performance'] != null
          ? Map<String, ExercisePerformance>.from(
              (json['exercise_performance'] as Map).map(
                (key, value) => MapEntry(
                  key,
                  ExercisePerformance.fromJson(value),
                ),
              ),
            )
          : {},
      newRecords: json['new_records'] != null
          ? (json['new_records'] as List)
              .map((e) => PersonalRecord.fromJson(e))
              .toList()
          : [],
      improvementScore: json['improvement_score']?.toDouble() ?? 0.0,
      sessionDate: DateTime.parse(json['session_date']),
      notes: json['notes'],
      overallRating: json['overall_rating'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'session_id': sessionId,
      'total_duration_seconds': totalDuration.inSeconds,
      'sets_completed': setsCompleted,
      'total_reps': totalReps,
      'total_volume': totalVolume,
      'estimated_calories': estimatedCalories,
      'exercise_performance': exercisePerformance.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
      'new_records': newRecords.map((e) => e.toJson()).toList(),
      'improvement_score': improvementScore,
      'session_date': sessionDate.toIso8601String(),
      'notes': notes,
      'overall_rating': overallRating,
    };
  }

  // Computed properties
  String get formattedDuration {
    final hours = totalDuration.inHours;
    final minutes = totalDuration.inMinutes % 60;
    final seconds = totalDuration.inSeconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  double get averageVolumePerSet =>
      setsCompleted > 0 ? totalVolume / setsCompleted : 0.0;

  double get averageRepsPerSet =>
      setsCompleted > 0 ? totalReps / setsCompleted : 0.0;

  double get caloriesPerMinute => totalDuration.inMinutes > 0
      ? estimatedCalories / totalDuration.inMinutes
      : 0.0;

  bool get hasNewRecords => newRecords.isNotEmpty;

  int get exerciseCount => exercisePerformance.length;

  List<String> get muscleGroupsWorked {
    return exercisePerformance.values
        .expand((performance) => performance.muscleGroups)
        .toSet()
        .toList();
  }

  String get performanceGrade {
    if (improvementScore >= 90) return 'A+';
    if (improvementScore >= 85) return 'A';
    if (improvementScore >= 70) return 'B';
    if (improvementScore >= 60) return 'C';
    return 'D';
  }

  SessionComparison compareWith(SessionAnalytics? previousSession) {
    if (previousSession == null) {
      return SessionComparison(
        volumeChange: 0.0,
        durationChange: Duration.zero,
        setsChange: 0,
        repsChange: 0,
        improvementChange: 0.0,
        isImprovement: false,
      );
    }

    final volumeChange = totalVolume - previousSession.totalVolume;
    final durationChange = totalDuration - previousSession.totalDuration;
    final setsChange = setsCompleted - previousSession.setsCompleted;
    final repsChange = totalReps - previousSession.totalReps;
    final improvementChange =
        improvementScore - previousSession.improvementScore;

    return SessionComparison(
      volumeChange: volumeChange,
      durationChange: durationChange,
      setsChange: setsChange,
      repsChange: repsChange,
      improvementChange: improvementChange,
      isImprovement: volumeChange > 0 || improvementChange > 0,
    );
  }
}

class ExercisePerformance {
  final String exerciseId;
  final String exerciseName;
  final int setsCompleted;
  final int totalReps;
  final double totalVolume;
  final double maxWeight;
  final int maxReps;
  final List<String> muscleGroups;
  final Duration timeSpent;
  final List<SetPerformance> setDetails;
  final double? averageDifficulty;

  ExercisePerformance({
    required this.exerciseId,
    required this.exerciseName,
    required this.setsCompleted,
    required this.totalReps,
    required this.totalVolume,
    required this.maxWeight,
    required this.maxReps,
    this.muscleGroups = const [],
    required this.timeSpent,
    this.setDetails = const [],
    this.averageDifficulty,
  });

  factory ExercisePerformance.fromJson(Map<String, dynamic> json) {
    return ExercisePerformance(
      exerciseId: json['exercise_id'],
      exerciseName: json['exercise_name'],
      setsCompleted: json['sets_completed'] ?? 0,
      totalReps: json['total_reps'] ?? 0,
      totalVolume: json['total_volume']?.toDouble() ?? 0.0,
      maxWeight: json['max_weight']?.toDouble() ?? 0.0,
      maxReps: json['max_reps'] ?? 0,
      muscleGroups: json['muscle_groups'] != null
          ? List<String>.from(json['muscle_groups'])
          : [],
      timeSpent: Duration(seconds: json['time_spent_seconds'] ?? 0),
      setDetails: json['set_details'] != null
          ? (json['set_details'] as List)
              .map((e) => SetPerformance.fromJson(e))
              .toList()
          : [],
      averageDifficulty: json['average_difficulty']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'exercise_id': exerciseId,
      'exercise_name': exerciseName,
      'sets_completed': setsCompleted,
      'total_reps': totalReps,
      'total_volume': totalVolume,
      'max_weight': maxWeight,
      'max_reps': maxReps,
      'muscle_groups': muscleGroups,
      'time_spent_seconds': timeSpent.inSeconds,
      'set_details': setDetails.map((e) => e.toJson()).toList(),
      'average_difficulty': averageDifficulty,
    };
  }

  // Computed properties
  double get averageVolumePerSet =>
      setsCompleted > 0 ? totalVolume / setsCompleted : 0.0;

  double get averageRepsPerSet =>
      setsCompleted > 0 ? totalReps / setsCompleted : 0.0;

  double get averageWeightPerSet => setsCompleted > 0
      ? setDetails.fold<double>(0.0, (sum, set) => sum + set.weight) /
          setsCompleted
      : 0.0;

  String get formattedTimeSpent {
    final minutes = timeSpent.inMinutes;
    final seconds = timeSpent.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }
}

class SetPerformance {
  final int setNumber;
  final double weight;
  final int reps;
  final double volume;
  final Duration restTime;
  final int? difficultyRating;
  final String? notes;

  SetPerformance({
    required this.setNumber,
    required this.weight,
    required this.reps,
    required this.volume,
    required this.restTime,
    this.difficultyRating,
    this.notes,
  });

  factory SetPerformance.fromJson(Map<String, dynamic> json) {
    return SetPerformance(
      setNumber: json['set_number'] ?? 0,
      weight: json['weight']?.toDouble() ?? 0.0,
      reps: json['reps'] ?? 0,
      volume: json['volume']?.toDouble() ?? 0.0,
      restTime: Duration(seconds: json['rest_time_seconds'] ?? 0),
      difficultyRating: json['difficulty_rating'],
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'set_number': setNumber,
      'weight': weight,
      'reps': reps,
      'volume': volume,
      'rest_time_seconds': restTime.inSeconds,
      'difficulty_rating': difficultyRating,
      'notes': notes,
    };
  }
}

class PersonalRecord {
  final String exerciseId;
  final String exerciseName;
  final PersonalRecordType type;
  final double value;
  final DateTime achievedDate;
  final double? previousValue;
  final String? notes;

  PersonalRecord({
    required this.exerciseId,
    required this.exerciseName,
    required this.type,
    required this.value,
    required this.achievedDate,
    this.previousValue,
    this.notes,
  });

  factory PersonalRecord.fromJson(Map<String, dynamic> json) {
    return PersonalRecord(
      exerciseId: json['exercise_id'],
      exerciseName: json['exercise_name'],
      type: PersonalRecordType.fromString(json['type']),
      value: json['value']?.toDouble() ?? 0.0,
      achievedDate: DateTime.parse(json['achieved_date']),
      previousValue: json['previous_value']?.toDouble(),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'exercise_id': exerciseId,
      'exercise_name': exerciseName,
      'type': type.name,
      'value': value,
      'achieved_date': achievedDate.toIso8601String(),
      'previous_value': previousValue,
      'notes': notes,
    };
  }

  // Computed properties
  double get improvementPercentage {
    if (previousValue == null || previousValue == 0) return 0.0;
    return ((value - previousValue!) / previousValue!) * 100;
  }

  String get formattedValue {
    switch (type) {
      case PersonalRecordType.maxWeight:
        return '${value.toStringAsFixed(1)} lbs';
      case PersonalRecordType.maxReps:
        return '${value.toInt()} reps';
      case PersonalRecordType.maxVolume:
        return '${value.toStringAsFixed(1)} lbs';
      case PersonalRecordType.bestTime:
        return '${Duration(seconds: value.toInt()).inMinutes}:${(Duration(seconds: value.toInt()).inSeconds % 60).toString().padLeft(2, '0')}';
    }
  }

  String get description {
    switch (type) {
      case PersonalRecordType.maxWeight:
        return 'New max weight for $exerciseName';
      case PersonalRecordType.maxReps:
        return 'New max reps for $exerciseName';
      case PersonalRecordType.maxVolume:
        return 'New max volume for $exerciseName';
      case PersonalRecordType.bestTime:
        return 'New best time for $exerciseName';
    }
  }
}

enum PersonalRecordType {
  maxWeight,
  maxReps,
  maxVolume,
  bestTime;

  static PersonalRecordType fromString(String? value) {
    switch (value?.toLowerCase()) {
      case 'max_weight':
        return PersonalRecordType.maxWeight;
      case 'max_reps':
        return PersonalRecordType.maxReps;
      case 'max_volume':
        return PersonalRecordType.maxVolume;
      case 'best_time':
        return PersonalRecordType.bestTime;
      default:
        return PersonalRecordType.maxWeight;
    }
  }
}

class SessionComparison {
  final double volumeChange;
  final Duration durationChange;
  final int setsChange;
  final int repsChange;
  final double improvementChange;
  final bool isImprovement;

  SessionComparison({
    required this.volumeChange,
    required this.durationChange,
    required this.setsChange,
    required this.repsChange,
    required this.improvementChange,
    required this.isImprovement,
  });

  // Computed properties
  String get volumeChangeFormatted {
    final sign = volumeChange >= 0 ? '+' : '';
    return '$sign${volumeChange.toStringAsFixed(1)} lbs';
  }

  String get durationChangeFormatted {
    final sign = durationChange.inSeconds >= 0 ? '+' : '-';
    final minutes = durationChange.inMinutes.abs();
    final seconds = (durationChange.inSeconds % 60).abs();
    return '$sign${minutes}m ${seconds}s';
  }

  String get setsChangeFormatted {
    final sign = setsChange >= 0 ? '+' : '';
    return '$sign$setsChange sets';
  }

  String get repsChangeFormatted {
    final sign = repsChange >= 0 ? '+' : '';
    return '$sign$repsChange reps';
  }

  String get improvementChangeFormatted {
    final sign = improvementChange >= 0 ? '+' : '';
    return '$sign${improvementChange.toStringAsFixed(1)}%';
  }
}
