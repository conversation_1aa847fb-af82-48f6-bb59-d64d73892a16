import 'workout.dart';
import 'exercise.dart';

enum WorkoutDifficulty {
  beginner,
  intermediate,
  advanced,
  expert;

  String get label {
    switch (this) {
      case WorkoutDifficulty.beginner:
        return 'Beginner';
      case WorkoutDifficulty.intermediate:
        return 'Intermediate';
      case WorkoutDifficulty.advanced:
        return 'Advanced';
      case WorkoutDifficulty.expert:
        return 'Expert';
    }
  }

  static WorkoutDifficulty fromString(String? value) {
    switch (value?.toLowerCase()) {
      case 'beginner':
        return WorkoutDifficulty.beginner;
      case 'intermediate':
        return WorkoutDifficulty.intermediate;
      case 'advanced':
        return WorkoutDifficulty.advanced;
      case 'expert':
        return WorkoutDifficulty.expert;
      default:
        return WorkoutDifficulty.beginner;
    }
  }
}

class EnhancedWorkout extends Workout {
  final WorkoutDifficulty difficulty;
  final List<String> targetMuscleGroups;
  final int estimatedCalories;
  final int estimatedDuration;
  final List<String> requiredEquipment;
  final double userRating;
  final int completionCount;
  final DateTime? lastCompleted;
  final WorkoutProgress? progress;

  EnhancedWorkout({
    required super.id,
    required super.userId,
    required super.name,
    super.aiDescription,
    super.startTime,
    super.endTime,
    super.isActive,
    super.duration,
    super.notes,
    super.rating,
    super.isMinimized,
    super.lastState,
    super.isCompleted,
    super.sessionOrder,
    required super.createdAt,
    super.updatedAt,
    super.exercises,
    this.difficulty = WorkoutDifficulty.beginner,
    this.targetMuscleGroups = const [],
    this.estimatedCalories = 0,
    this.estimatedDuration = 0,
    this.requiredEquipment = const [],
    this.userRating = 0.0,
    this.completionCount = 0,
    this.lastCompleted,
    this.progress,
  });

  factory EnhancedWorkout.fromWorkout(
    Workout workout, {
    WorkoutDifficulty? difficulty,
    List<String>? targetMuscleGroups,
    int? estimatedCalories,
    int? estimatedDuration,
    List<String>? requiredEquipment,
    double? userRating,
    int? completionCount,
    DateTime? lastCompleted,
    WorkoutProgress? progress,
  }) {
    return EnhancedWorkout(
      id: workout.id,
      userId: workout.userId,
      name: workout.name,
      aiDescription: workout.aiDescription,
      startTime: workout.startTime,
      endTime: workout.endTime,
      isActive: workout.isActive,
      duration: workout.duration,
      notes: workout.notes,
      rating: workout.rating,
      isMinimized: workout.isMinimized,
      lastState: workout.lastState,
      isCompleted: workout.isCompleted,
      sessionOrder: workout.sessionOrder,
      createdAt: workout.createdAt,
      updatedAt: workout.updatedAt,
      exercises: workout.exercises,
      difficulty: difficulty ?? WorkoutDifficulty.beginner,
      targetMuscleGroups: targetMuscleGroups ?? workout.primaryMuscles,
      estimatedCalories:
          estimatedCalories ?? _calculateEstimatedCalories(workout),
      estimatedDuration:
          estimatedDuration ?? _calculateEstimatedDuration(workout),
      requiredEquipment: requiredEquipment ?? workout.equipmentNeeded,
      userRating: userRating ?? 0.0,
      completionCount: completionCount ?? 0,
      lastCompleted: lastCompleted,
      progress: progress,
    );
  }

  factory EnhancedWorkout.fromJson(Map<String, dynamic> json) {
    final workout = Workout.fromJson(json);
    return EnhancedWorkout.fromWorkout(
      workout,
      difficulty: WorkoutDifficulty.fromString(json['difficulty']),
      targetMuscleGroups: json['target_muscle_groups'] != null
          ? List<String>.from(json['target_muscle_groups'])
          : null,
      estimatedCalories: json['estimated_calories'],
      estimatedDuration: json['estimated_duration'],
      requiredEquipment: json['required_equipment'] != null
          ? List<String>.from(json['required_equipment'])
          : null,
      userRating: json['user_rating']?.toDouble() ?? 0.0,
      completionCount: json['completion_count'] ?? 0,
      lastCompleted: json['last_completed'] != null
          ? DateTime.parse(json['last_completed'])
          : null,
      progress: json['progress'] != null
          ? WorkoutProgress.fromJson(json['progress'])
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'difficulty': difficulty.name,
      'target_muscle_groups': targetMuscleGroups,
      'estimated_calories': estimatedCalories,
      'estimated_duration': estimatedDuration,
      'required_equipment': requiredEquipment,
      'user_rating': userRating,
      'completion_count': completionCount,
      'last_completed': lastCompleted?.toIso8601String(),
      'progress': progress?.toJson(),
    });
    return json;
  }

  // Computed properties
  bool get isRecommended {
    // Recommend if user has completed similar workouts successfully
    // or if it matches their fitness level
    if (progress != null && progress!.averageRating >= 3.5) return true;
    if (completionCount > 0 && userRating >= 3.0) return true;
    return false;
  }

  bool get hasProgressData => progress != null && completionCount > 0;

  String get difficultyLabel => difficulty.label;

  List<Exercise> get uniqueExercises {
    final exerciseIds = <String>{};
    return exercises
        .where((workoutExercise) => exerciseIds.add(workoutExercise.exerciseId))
        .map((workoutExercise) => workoutExercise.exercise)
        .toList();
  }

  double get progressPercentage {
    if (progress == null || progress!.totalCompletions == 0) return 0.0;

    // Calculate progress based on completion frequency and improvement
    final consistencyScore =
        progress!.totalCompletions / 10.0; // Max 10 completions for 100%
    final improvementScore =
        progress!.averageRating / 5.0; // Max 5 rating for 100%

    return ((consistencyScore + improvementScore) / 2.0).clamp(0.0, 1.0);
  }

  String get formattedEstimatedDuration {
    if (estimatedDuration <= 0) return 'Unknown';

    final hours = estimatedDuration ~/ 60;
    final minutes = estimatedDuration % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  String get formattedCalories {
    if (estimatedCalories <= 0) return 'Unknown';
    return '$estimatedCalories cal';
  }

  // Helper methods for calculating estimated values
  static int _calculateEstimatedCalories(Workout workout) {
    // Basic calculation: 5 calories per minute per exercise
    final exerciseCount = workout.exercises.length;
    final estimatedMinutes = _calculateEstimatedDuration(workout);
    return (exerciseCount * estimatedMinutes * 5).round();
  }

  static int _calculateEstimatedDuration(Workout workout) {
    // Basic calculation: 2 minutes per set + 1 minute rest
    final totalSets = workout.exercises.fold<int>(
      0,
      (sum, exercise) => sum + exercise.sets,
    );
    return totalSets * 3; // 2 minutes work + 1 minute rest
  }
}

class WorkoutProgress {
  final String workoutId;
  final int totalCompletions;
  final double averageRating;
  final int totalSets;
  final int totalReps;
  final double totalVolume;
  final DateTime firstCompleted;
  final DateTime lastCompleted;
  final List<ExerciseProgress> exerciseProgress;
  final Map<String, dynamic> personalRecords;

  WorkoutProgress({
    required this.workoutId,
    required this.totalCompletions,
    required this.averageRating,
    required this.totalSets,
    required this.totalReps,
    required this.totalVolume,
    required this.firstCompleted,
    required this.lastCompleted,
    this.exerciseProgress = const [],
    this.personalRecords = const {},
  });

  factory WorkoutProgress.fromJson(Map<String, dynamic> json) {
    return WorkoutProgress(
      workoutId: json['workout_id'],
      totalCompletions: json['total_completions'] ?? 0,
      averageRating: json['average_rating']?.toDouble() ?? 0.0,
      totalSets: json['total_sets'] ?? 0,
      totalReps: json['total_reps'] ?? 0,
      totalVolume: json['total_volume']?.toDouble() ?? 0.0,
      firstCompleted: DateTime.parse(json['first_completed']),
      lastCompleted: DateTime.parse(json['last_completed']),
      exerciseProgress: json['exercise_progress'] != null
          ? (json['exercise_progress'] as List)
              .map((e) => ExerciseProgress.fromJson(e))
              .toList()
          : [],
      personalRecords: json['personal_records'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'workout_id': workoutId,
      'total_completions': totalCompletions,
      'average_rating': averageRating,
      'total_sets': totalSets,
      'total_reps': totalReps,
      'total_volume': totalVolume,
      'first_completed': firstCompleted.toIso8601String(),
      'last_completed': lastCompleted.toIso8601String(),
      'exercise_progress': exerciseProgress.map((e) => e.toJson()).toList(),
      'personal_records': personalRecords,
    };
  }

  // Computed properties
  double get averageVolume =>
      totalCompletions > 0 ? totalVolume / totalCompletions : 0.0;

  double get averageSetsPerSession =>
      totalCompletions > 0 ? totalSets / totalCompletions : 0.0;

  double get averageRepsPerSession =>
      totalCompletions > 0 ? totalReps / totalCompletions : 0.0;

  Duration get totalTimeSpent {
    return lastCompleted.difference(firstCompleted);
  }

  bool get hasImprovement {
    return exerciseProgress.any((progress) => progress.hasImprovement);
  }
}

class ExerciseProgress {
  final String exerciseId;
  final String exerciseName;
  final int totalSets;
  final int totalReps;
  final double totalVolume;
  final double maxWeight;
  final int maxReps;
  final DateTime firstPerformed;
  final DateTime lastPerformed;
  final List<PerformanceRecord> records;

  ExerciseProgress({
    required this.exerciseId,
    required this.exerciseName,
    required this.totalSets,
    required this.totalReps,
    required this.totalVolume,
    required this.maxWeight,
    required this.maxReps,
    required this.firstPerformed,
    required this.lastPerformed,
    this.records = const [],
  });

  factory ExerciseProgress.fromJson(Map<String, dynamic> json) {
    return ExerciseProgress(
      exerciseId: json['exercise_id'],
      exerciseName: json['exercise_name'],
      totalSets: json['total_sets'] ?? 0,
      totalReps: json['total_reps'] ?? 0,
      totalVolume: json['total_volume']?.toDouble() ?? 0.0,
      maxWeight: json['max_weight']?.toDouble() ?? 0.0,
      maxReps: json['max_reps'] ?? 0,
      firstPerformed: DateTime.parse(json['first_performed']),
      lastPerformed: DateTime.parse(json['last_performed']),
      records: json['records'] != null
          ? (json['records'] as List)
              .map((e) => PerformanceRecord.fromJson(e))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'exercise_id': exerciseId,
      'exercise_name': exerciseName,
      'total_sets': totalSets,
      'total_reps': totalReps,
      'total_volume': totalVolume,
      'max_weight': maxWeight,
      'max_reps': maxReps,
      'first_performed': firstPerformed.toIso8601String(),
      'last_performed': lastPerformed.toIso8601String(),
      'records': records.map((e) => e.toJson()).toList(),
    };
  }

  // Computed properties
  bool get hasImprovement {
    if (records.length < 2) return false;

    final firstRecord = records.first;
    final lastRecord = records.last;

    return lastRecord.weight > firstRecord.weight ||
        lastRecord.reps > firstRecord.reps ||
        lastRecord.volume > firstRecord.volume;
  }

  double get improvementPercentage {
    if (records.length < 2) return 0.0;

    final firstRecord = records.first;
    final lastRecord = records.last;

    if (firstRecord.volume == 0) return 0.0;

    return ((lastRecord.volume - firstRecord.volume) / firstRecord.volume) *
        100;
  }
}

class PerformanceRecord {
  final DateTime date;
  final double weight;
  final int reps;
  final double volume;
  final int? difficulty;

  PerformanceRecord({
    required this.date,
    required this.weight,
    required this.reps,
    required this.volume,
    this.difficulty,
  });

  factory PerformanceRecord.fromJson(Map<String, dynamic> json) {
    return PerformanceRecord(
      date: DateTime.parse(json['date']),
      weight: json['weight']?.toDouble() ?? 0.0,
      reps: json['reps'] ?? 0,
      volume: json['volume']?.toDouble() ?? 0.0,
      difficulty: json['difficulty'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'weight': weight,
      'reps': reps,
      'volume': volume,
      'difficulty': difficulty,
    };
  }
}
