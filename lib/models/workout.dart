import 'exercise.dart';

class Workout {
  final String id;
  final String userId;
  final String name;
  final String? aiDescription;
  final DateTime? startTime;
  final DateTime? endTime;
  final bool isActive;
  final int? duration;
  final String? notes;
  final int? rating;
  final bool isMinimized;
  final Map<String, dynamic>? lastState;
  final bool? isCompleted;
  final int? sessionOrder;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<WorkoutExercise> exercises;

  Workout({
    required this.id,
    required this.userId,
    required this.name,
    this.aiDescription,
    this.startTime,
    this.endTime,
    this.isActive = false,
    this.duration,
    this.notes,
    this.rating,
    this.isMinimized = false,
    this.lastState,
    this.isCompleted,
    this.sessionOrder,
    required this.createdAt,
    this.updatedAt,
    this.exercises = const [],
  });

  factory Workout.fromJson(Map<String, dynamic> json) {
    return Workout(
      id: json['id'],
      userId: json['user_id'],
      name: json['name'],
      aiDescription: json['ai_description'],
      startTime: json['start_time'] != null
          ? DateTime.parse(json['start_time'])
          : null,
      endTime:
          json['end_time'] != null ? DateTime.parse(json['end_time']) : null,
      isActive: json['is_active'] ?? false,
      duration: json['duration'],
      notes: json['notes'],
      rating: json['rating'],
      isMinimized: json['is_minimized'] ?? false,
      lastState: json['last_state'],
      isCompleted: json['is_completed'],
      sessionOrder: json['session_order'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      exercises: json['exercises'] != null
          ? (json['exercises'] as List)
              .map((e) => WorkoutExercise.fromJson(e))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'ai_description': aiDescription,
      'start_time': startTime?.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'is_active': isActive,
      'duration': duration,
      'notes': notes,
      'rating': rating,
      'is_minimized': isMinimized,
      'last_state': lastState,
      'is_completed': isCompleted,
      'session_order': sessionOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'exercises': exercises.map((e) => e.toJson()).toList(),
    };
  }

  // Helper getters
  int get exerciseCount => exercises.length;

  String get formattedDuration {
    if (duration == null || duration == 0) return 'Not started';

    final hours = duration! ~/ 3600;
    final minutes = (duration! % 3600) ~/ 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m';
    } else {
      return '${duration}s';
    }
  }

  List<String> get primaryMuscles {
    return exercises
        .map((e) => e.exercise.primaryMuscle)
        .where((muscle) => muscle != null)
        .cast<String>()
        .toSet()
        .toList();
  }

  List<String> get equipmentNeeded {
    return exercises
        .map((e) => e.exercise.equipment)
        .where((equipment) => equipment != null && equipment != 'None')
        .cast<String>()
        .toSet()
        .toList();
  }

  String get status {
    if (isActive) return 'Active';
    if (isCompleted == true) return 'Completed';
    return 'Draft';
  }

  String get workoutSummary {
    if (exercises.isEmpty) return 'No exercises';

    final totalSets =
        exercises.fold<int>(0, (sum, exercise) => sum + exercise.sets);
    return '$exerciseCount exercises • $totalSets sets';
  }
}
