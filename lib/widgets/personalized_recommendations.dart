import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../design_system/design_system.dart';
import '../models/exercise.dart';
import '../models/workout.dart';
import '../services/recommendation_service.dart';

/// A widget that displays personalized workout recommendations based on user data
/// Uses workout_set_logs for weight/rep suggestions and provides exercise alternatives
class PersonalizedRecommendations extends StatefulWidget {
  final Exercise exercise;
  final Workout? currentWorkout;
  final bool showWeightSuggestions;
  final bool showRepSuggestions;
  final bool showAlternatives;
  final bool showDifficultyAdjustments;
  final Function(Exercise)? onExerciseSelected;
  final Function(double weight, int reps)? onSuggestionApplied;

  const PersonalizedRecommendations({
    super.key,
    required this.exercise,
    this.currentWorkout,
    this.showWeightSuggestions = true,
    this.showRepSuggestions = true,
    this.showAlternatives = true,
    this.showDifficultyAdjustments = true,
    this.onExerciseSelected,
    this.onSuggestionApplied,
  });

  @override
  State<PersonalizedRecommendations> createState() =>
      _PersonalizedRecommendationsState();
}

class _PersonalizedRecommendationsState
    extends State<PersonalizedRecommendations> {
  bool _isExpanded = false;
  bool _isLoading = false;
  PerformanceSuggestion? _weightSuggestion;
  PerformanceSuggestion? _repSuggestion;
  List<ExerciseAlternative> _alternatives = [];
  List<DifficultyAdjustment> _difficultyAdjustments = [];

  @override
  void initState() {
    super.initState();
    _loadRecommendations();
  }

  @override
  void didUpdateWidget(PersonalizedRecommendations oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.exercise.id != widget.exercise.id) {
      _loadRecommendations();
    }
  }

  Future<void> _loadRecommendations() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final recommendationService = context.read<RecommendationService>();

      // Load weight and rep suggestions from workout_set_logs
      if (widget.showWeightSuggestions) {
        _weightSuggestion =
            await _getWeightSuggestionFromLogs(recommendationService);
      }

      if (widget.showRepSuggestions) {
        _repSuggestion = await _getRepSuggestionFromLogs(recommendationService);
      }

      // Load exercise alternatives from exercises table
      if (widget.showAlternatives) {
        _alternatives = await _getExerciseAlternativesFromDatabase();
      }

      // Load difficulty adjustments based on user profile
      if (widget.showDifficultyAdjustments) {
        _difficultyAdjustments =
            _getDifficultyAdjustments(recommendationService);
      }
    } catch (e) {
      debugPrint('Error loading recommendations: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RecommendationService>(
      builder: (context, recommendationService, child) {
        return AppCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(context, recommendationService),

              const SizedBox(height: AppSpacing.md),

              // Loading indicator
              if (_isLoading)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(AppSpacing.md),
                    child: CircularProgressIndicator(),
                  ),
                ),

              // Weight/Rep suggestions
              if (!_isLoading &&
                  (widget.showWeightSuggestions || widget.showRepSuggestions))
                _buildPerformanceSuggestions(context, recommendationService),

              // Exercise alternatives
              if (!_isLoading && widget.showAlternatives && _isExpanded)
                _buildExerciseAlternatives(context),

              // Difficulty adjustments
              if (!_isLoading &&
                  widget.showDifficultyAdjustments &&
                  _isExpanded)
                _buildDifficultyAdjustments(context, recommendationService),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, RecommendationService service) {
    return Row(
      children: [
        Icon(
          Icons.psychology,
          size: 24,
          color: AppColors.primary,
        ),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Personalized Recommendations',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColorsTheme.textPrimary(context),
                    ),
              ),
              Text(
                'Based on your fitness level and history',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                    ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          icon: Icon(
            _isExpanded ? Icons.expand_less : Icons.expand_more,
            color: AppColorsTheme.textSecondary(context),
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceSuggestions(
      BuildContext context, RecommendationService service) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Performance Suggestions',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColorsTheme.textPrimary(context),
              ),
        ),
        const SizedBox(height: AppSpacing.md),
        Row(
          children: [
            // Weight suggestion
            if (widget.showWeightSuggestions && _weightSuggestion != null)
              Expanded(
                child: _buildSuggestionCard(
                  context,
                  'Suggested Weight',
                  _weightSuggestion!.value,
                  _weightSuggestion!.reason,
                  Icons.fitness_center,
                  AppColors.primary,
                  onApply: _weightSuggestion!.numericValue != null
                      ? () => _applySuggestion(_weightSuggestion!.numericValue!,
                          _repSuggestion?.numericValue?.toInt() ?? 10)
                      : null,
                ),
              ),

            if (widget.showWeightSuggestions &&
                widget.showRepSuggestions &&
                _weightSuggestion != null &&
                _repSuggestion != null)
              const SizedBox(width: AppSpacing.md),

            // Rep suggestion
            if (widget.showRepSuggestions && _repSuggestion != null)
              Expanded(
                child: _buildSuggestionCard(
                  context,
                  'Suggested Reps',
                  _repSuggestion!.value,
                  _repSuggestion!.reason,
                  Icons.repeat,
                  AppColors.success,
                  onApply: _repSuggestion!.numericValue != null
                      ? () => _applySuggestion(
                          _weightSuggestion?.numericValue ?? 0,
                          _repSuggestion!.numericValue!.toInt())
                      : null,
                ),
              ),
          ],
        ),
        const SizedBox(height: AppSpacing.md),
      ],
    );
  }

  Widget _buildSuggestionCard(
    BuildContext context,
    String title,
    String value,
    String reason,
    IconData icon,
    Color color, {
    VoidCallback? onApply,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSpacing.sm),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: color,
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColorsTheme.textPrimary(context),
                ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            reason,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
          ),
          if (onApply != null) ...[
            const SizedBox(height: AppSpacing.sm),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: onApply,
                style: TextButton.styleFrom(
                  backgroundColor: color.withValues(alpha: 0.1),
                  foregroundColor: color,
                  padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
                ),
                child: Text(
                  'Apply',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildExerciseAlternatives(BuildContext context) {
    if (_alternatives.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: AppSpacing.md),
        Text(
          'Alternative Exercises',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColorsTheme.textPrimary(context),
              ),
        ),
        const SizedBox(height: AppSpacing.md),
        ..._alternatives.map(
            (alternative) => _buildAlternativeExercise(context, alternative)),
      ],
    );
  }

  Widget _buildAlternativeExercise(
      BuildContext context, ExerciseAlternative alternative) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: GestureDetector(
        onTap: () => widget.onExerciseSelected?.call(alternative.exercise),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            color: AppColorsTheme.surfaceVariant(context),
            borderRadius: BorderRadius.circular(AppSpacing.sm),
            border: Border.all(
              color: AppColorsTheme.borderLight(context),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Exercise icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: alternative.difficultyColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSpacing.sm),
                  border: Border.all(
                    color: alternative.difficultyColor.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  alternative.icon,
                  color: alternative.difficultyColor,
                  size: 20,
                ),
              ),

              const SizedBox(width: AppSpacing.md),

              // Exercise info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      alternative.exercise.name,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColorsTheme.textPrimary(context),
                          ),
                    ),
                    Text(
                      alternative.reason,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColorsTheme.textSecondary(context),
                          ),
                    ),
                  ],
                ),
              ),

              // Difficulty indicator
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.xs,
                ),
                decoration: BoxDecoration(
                  color: alternative.difficultyColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSpacing.sm),
                ),
                child: Text(
                  alternative.difficultyLabel,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: alternative.difficultyColor,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDifficultyAdjustments(
      BuildContext context, RecommendationService service) {
    final adjustments = _getDifficultyAdjustments(service);

    if (adjustments.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: AppSpacing.md),
        Text(
          'Difficulty Adjustments',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColorsTheme.textPrimary(context),
              ),
        ),
        const SizedBox(height: AppSpacing.md),
        ...adjustments
            .map((adjustment) => _buildAdjustmentTip(context, adjustment)),
      ],
    );
  }

  Widget _buildAdjustmentTip(
      BuildContext context, DifficultyAdjustment adjustment) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: adjustment.color,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Text(
              adjustment.tip,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                    height: 1.4,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  void _applySuggestion(double weight, int reps) {
    widget.onSuggestionApplied?.call(weight, reps);
  }

  Future<PerformanceSuggestion?> _getWeightSuggestionFromLogs(
      RecommendationService service) async {
    try {
      final supabase = Supabase.instance.client;
      final user = supabase.auth.currentUser;
      if (user == null) return _getFallbackWeightSuggestion(service);

      // Get recent workout set logs for this exercise
      final response = await supabase
          .from('workout_set_logs')
          .select('weight, reps, completed_at')
          .eq('exercise_id', widget.exercise.id)
          .eq('user_id', user.id)
          .order('completed_at', ascending: false)
          .limit(10);

      final setLogs = response as List<dynamic>;

      if (setLogs.isEmpty) {
        return _getFallbackWeightSuggestion(service);
      }

      // Calculate progressive overload suggestion
      final recentWeights = setLogs
          .map((log) => (log['weight'] as num?)?.toDouble() ?? 0.0)
          .where((weight) => weight > 0)
          .toList();

      if (recentWeights.isEmpty) {
        return _getFallbackWeightSuggestion(service);
      }

      final maxRecentWeight = recentWeights.reduce((a, b) => a > b ? a : b);

      // Suggest 2.5-5% increase for progressive overload
      final suggestedWeight = maxRecentWeight * 1.025;
      final weightDiff = suggestedWeight - maxRecentWeight;

      String reason;
      if (weightDiff > 0) {
        reason =
            'Progressive overload: +${weightDiff.toStringAsFixed(1)} lbs from your recent best';
      } else {
        reason =
            'Maintain current weight: ${maxRecentWeight.toStringAsFixed(1)} lbs';
      }

      return PerformanceSuggestion(
        value: '${suggestedWeight.toStringAsFixed(1)} lbs',
        reason: reason,
        numericValue: suggestedWeight,
      );
    } catch (e) {
      debugPrint('Error getting weight suggestion from logs: $e');
      return _getFallbackWeightSuggestion(service);
    }
  }

  Future<PerformanceSuggestion?> _getRepSuggestionFromLogs(
      RecommendationService service) async {
    try {
      final supabase = Supabase.instance.client;
      final user = supabase.auth.currentUser;
      if (user == null) return _getFallbackRepSuggestion(service);

      // Get recent workout set logs for this exercise
      final response = await supabase
          .from('workout_set_logs')
          .select('weight, reps, completed_at')
          .eq('exercise_id', widget.exercise.id)
          .eq('user_id', user.id)
          .order('completed_at', ascending: false)
          .limit(10);

      final setLogs = response as List<dynamic>;

      if (setLogs.isEmpty) {
        return _getFallbackRepSuggestion(service);
      }

      // Analyze recent rep performance
      final recentReps = setLogs
          .map((log) => log['reps'] as int? ?? 0)
          .where((reps) => reps > 0)
          .toList();

      if (recentReps.isEmpty) {
        return _getFallbackRepSuggestion(service);
      }

      final avgReps = recentReps.reduce((a, b) => a + b) / recentReps.length;
      final maxReps = recentReps.reduce((a, b) => a > b ? a : b);

      // Suggest rep range based on recent performance and goals
      final userProfile = service.userProfile;
      final goals = userProfile?.fitnessGoals ?? [];

      int suggestedReps;
      String reason;

      if (goals.any((goal) => goal.toLowerCase().contains('strength'))) {
        suggestedReps = (maxReps * 0.8).round().clamp(3, 8);
        reason = 'Strength focus: Lower reps with heavier weight';
      } else if (goals
          .any((goal) => goal.toLowerCase().contains('endurance'))) {
        suggestedReps = (maxReps * 1.2).round().clamp(12, 20);
        reason = 'Endurance focus: Higher reps for muscular endurance';
      } else {
        // General muscle building
        suggestedReps = avgReps.round().clamp(8, 12);
        reason = 'Muscle building: Optimal rep range based on your history';
      }

      return PerformanceSuggestion(
        value: suggestedReps.toString(),
        reason: reason,
        numericValue: suggestedReps.toDouble(),
      );
    } catch (e) {
      debugPrint('Error getting rep suggestion from logs: $e');
      return _getFallbackRepSuggestion(service);
    }
  }

  Future<List<ExerciseAlternative>>
      _getExerciseAlternativesFromDatabase() async {
    try {
      final supabase = Supabase.instance.client;
      final primaryMuscle = widget.exercise.primaryMuscle?.toLowerCase();

      if (primaryMuscle == null) return [];

      // Get alternative exercises from the same muscle group
      final response = await supabase
          .from('exercises')
          .select('*')
          .ilike('primary_muscle', '%$primaryMuscle%')
          .neq('id', widget.exercise.id)
          .limit(5);

      final exercises = response as List<dynamic>;

      final alternatives = <ExerciseAlternative>[];

      for (final exerciseData in exercises) {
        final exercise = Exercise.fromJson(exerciseData);
        final difficulty = _estimateExerciseDifficulty(exercise);

        alternatives.add(ExerciseAlternative(
          exercise: exercise,
          reason: _getAlternativeReason(exercise, difficulty),
          difficultyLabel: difficulty.label,
          difficultyColor: difficulty.color,
          icon: difficulty.icon,
        ));
      }

      return alternatives;
    } catch (e) {
      debugPrint('Error getting exercise alternatives: $e');
      return _getFallbackAlternatives();
    }
  }

  ExerciseDifficulty _estimateExerciseDifficulty(Exercise exercise) {
    final equipment = exercise.equipment?.toLowerCase() ?? 'none';
    final name = exercise.name.toLowerCase();

    // Bodyweight exercises are generally easier to start with
    if (equipment == 'none' || equipment.contains('bodyweight')) {
      if (name.contains('push-up') &&
          !name.contains('diamond') &&
          !name.contains('decline')) {
        return ExerciseDifficulty.easier;
      }
      if (name.contains('assisted') ||
          name.contains('incline') ||
          name.contains('modified')) {
        return ExerciseDifficulty.easier;
      }
      if (name.contains('diamond') ||
          name.contains('decline') ||
          name.contains('pistol')) {
        return ExerciseDifficulty.harder;
      }
      return ExerciseDifficulty.similar;
    }

    // Machine exercises are generally easier than free weights
    if (equipment.contains('machine') || equipment.contains('cable')) {
      return ExerciseDifficulty.easier;
    }

    // Barbell exercises are generally harder than dumbbell
    if (equipment.contains('barbell') &&
        widget.exercise.equipment?.toLowerCase().contains('dumbbell') == true) {
      return ExerciseDifficulty.harder;
    }

    if (equipment.contains('dumbbell') &&
        widget.exercise.equipment?.toLowerCase().contains('barbell') == true) {
      return ExerciseDifficulty.easier;
    }

    return ExerciseDifficulty.similar;
  }

  String _getAlternativeReason(
      Exercise exercise, ExerciseDifficulty difficulty) {
    switch (difficulty) {
      case ExerciseDifficulty.easier:
        return 'Easier variation to build up strength';
      case ExerciseDifficulty.harder:
        return 'Advanced variation for extra challenge';
      case ExerciseDifficulty.similar:
        return 'Similar difficulty with different movement pattern';
    }
  }

  PerformanceSuggestion? _getFallbackWeightSuggestion(
      RecommendationService service) {
    final userProfile = service.userProfile;
    if (userProfile == null) return null;

    final fitnessLevel = userProfile.fitnessLevel?.toLowerCase() ?? 'beginner';

    // Base weight suggestions on exercise type and user level
    String weight;
    String reason;

    switch (widget.exercise.equipment?.toLowerCase()) {
      case 'barbell':
        switch (fitnessLevel) {
          case 'beginner':
            weight = '45-65 lbs';
            reason = 'Start with the bar and light weight';
            break;
          case 'intermediate':
            weight = '95-135 lbs';
            reason = 'Based on your intermediate level';
            break;
          case 'advanced':
            weight = '155-225 lbs';
            reason = 'Challenge yourself with heavier weight';
            break;
          default:
            weight = '45-65 lbs';
            reason = 'Conservative starting weight';
        }
        break;
      case 'dumbbells':
      case 'dumbbell':
        switch (fitnessLevel) {
          case 'beginner':
            weight = '10-20 lbs';
            reason = 'Light weight to master form';
            break;
          case 'intermediate':
            weight = '25-40 lbs';
            reason = 'Moderate weight for your level';
            break;
          case 'advanced':
            weight = '45-65 lbs';
            reason = 'Heavy weight for advanced training';
            break;
          default:
            weight = '10-20 lbs';
            reason = 'Safe starting weight';
        }
        break;
      default:
        return null; // No weight suggestion for bodyweight exercises
    }

    return PerformanceSuggestion(
      value: weight,
      reason: reason,
      numericValue: double.tryParse(weight.split('-').first) ?? 0,
    );
  }

  PerformanceSuggestion? _getFallbackRepSuggestion(
      RecommendationService service) {
    final userProfile = service.userProfile;
    if (userProfile == null) return null;

    final fitnessLevel = userProfile.fitnessLevel?.toLowerCase() ?? 'beginner';
    final goals = userProfile.fitnessGoals ?? [];

    String reps;
    String reason;

    // Adjust reps based on goals and fitness level
    if (goals.any((goal) => goal.toLowerCase().contains('strength'))) {
      switch (fitnessLevel) {
        case 'beginner':
          reps = '8-10';
          reason = 'Build strength with moderate reps';
          break;
        case 'intermediate':
        case 'advanced':
          reps = '5-8';
          reason = 'Low reps for maximum strength';
          break;
        default:
          reps = '8-10';
          reason = 'Good for strength building';
      }
    } else if (goals.any((goal) => goal.toLowerCase().contains('endurance'))) {
      reps = '15-20';
      reason = 'High reps for endurance';
    } else {
      // General fitness
      switch (fitnessLevel) {
        case 'beginner':
          reps = '10-12';
          reason = 'Perfect for building muscle';
          break;
        case 'intermediate':
          reps = '8-12';
          reason = 'Balanced strength and size';
          break;
        case 'advanced':
          reps = '6-10';
          reason = 'Advanced muscle building';
          break;
        default:
          reps = '10-12';
          reason = 'Good all-around rep range';
      }
    }

    return PerformanceSuggestion(
      value: reps,
      reason: reason,
      numericValue: double.tryParse(reps.split('-').first) ?? 10,
    );
  }

  List<ExerciseAlternative> _getFallbackAlternatives() {
    // Generate alternatives based on exercise type and muscle group
    final alternatives = <ExerciseAlternative>[];
    final primaryMuscle = widget.exercise.primaryMuscle?.toLowerCase();

    if (primaryMuscle == null) return alternatives;

    // Mock alternatives based on muscle group
    switch (primaryMuscle) {
      case 'chest':
        alternatives.addAll([
          ExerciseAlternative(
            exercise: Exercise(
              id: 'alt1',
              name: 'Incline Push-ups',
              primaryMuscle: 'Chest',
              equipment: 'None',
            ),
            reason: 'Easier variation for beginners',
            difficultyLabel: 'Easier',
            difficultyColor: AppColors.success,
            icon: Icons.trending_down,
          ),
          ExerciseAlternative(
            exercise: Exercise(
              id: 'alt2',
              name: 'Diamond Push-ups',
              primaryMuscle: 'Chest',
              equipment: 'None',
            ),
            reason: 'Harder variation for challenge',
            difficultyLabel: 'Harder',
            difficultyColor: AppColors.error,
            icon: Icons.trending_up,
          ),
        ]);
        break;
      case 'back':
        alternatives.addAll([
          ExerciseAlternative(
            exercise: Exercise(
              id: 'alt3',
              name: 'Assisted Pull-ups',
              primaryMuscle: 'Back',
              equipment: 'Resistance Bands',
            ),
            reason: 'Easier with band assistance',
            difficultyLabel: 'Easier',
            difficultyColor: AppColors.success,
            icon: Icons.trending_down,
          ),
        ]);
        break;
      default:
        // Add generic alternatives
        alternatives.add(
          ExerciseAlternative(
            exercise: Exercise(
              id: 'alt_generic',
              name: 'Modified ${widget.exercise.name}',
              primaryMuscle: widget.exercise.primaryMuscle,
              equipment: 'None',
            ),
            reason: 'Bodyweight variation',
            difficultyLabel: 'Modified',
            difficultyColor: AppColors.warning,
            icon: Icons.swap_horiz,
          ),
        );
    }

    return alternatives;
  }

  List<DifficultyAdjustment> _getDifficultyAdjustments(
      RecommendationService service) {
    final adjustments = <DifficultyAdjustment>[];
    final userProfile = service.userProfile;

    if (userProfile == null) return adjustments;

    final fitnessLevel = userProfile.fitnessLevel?.toLowerCase() ?? 'beginner';

    switch (fitnessLevel) {
      case 'beginner':
        adjustments.addAll([
          DifficultyAdjustment(
            tip: 'Focus on proper form over speed or weight',
            color: AppColors.primary,
          ),
          DifficultyAdjustment(
            tip: 'Take longer rest periods (60-90 seconds) between sets',
            color: AppColors.primary,
          ),
          DifficultyAdjustment(
            tip: 'Start with fewer sets and gradually increase',
            color: AppColors.primary,
          ),
        ]);
        break;
      case 'intermediate':
        adjustments.addAll([
          DifficultyAdjustment(
            tip:
                'Try increasing weight by 5-10% when you can complete all reps easily',
            color: AppColors.warning,
          ),
          DifficultyAdjustment(
            tip: 'Consider adding drop sets or supersets for intensity',
            color: AppColors.warning,
          ),
        ]);
        break;
      case 'advanced':
        adjustments.addAll([
          DifficultyAdjustment(
            tip: 'Experiment with tempo variations (slow negatives)',
            color: AppColors.error,
          ),
          DifficultyAdjustment(
            tip: 'Try advanced techniques like rest-pause or cluster sets',
            color: AppColors.error,
          ),
        ]);
        break;
    }

    return adjustments;
  }
}

class PerformanceSuggestion {
  final String value;
  final String reason;
  final double? numericValue;

  const PerformanceSuggestion({
    required this.value,
    required this.reason,
    this.numericValue,
  });
}

class ExerciseAlternative {
  final Exercise exercise;
  final String reason;
  final String difficultyLabel;
  final Color difficultyColor;
  final IconData icon;

  const ExerciseAlternative({
    required this.exercise,
    required this.reason,
    required this.difficultyLabel,
    required this.difficultyColor,
    required this.icon,
  });
}

class DifficultyAdjustment {
  final String tip;
  final Color color;

  const DifficultyAdjustment({
    required this.tip,
    required this.color,
  });
}

enum ExerciseDifficulty {
  easier,
  similar,
  harder;

  String get label {
    switch (this) {
      case ExerciseDifficulty.easier:
        return 'Easier';
      case ExerciseDifficulty.similar:
        return 'Similar';
      case ExerciseDifficulty.harder:
        return 'Harder';
    }
  }

  Color get color {
    switch (this) {
      case ExerciseDifficulty.easier:
        return AppColors.success;
      case ExerciseDifficulty.similar:
        return AppColors.warning;
      case ExerciseDifficulty.harder:
        return AppColors.error;
    }
  }

  IconData get icon {
    switch (this) {
      case ExerciseDifficulty.easier:
        return Icons.trending_down;
      case ExerciseDifficulty.similar:
        return Icons.swap_horiz;
      case ExerciseDifficulty.harder:
        return Icons.trending_up;
    }
  }
}
