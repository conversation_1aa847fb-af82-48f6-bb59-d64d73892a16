import 'package:flutter/material.dart';
import '../models/session_analytics.dart';
import '../design_system/design_system.dart';

class SessionSummaryCard extends StatelessWidget {
  final SessionAnalytics sessionAnalytics;
  final SessionAnalytics? previousSession;
  final VoidCallback? onViewDetails;

  const SessionSummaryCard({
    super.key,
    required this.sessionAnalytics,
    this.previousSession,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    final comparison = sessionAnalytics.compareWith(previousSession);

    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: AppSpacing.lg),
            _buildMainStats(context),
            const SizedBox(height: AppSpacing.lg),
            _buildPerformanceComparison(context, comparison),
            if (sessionAnalytics.hasNewRecords) ...[
              const SizedBox(height: AppSpacing.lg),
              _buildPersonalRecords(context),
            ],
            if (onViewDetails != null) ...[
              const SizedBox(height: AppSpacing.lg),
              _buildViewDetailsButton(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          child: Icon(
            Icons.emoji_events,
            color: AppColors.primary,
            size: 24,
          ),
        ),
        const SizedBox(width: AppSpacing.lg),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Workout Complete!',
                style: AppTypography.heading2.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Great job on your session',
                style: AppTypography.body2.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.sm,
            vertical: AppSpacing.xs,
          ),
          decoration: BoxDecoration(
            color: _getPerformanceColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          child: Text(
            sessionAnalytics.performanceGrade,
            style: AppTypography.buttonMedium.copyWith(
              color: _getPerformanceColor(),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMainStats(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            context,
            icon: Icons.access_time,
            label: 'Duration',
            value: sessionAnalytics.formattedDuration,
            color: AppColors.secondary,
          ),
        ),
        const SizedBox(width: AppSpacing.lg),
        Expanded(
          child: _buildStatItem(
            context,
            icon: Icons.fitness_center,
            label: 'Sets',
            value: '${sessionAnalytics.setsCompleted}',
            color: AppColors.primary,
          ),
        ),
        const SizedBox(width: AppSpacing.lg),
        Expanded(
          child: _buildStatItem(
            context,
            icon: Icons.local_fire_department,
            label: 'Calories',
            value: '${sessionAnalytics.estimatedCalories}',
            color: AppColors.warning,
          ),
        ),
        const SizedBox(width: AppSpacing.lg),
        Expanded(
          child: _buildStatItem(
            context,
            icon: Icons.trending_up,
            label: 'Volume',
            value: '${sessionAnalytics.totalVolume.toStringAsFixed(0)} lbs',
            color: AppColors.success,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            value,
            style: AppTypography.heading3.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: AppTypography.caption.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceComparison(
    BuildContext context,
    SessionComparison comparison,
  ) {
    if (previousSession == null) {
      return Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: AppColors.secondary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          border: Border.all(
            color: AppColors.secondary.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.star,
              color: AppColors.secondary,
              size: 20,
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Text(
                'First time doing this workout! Great start!',
                style: AppTypography.body1.copyWith(
                  color: AppColors.secondary,
                ),
              ),
            ),
          ],
        ),
      );
    }

    final improvements = <String>[];
    final declines = <String>[];

    if (comparison.volumeChange > 0) {
      improvements.add('${comparison.volumeChangeFormatted} volume');
    } else if (comparison.volumeChange < 0) {
      declines.add('${comparison.volumeChangeFormatted} volume');
    }

    if (comparison.setsChange > 0) {
      improvements.add(comparison.setsChangeFormatted);
    } else if (comparison.setsChange < 0) {
      declines.add(comparison.setsChangeFormatted);
    }

    if (comparison.repsChange > 0) {
      improvements.add(comparison.repsChangeFormatted);
    } else if (comparison.repsChange < 0) {
      declines.add(comparison.repsChangeFormatted);
    }

    if (improvements.isEmpty && declines.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: AppColors.textSecondary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        child: Row(
          children: [
            Icon(
              Icons.horizontal_rule,
              color: AppColors.textSecondary,
              size: 20,
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Text(
                'Similar performance to last time',
                style: AppTypography.body1.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (improvements.isNotEmpty) {
      return Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: AppColors.success.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          border: Border.all(
            color: AppColors.success.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.trending_up,
              color: AppColors.success,
              size: 20,
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Text(
                'Improved: ${improvements.join(', ')}',
                style: AppTypography.body1.copyWith(
                  color: AppColors.success,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.trending_down,
            color: AppColors.warning,
            size: 20,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              'Keep pushing! ${declines.join(', ')}',
              style: AppTypography.body1.copyWith(
                color: AppColors.warning,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalRecords(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.emoji_events,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(
                'New Personal Records!',
                style: AppTypography.buttonLarge.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.sm),
          ...sessionAnalytics.newRecords.take(3).map((record) => Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.xs),
                child: Text(
                  '• ${record.description}: ${record.formattedValue}',
                  style: AppTypography.caption.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              )),
          if (sessionAnalytics.newRecords.length > 3)
            Text(
              '+ ${sessionAnalytics.newRecords.length - 3} more records',
              style: AppTypography.caption.copyWith(
                color: AppColors.primary,
                fontStyle: FontStyle.italic,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildViewDetailsButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        onPressed: onViewDetails,
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: AppColors.primary),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          padding: const EdgeInsets.symmetric(vertical: AppSpacing.lg),
        ),
        child: Text(
          'View Detailed Analysis',
          style: AppTypography.buttonLarge.copyWith(
            color: AppColors.primary,
          ),
        ),
      ),
    );
  }

  Color _getPerformanceColor() {
    if (sessionAnalytics.improvementScore >= 85) {
      return AppColors.success;
    } else if (sessionAnalytics.improvementScore >= 70) {
      return AppColors.primary;
    } else if (sessionAnalytics.improvementScore >= 60) {
      return AppColors.warning;
    } else {
      return AppColors.error;
    }
  }
}
