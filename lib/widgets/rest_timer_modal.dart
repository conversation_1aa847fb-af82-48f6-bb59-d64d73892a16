import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/design_system.dart';

class RestTimerModal extends StatefulWidget {
  final String currentExerciseName;
  final int currentSet;
  final int totalSets;
  final String? nextExerciseName;
  final int restDurationSeconds;
  final VoidCallback onComplete;
  final VoidCallback? onSkip;

  const RestTimerModal({
    super.key,
    required this.currentExerciseName,
    required this.currentSet,
    required this.totalSets,
    this.nextExerciseName,
    this.restDurationSeconds = 90, // Default 90 seconds
    required this.onComplete,
    this.onSkip,
  });

  @override
  State<RestTimerModal> createState() => _RestTimerModalState();
}

class _RestTimerModalState extends State<RestTimerModal>
    with SingleTickerProviderStateMixin {
  late Timer _timer;
  late int _remainingSeconds;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isCompleted = false;
  bool _isPaused = false;

  @override
  void initState() {
    super.initState();
    _remainingSeconds = widget.restDurationSeconds;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _startTimer();
    _animationController.forward();
  }

  @override
  void dispose() {
    _timer.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isPaused) {
        setState(() {
          if (_remainingSeconds > 0) {
            _remainingSeconds--;
          } else {
            _completeRest();
          }
        });
      }
    });
  }

  void _togglePause() {
    setState(() {
      _isPaused = !_isPaused;
    });
    HapticFeedback.selectionClick();
  }

  void _completeRest() async {
    if (_isCompleted) return;
    _isCompleted = true;

    _timer.cancel();
    HapticFeedback.mediumImpact();

    // Animate out
    await _animationController.reverse();
    widget.onComplete();
  }

  void _skipRest() async {
    if (_isCompleted) return;
    _isCompleted = true;

    _timer.cancel();
    HapticFeedback.lightImpact();

    // Animate out
    await _animationController.reverse();

    if (widget.onSkip != null) {
      widget.onSkip!();
    } else {
      widget.onComplete();
    }
  }

  void _addTime(int seconds) {
    setState(() {
      _remainingSeconds =
          (_remainingSeconds + seconds).clamp(0, 300); // Max 5 minutes
    });
    HapticFeedback.selectionClick();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Material(
          color: AppColorsTheme.background(context),
          child: SafeArea(
            child: SlideTransition(
              position: _slideAnimation,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Stack(
                  children: [
                    Column(
                      children: [
                        _buildHeader(),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 24),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 20),
                                  _buildTimerDisplay(),
                                  const SizedBox(height: 40),
                                  _buildTimeControls(),
                                  const SizedBox(height: 32),
                                  _buildActionButtons(),
                                  const SizedBox(height: 32),
                                  _buildNextExerciseCard(),
                                  const SizedBox(
                                      height: 120), // Space for bottom nav
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    // Bottom navigation positioned
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: AppBottomNavigation(
                        currentItem: AppNavigationItem.workouts,
                        onItemTapped: (item) {
                          // During rest timer, we should ask for confirmation before leaving
                          if (item != AppNavigationItem.workouts) {
                            _showLeaveConfirmation(item);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          AppWorkoutHeader(
            trailing: Icon(
              Icons.help_outline,
              color: AppColorsTheme.textSecondary(context),
              size: 24,
            ),
          ),
          const SizedBox(height: 32),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '38:30',
                    style: AppTypography.display2.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                    ),
                  ),
                  Text(
                    'Elapsed Time',
                    style: AppTypography.caption.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '62%',
                    style: AppTypography.display2.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                    ),
                  ),
                  Text(
                    'Complete',
                    style: AppTypography.caption.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Progress bar
          LinearProgressIndicator(
            value: 0.62,
            backgroundColor: AppColorsTheme.border(context),
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            minHeight: 4,
          ),
          const SizedBox(height: 24),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Now:',
                style: AppTypography.body2.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
              ),
              Text(
                widget.currentExerciseName,
                style: AppTypography.heading3.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '12',
                        style: AppTypography.heading2.copyWith(
                          color: AppColorsTheme.textPrimary(context),
                          fontSize: 20,
                        ),
                      ),
                      Text(
                        'Reps',
                        style: AppTypography.caption.copyWith(
                          color: AppColorsTheme.textSecondary(context),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 40),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '25',
                        style: AppTypography.heading2.copyWith(
                          color: AppColorsTheme.textPrimary(context),
                          fontSize: 20,
                        ),
                      ),
                      Text(
                        'lbs',
                        style: AppTypography.caption.copyWith(
                          color: AppColorsTheme.textSecondary(context),
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Text(
                    'Set ${widget.currentSet} / ${widget.totalSets}',
                    style: AppTypography.body2.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimerDisplay() {
    final minutes = _remainingSeconds ~/ 60;
    final seconds = _remainingSeconds % 60;
    final progress = 1.0 - (_remainingSeconds / widget.restDurationSeconds);

    return SizedBox(
      width: 180,
      height: 180,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Custom segmented progress indicator
          CustomPaint(
            size: const Size(180, 180),
            painter: SegmentedCircularProgressPainter(
              progress: progress,
              isPaused: _isPaused,
            ),
          ),
          // Timer text
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}',
                style: AppTypography.display1.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                  fontSize: 42,
                ),
              ),
              Text(
                'remaining',
                style: AppTypography.body2.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNextExerciseCard() {
    if (widget.nextExerciseName == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColorsTheme.surfaceVariant(context),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColorsTheme.border(context),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColorsTheme.border(context),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.fitness_center,
              color: AppColorsTheme.textSecondary(context),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Next:',
                  style: AppTypography.caption.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
                Text(
                  widget.nextExerciseName!,
                  style: AppTypography.heading3.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: AppColors.primary,
            size: 16,
          ),
        ],
      ),
    );
  }

  Widget _buildTimeControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildTimeButton('-15s', -15),
        const SizedBox(width: 60),
        _buildTimeButton('+15s', 15),
      ],
    );
  }

  Widget _buildTimeButton(String label, int seconds) {
    return GestureDetector(
      onTap: () => _addTime(seconds),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: AppColorsTheme.surfaceVariant(context),
          shape: BoxShape.circle,
          border: Border.all(
            color: AppColorsTheme.border(context),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            label,
            style: AppTypography.buttonMedium.copyWith(
              color: AppColorsTheme.textPrimary(context),
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: AppButton(
              onPressed: _skipRest,
              text: 'Skip Rest',
              icon: Icons.play_arrow,
              fullWidth: true,
            ),
          ),
          const SizedBox(height: 12),
          TextButton(
            onPressed: _togglePause,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _isPaused ? Icons.play_arrow : Icons.pause,
                  color: AppColorsTheme.textSecondary(context),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _isPaused ? 'Resume Timer' : 'Pause Timer',
                  style: AppTypography.buttonMedium.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showLeaveConfirmation(AppNavigationItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: AppBorderRadius.cardRadius,
        ),
        title: const Text('Leave Rest Timer?'),
        content: const Text(
          'Your workout progress will be saved, but the rest timer will be cancelled.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              _timer.cancel(); // Stop the timer
              navigateToItem(context, item);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primary,
            ),
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }
}

// Custom painter for segmented circular progress
class SegmentedCircularProgressPainter extends CustomPainter {
  final double progress;
  final bool isPaused;

  SegmentedCircularProgressPainter({
    required this.progress,
    required this.isPaused,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 10;
    final strokeWidth = 8.0;

    // Number of segments
    const int segments = 8;
    const double segmentAngle = 2 * 3.14159 / segments;
    const double gapAngle = 0.1; // Small gap between segments

    final paint = Paint()
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Draw background segments
    paint.color = Colors.grey[200]!;
    for (int i = 0; i < segments; i++) {
      final startAngle = i * segmentAngle - 3.14159 / 2; // Start from top
      final sweepAngle = segmentAngle - gapAngle;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        false,
        paint,
      );
    }

    // Draw progress segments
    paint.color = const Color(0xFF6366F1);
    final progressSegments = (progress * segments).floor();
    final partialProgress = (progress * segments) - progressSegments;

    for (int i = 0; i < progressSegments; i++) {
      final startAngle = i * segmentAngle - 3.14159 / 2;
      final sweepAngle = segmentAngle - gapAngle;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        false,
        paint,
      );
    }

    // Draw partial segment if needed
    if (partialProgress > 0 && progressSegments < segments) {
      final startAngle = progressSegments * segmentAngle - 3.14159 / 2;
      final sweepAngle = (segmentAngle - gapAngle) * partialProgress;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        false,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(SegmentedCircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.isPaused != isPaused;
  }
}
