import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import '../models/session_analytics.dart';

class PersonalRecordDisplay extends StatelessWidget {
  final List<PersonalRecord> personalRecords;
  final bool showAnimation;

  const PersonalRecordDisplay({
    super.key,
    required this.personalRecords,
    this.showAnimation = true,
  });

  @override
  Widget build(BuildContext context) {
    if (personalRecords.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: AppSpacing.md),
            ...personalRecords
                .map((record) => _buildRecordItem(context, record)),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.success.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          child: Icon(
            Icons.emoji_events,
            color: AppColors.success,
            size: 20,
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Personal Records',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                '${personalRecords.length} new record${personalRecords.length == 1 ? '' : 's'}!',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
        ),
        if (showAnimation)
          TweenAnimationBuilder<double>(
            tween: Tween(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 800),
            builder: (context, value, child) {
              return Transform.scale(
                scale: 0.8 + (0.2 * value),
                child: Opacity(
                  opacity: value,
                  child: Icon(
                    Icons.celebration,
                    color: AppColors.warning,
                    size: 24,
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  Widget _buildRecordItem(BuildContext context, PersonalRecord record) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppBorderRadius.xs),
            ),
            child: Icon(
              _getRecordIcon(record.type),
              color: AppColors.success,
              size: 16,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  record.exerciseName,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Text(
                  _getRecordDescription(record),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                record.formattedValue,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              if (record.previousValue != null &&
                  record.improvementPercentage > 0)
                Text(
                  '+${record.improvementPercentage.toStringAsFixed(1)}%',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: AppColors.success,
                      ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getRecordIcon(PersonalRecordType type) {
    switch (type) {
      case PersonalRecordType.maxWeight:
        return Icons.fitness_center;
      case PersonalRecordType.maxReps:
        return Icons.repeat;
      case PersonalRecordType.maxVolume:
        return Icons.trending_up;
      case PersonalRecordType.bestTime:
        return Icons.timer;
    }
  }

  String _getRecordDescription(PersonalRecord record) {
    switch (record.type) {
      case PersonalRecordType.maxWeight:
        return 'New max weight';
      case PersonalRecordType.maxReps:
        return 'New max reps';
      case PersonalRecordType.maxVolume:
        return 'New max volume';
      case PersonalRecordType.bestTime:
        return 'New best time';
    }
  }
}
