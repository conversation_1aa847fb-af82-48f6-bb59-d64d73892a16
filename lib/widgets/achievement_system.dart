import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import '../models/session_analytics.dart';
import 'streak_tracker.dart';
import 'personal_record_display.dart';

class AchievementSystem extends StatelessWidget {
  final int currentStreak;
  final int longestStreak;
  final double consistencyScore;
  final List<DateTime> recentWorkoutDates;
  final List<PersonalRecord> recentPersonalRecords;
  final Map<String, dynamic> consistencyMetrics;

  const AchievementSystem({
    super.key,
    required this.currentStreak,
    required this.longestStreak,
    required this.consistencyScore,
    this.recentWorkoutDates = const [],
    this.recentPersonalRecords = const [],
    this.consistencyMetrics = const {},
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildAchievementHeader(context),
        const SizedBox(height: AppSpacing.md),

        // Streak tracking
        StreakTracker(
          currentStreak: currentStreak,
          longestStreak: longestStreak,
          consistencyScore: consistencyScore,
          recentWorkoutDates: recentWorkoutDates,
        ),

        const SizedBox(height: AppSpacing.md),

        // Recent personal records
        if (recentPersonalRecords.isNotEmpty) ...[
          PersonalRecordDisplay(
            personalRecords: recentPersonalRecords,
            showAnimation: false,
          ),
          const SizedBox(height: AppSpacing.md),
        ],

        // Achievement badges
        _buildAchievementBadges(context),
      ],
    );
  }

  Widget _buildAchievementHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.warning.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          child: Icon(
            Icons.emoji_events,
            color: AppColors.warning,
            size: 20,
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Achievements',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                'Track your progress and milestones',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAchievementBadges(BuildContext context) {
    final achievements = _generateAchievements();

    if (achievements.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            children: [
              Icon(
                Icons.emoji_events_outlined,
                size: 48,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: AppSpacing.sm),
              Text(
                'No achievements yet',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
              Text(
                'Keep working out to unlock achievements!',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Achievements',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: AppSpacing.md),
            Wrap(
              spacing: AppSpacing.sm,
              runSpacing: AppSpacing.sm,
              children: achievements
                  .map((achievement) =>
                      _buildAchievementBadge(context, achievement))
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementBadge(BuildContext context, Achievement achievement) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: achievement.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        border: Border.all(
          color: achievement.color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            achievement.icon,
            color: achievement.color,
            size: 16,
          ),
          const SizedBox(width: 4),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                achievement.title,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: achievement.color,
                    ),
              ),
              if (achievement.description.isNotEmpty)
                Text(
                  achievement.description,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: AppColors.textSecondary,
                        fontSize: 10,
                      ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  List<Achievement> _generateAchievements() {
    final achievements = <Achievement>[];

    // Streak achievements
    if (currentStreak >= 3) {
      achievements.add(Achievement(
        title: 'Getting Started',
        description: '3 day streak',
        icon: Icons.trending_up,
        color: AppColors.primary,
        value: currentStreak,
      ));
    }

    if (currentStreak >= 7) {
      achievements.add(Achievement(
        title: 'Week Warrior',
        description: '7 day streak',
        icon: Icons.local_fire_department,
        color: AppColors.warning,
        value: currentStreak,
      ));
    }

    if (currentStreak >= 30) {
      achievements.add(Achievement(
        title: 'Monthly Master',
        description: '30 day streak',
        icon: Icons.emoji_events,
        color: AppColors.success,
        value: currentStreak,
      ));
    }

    // Consistency achievements
    if (consistencyScore >= 70) {
      achievements.add(Achievement(
        title: 'Consistent',
        description: '70% consistency',
        icon: Icons.check_circle,
        color: AppColors.success,
        value: consistencyScore.toInt(),
      ));
    }

    if (consistencyScore >= 90) {
      achievements.add(Achievement(
        title: 'Dedication',
        description: '90% consistency',
        icon: Icons.star,
        color: AppColors.warning,
        value: consistencyScore.toInt(),
      ));
    }

    // Personal record achievements
    if (recentPersonalRecords.isNotEmpty) {
      achievements.add(Achievement(
        title: 'Record Breaker',
        description:
            '${recentPersonalRecords.length} new PR${recentPersonalRecords.length == 1 ? '' : 's'}',
        icon: Icons.celebration,
        color: AppColors.success,
        value: recentPersonalRecords.length,
      ));
    }

    // Workout count achievements
    final totalWorkouts = consistencyMetrics['totalWorkouts'] as int? ?? 0;
    if (totalWorkouts >= 10) {
      achievements.add(Achievement(
        title: 'Getting Fit',
        description: '10 workouts completed',
        icon: Icons.fitness_center,
        color: AppColors.primary,
        value: totalWorkouts,
      ));
    }

    if (totalWorkouts >= 50) {
      achievements.add(Achievement(
        title: 'Fitness Enthusiast',
        description: '50 workouts completed',
        icon: Icons.sports_gymnastics,
        color: AppColors.warning,
        value: totalWorkouts,
      ));
    }

    if (totalWorkouts >= 100) {
      achievements.add(Achievement(
        title: 'Fitness Fanatic',
        description: '100 workouts completed',
        icon: Icons.emoji_events,
        color: AppColors.success,
        value: totalWorkouts,
      ));
    }

    return achievements;
  }
}

class Achievement {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final int value;

  Achievement({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.value,
  });
}
