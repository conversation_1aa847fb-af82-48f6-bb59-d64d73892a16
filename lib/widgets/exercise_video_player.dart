import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// A widget that displays exercise videos with integrated playback controls
class ExerciseVideoPlayer extends StatefulWidget {
  final String? videoUrl;
  final String? verticalVideoUrl;
  final String exerciseName;
  final bool autoPlay;
  final bool showControls;

  const ExerciseVideoPlayer({
    super.key,
    this.videoUrl,
    this.verticalVideoUrl,
    required this.exerciseName,
    this.autoPlay = false,
    this.showControls = true,
  });

  @override
  State<ExerciseVideoPlayer> createState() => _ExerciseVideoPlayerState();
}

class _ExerciseVideoPlayerState extends State<ExerciseVideoPlayer> {
  bool _isPlaying = false;
  final bool _showControls = true;
  bool _useVerticalVideo = false;

  @override
  void initState() {
    super.initState();
    _isPlaying = widget.autoPlay;
  }

  bool get _hasVideo =>
      widget.videoUrl != null || widget.verticalVideoUrl != null;
  bool get _hasMultipleFormats =>
      widget.videoUrl != null && widget.verticalVideoUrl != null;

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
    });
  }

  void _toggleVideoFormat() {
    if (_hasMultipleFormats) {
      setState(() {
        _useVerticalVideo = !_useVerticalVideo;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasVideo) {
      return _buildPlaceholder(context);
    }

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        borderRadius: BorderRadius.circular(AppSpacing.md),
        border: Border.all(
          color: AppColorsTheme.border(context),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppSpacing.md),
        child: Stack(
          children: [
            // Video placeholder with thumbnail
            _buildVideoContent(context),

            // Controls overlay
            if (_showControls && widget.showControls)
              _buildControlsOverlay(context),

            // Format toggle button
            if (_hasMultipleFormats) _buildFormatToggle(context),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoContent(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Video thumbnail/placeholder
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.3),
                  Colors.black.withValues(alpha: 0.7),
                ],
              ),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _isPlaying
                        ? Icons.pause_circle_filled
                        : Icons.play_circle_filled,
                    size: 64,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                  const SizedBox(height: AppSpacing.sm),
                  Text(
                    widget.exerciseName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),

          // Tap detector for play/pause
          Positioned.fill(
            child: GestureDetector(
              onTap: _togglePlayPause,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlsOverlay(BuildContext context) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.7),
            ],
          ),
        ),
        child: Row(
          children: [
            // Play/Pause button
            IconButton(
              onPressed: _togglePlayPause,
              icon: Icon(
                _isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
              ),
            ),

            // Progress bar placeholder
            Expanded(
              child: Container(
                height: 4,
                margin: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _isPlaying ? 0.3 : 0.0, // Simulated progress
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              ),
            ),

            // Fullscreen button
            IconButton(
              onPressed: () {
                // TODO: Implement fullscreen functionality
              },
              icon: const Icon(
                Icons.fullscreen,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormatToggle(BuildContext context) {
    return Positioned(
      top: AppSpacing.md,
      right: AppSpacing.md,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(AppSpacing.xs),
        ),
        child: GestureDetector(
          onTap: _toggleVideoFormat,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _useVerticalVideo
                    ? Icons.stay_current_portrait
                    : Icons.stay_current_landscape,
                size: 16,
                color: Colors.white,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                _useVerticalVideo ? 'Vertical' : 'Landscape',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontSize: 12,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        borderRadius: BorderRadius.circular(AppSpacing.md),
        border: Border.all(
          color: AppColorsTheme.border(context),
          width: 1,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.video_library_outlined,
              size: 48,
              color: AppColorsTheme.textSecondary(context),
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'No video available',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              'for ${widget.exerciseName}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColorsTheme.textTertiary(context),
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
