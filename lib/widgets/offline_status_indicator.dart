import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../services/offline_service.dart';
import '../services/sync_service.dart';
import '../models/offline_workout_session.dart';
import '../design_system/design_system.dart';

class OfflineStatusIndicator extends StatefulWidget {
  final bool showSyncStatus;
  final bool compact;

  const OfflineStatusIndicator({
    super.key,
    this.showSyncStatus = true,
    this.compact = false,
  });

  @override
  State<OfflineStatusIndicator> createState() => _OfflineStatusIndicatorState();
}

class _OfflineStatusIndicatorState extends State<OfflineStatusIndicator> {
  bool _isOnline = true;
  late Stream<List<ConnectivityResult>> _connectivityStream;

  @override
  void initState() {
    super.initState();
    _connectivityStream = Connectivity().onConnectivityChanged;
    _checkInitialConnectivity();
  }

  Future<void> _checkInitialConnectivity() async {
    final connectivityResults = await Connectivity().checkConnectivity();
    setState(() {
      _isOnline = connectivityResults.isNotEmpty &&
          connectivityResults
              .any((result) => result != ConnectivityResult.none);
    });
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<ConnectivityResult>>(
      stream: _connectivityStream,
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          final isOnline = snapshot.data!.isNotEmpty &&
              snapshot.data!.any((result) => result != ConnectivityResult.none);

          if (isOnline != _isOnline) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _isOnline = isOnline;
              });
            });
          }
        }

        return Consumer2<OfflineService, SyncService>(
          builder: (context, offlineService, syncService, child) {
            return FutureBuilder<OfflineSessionSummary>(
              future: offlineService.getSessionSummary(),
              builder: (context, summarySnapshot) {
                final summary = summarySnapshot.data;
                final hasPendingData = summary != null &&
                    (summary.pendingSyncSessions > 0 ||
                        summary.failedSyncSessions > 0);

                if (!_isOnline || hasPendingData || syncService.isSyncing) {
                  return _buildStatusIndicator(
                    isOnline: _isOnline,
                    isSyncing: syncService.isSyncing,
                    pendingSessions: summary?.pendingSyncSessions ?? 0,
                    failedSessions: summary?.failedSyncSessions ?? 0,
                  );
                }

                return const SizedBox.shrink();
              },
            );
          },
        );
      },
    );
  }

  Widget _buildStatusIndicator({
    required bool isOnline,
    required bool isSyncing,
    required int pendingSessions,
    required int failedSessions,
  }) {
    if (widget.compact) {
      return _buildCompactIndicator(
        isOnline: isOnline,
        isSyncing: isSyncing,
        pendingSessions: pendingSessions,
        failedSessions: failedSessions,
      );
    }

    return _buildFullIndicator(
      isOnline: isOnline,
      isSyncing: isSyncing,
      pendingSessions: pendingSessions,
      failedSessions: failedSessions,
    );
  }

  Widget _buildCompactIndicator({
    required bool isOnline,
    required bool isSyncing,
    required int pendingSessions,
    required int failedSessions,
  }) {
    IconData icon;
    Color color;
    String tooltip;

    if (!isOnline) {
      icon = Icons.cloud_off;
      color = AppColors.warning;
      tooltip = 'Offline - Data will sync when connected';
    } else if (isSyncing) {
      icon = Icons.sync;
      color = AppColors.primary;
      tooltip = 'Syncing workout data...';
    } else if (failedSessions > 0) {
      icon = Icons.sync_problem;
      color = AppColors.error;
      tooltip = '$failedSessions workouts failed to sync';
    } else if (pendingSessions > 0) {
      icon = Icons.cloud_upload;
      color = AppColors.warning;
      tooltip = '$pendingSessions workouts pending sync';
    } else {
      return const SizedBox.shrink();
    }

    return Tooltip(
      message: tooltip,
      child: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: isSyncing
            ? SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                ),
              )
            : Icon(
                icon,
                size: 16,
                color: color,
              ),
      ),
    );
  }

  Widget _buildFullIndicator({
    required bool isOnline,
    required bool isSyncing,
    required int pendingSessions,
    required int failedSessions,
  }) {
    String title;
    String subtitle;
    IconData icon;
    Color color;
    VoidCallback? onTap;

    if (!isOnline) {
      title = 'Offline Mode';
      subtitle = 'Workouts will sync when connected';
      icon = Icons.cloud_off;
      color = AppColors.warning;
    } else if (isSyncing) {
      title = 'Syncing Data';
      subtitle = 'Uploading workout progress...';
      icon = Icons.sync;
      color = AppColors.primary;
    } else if (failedSessions > 0) {
      title = 'Sync Issues';
      subtitle =
          '$failedSessions workout${failedSessions == 1 ? '' : 's'} failed to sync';
      icon = Icons.sync_problem;
      color = AppColors.error;
      onTap = () => _showSyncDetails(context, pendingSessions, failedSessions);
    } else if (pendingSessions > 0) {
      title = 'Pending Sync';
      subtitle =
          '$pendingSessions workout${pendingSessions == 1 ? '' : 's'} waiting to sync';
      icon = Icons.cloud_upload;
      color = AppColors.warning;
      onTap = () => _showSyncDetails(context, pendingSessions, failedSessions);
    } else {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: AppSpacing.screenPadding,
          vertical: AppSpacing.xs,
        ),
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: AppBorderRadius.cardRadius,
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            isSyncing
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(color),
                    ),
                  )
                : Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.buttonMedium.copyWith(
                      color: color,
                    ),
                  ),
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    subtitle,
                    style: AppTypography.caption.copyWith(
                      color: color,
                    ),
                  ),
                ],
              ),
            ),
            if (onTap != null) ...[
              Icon(
                Icons.chevron_right,
                color: color,
                size: 20,
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showSyncDetails(
      BuildContext context, int pendingSessions, int failedSessions) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SyncStatusModal(
        pendingSessions: pendingSessions,
        failedSessions: failedSessions,
      ),
    );
  }
}

class SyncStatusModal extends StatelessWidget {
  final int pendingSessions;
  final int failedSessions;

  const SyncStatusModal({
    super.key,
    required this.pendingSessions,
    required this.failedSessions,
  });

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      maxChildSize: 0.8,
      minChildSize: 0.4,
      builder: (context, scrollController) => Container(
        decoration: BoxDecoration(
          color: AppColorsTheme.surface(context),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColorsTheme.textSecondary(context)
                      .withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Icon(
                  failedSessions > 0 ? Icons.sync_problem : Icons.cloud_upload,
                  color:
                      failedSessions > 0 ? AppColors.error : AppColors.warning,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Sync Status',
                    style: AppTypography.heading2.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Expanded(
              child: Consumer<SyncService>(
                builder: (context, syncService, child) {
                  return SingleChildScrollView(
                    controller: scrollController,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (pendingSessions > 0) ...[
                          _buildSyncSection(
                            title: 'Pending Sync',
                            count: pendingSessions,
                            icon: Icons.cloud_upload,
                            color: AppColors.warning,
                            description:
                                'These workouts will sync automatically when you have a stable connection.',
                          ),
                          const SizedBox(height: 24),
                        ],
                        if (failedSessions > 0) ...[
                          _buildSyncSection(
                            title: 'Failed Sync',
                            count: failedSessions,
                            icon: Icons.sync_problem,
                            color: AppColors.error,
                            description:
                                'These workouts encountered errors during sync. They will be retried automatically.',
                          ),
                          const SizedBox(height: 24),
                        ],
                        _buildSyncStats(syncService),
                        const SizedBox(height: 32),
                        _buildSyncActions(context, syncService),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncSection({
    required String title,
    required int count,
    required IconData icon,
    required Color color,
    required String description,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: AppTypography.buttonMedium.copyWith(color: color),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  count.toString(),
                  style: AppTypography.small.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: AppTypography.body2.copyWith(color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildSyncStats(SyncService syncService) {
    final stats = syncService.getSyncStatistics();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Sync Statistics',
                style: AppTypography.buttonMedium.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Success Rate',
                  '${stats['successRate']}%',
                  AppColors.success,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Total Syncs',
                  '${stats['totalSyncAttempts']}',
                  AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (stats['lastSuccessfulSync'] != null) ...[
            Text(
              'Last successful sync: ${_formatDateTime(stats['lastSuccessfulSync'])}',
              style: AppTypography.caption.copyWith(
                color: AppColors.primary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: AppTypography.heading3.copyWith(
            color: color,
            fontSize: 18,
          ),
        ),
        Text(
          label,
          style: AppTypography.caption.copyWith(
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildSyncActions(BuildContext context, SyncService syncService) {
    return Column(
      children: [
        AppButton(
          text: syncService.isSyncing ? 'Syncing...' : 'Sync Now',
          icon: syncService.isSyncing ? null : Icons.sync,
          onPressed: syncService.isSyncing
              ? null
              : () async {
                  final result = await syncService.syncPendingData();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(result.message),
                        backgroundColor: result.success
                            ? AppColors.success
                            : AppColors.error,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                    if (result.success) {
                      Navigator.of(context).pop();
                    }
                  }
                },
          variant: AppButtonVariant.primary,
          fullWidth: true,
          isLoading: syncService.isSyncing,
        ),
        const SizedBox(height: 12),
        AppButton(
          text: 'Close',
          onPressed: () => Navigator.of(context).pop(),
          variant: AppButtonVariant.secondary,
          fullWidth: true,
        ),
      ],
    );
  }

  String _formatDateTime(String? isoString) {
    if (isoString == null) return 'Never';

    try {
      final dateTime = DateTime.parse(isoString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inHours < 1) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inDays < 1) {
        return '${difference.inHours}h ago';
      } else {
        return '${difference.inDays}d ago';
      }
    } catch (e) {
      return 'Unknown';
    }
  }
}
