import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../design_system/design_system.dart';
import '../models/exercise.dart';
import '../models/offline_workout_session.dart';

import '../services/progress_service.dart';

/// A panel for mid-workout exercise modifications including weight adjustments,
/// exercise skipping, and alternative exercise suggestions
class ExerciseModificationPanel extends StatefulWidget {
  final Exercise currentExercise;
  final int currentSetNumber;
  final String sessionId;
  final List<OfflineSetData> previousSetsInSession;
  final OfflineSetData? lastSetData;
  final Function(ExerciseModification modification) onModificationApplied;
  final VoidCallback? onClose;

  const ExerciseModificationPanel({
    super.key,
    required this.currentExercise,
    required this.currentSetNumber,
    required this.sessionId,
    this.previousSetsInSession = const [],
    this.lastSetData,
    required this.onModificationApplied,
    this.onClose,
  });

  @override
  State<ExerciseModificationPanel> createState() =>
      _ExerciseModificationPanelState();
}

class _ExerciseModificationPanelState extends State<ExerciseModificationPanel>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _weightController = TextEditingController();
  final _repsController = TextEditingController();

  bool _isLoading = false;
  List<Exercise> _alternativeExercises = [];
  WeightAdjustmentSuggestion? _weightSuggestion;
  String? _skipReason;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeControllers();
    _loadAlternativeExercises();
    _generateWeightSuggestion();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _weightController.dispose();
    _repsController.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    if (widget.lastSetData != null) {
      _weightController.text = widget.lastSetData!.weight.toString();
      _repsController.text = widget.lastSetData!.reps.toString();
    }
  }

  Future<void> _loadAlternativeExercises() async {
    setState(() => _isLoading = true);

    try {
      // Get exercises with same primary muscle group
      final alternatives = await _findAlternativeExercises(
        widget.currentExercise,
      );

      if (mounted) {
        setState(() {
          _alternativeExercises = alternatives;
        });
      }
    } catch (e) {
      debugPrint('Error loading alternative exercises: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<List<Exercise>> _findAlternativeExercises(
    Exercise currentExercise,
  ) async {
    // This would typically query the exercises table for alternatives
    // For now, return a mock list based on muscle group
    final alternatives = <Exercise>[];

    // Mock alternative exercises based on primary muscle
    switch (currentExercise.primaryMuscle?.toLowerCase()) {
      case 'chest':
        alternatives.addAll([
          Exercise(
            id: 'alt_chest_1',
            name: 'Push-ups',
            primaryMuscle: 'Chest',
            equipment: 'Bodyweight',
            description: 'Bodyweight chest exercise',
          ),
          Exercise(
            id: 'alt_chest_2',
            name: 'Dumbbell Flyes',
            primaryMuscle: 'Chest',
            equipment: 'Dumbbells',
            description: 'Isolation chest exercise',
          ),
        ]);
        break;
      case 'back':
        alternatives.addAll([
          Exercise(
            id: 'alt_back_1',
            name: 'Pull-ups',
            primaryMuscle: 'Back',
            equipment: 'Pull-up Bar',
            description: 'Bodyweight back exercise',
          ),
          Exercise(
            id: 'alt_back_2',
            name: 'Seated Cable Row',
            primaryMuscle: 'Back',
            equipment: 'Cable Machine',
            description: 'Seated rowing exercise',
          ),
        ]);
        break;
      case 'legs':
      case 'quadriceps':
        alternatives.addAll([
          Exercise(
            id: 'alt_legs_1',
            name: 'Bodyweight Squats',
            primaryMuscle: 'Quadriceps',
            equipment: 'Bodyweight',
            description: 'Bodyweight leg exercise',
          ),
          Exercise(
            id: 'alt_legs_2',
            name: 'Leg Press',
            primaryMuscle: 'Quadriceps',
            equipment: 'Leg Press Machine',
            description: 'Machine-based leg exercise',
          ),
        ]);
        break;
      default:
        // Add generic alternatives
        alternatives.add(
          Exercise(
            id: 'alt_generic_1',
            name: 'Modified ${currentExercise.name}',
            primaryMuscle: currentExercise.primaryMuscle,
            equipment: 'Bodyweight',
            description: 'Bodyweight variation',
          ),
        );
    }

    return alternatives;
  }

  Future<void> _generateWeightSuggestion() async {
    if (widget.previousSetsInSession.isEmpty) return;

    try {
      final progressService = context.read<ProgressService>();

      // Analyze recent performance to suggest weight adjustments
      final suggestion = await _analyzePerformanceAndSuggestWeight(
        progressService,
        widget.currentExercise,
        widget.previousSetsInSession,
      );

      if (mounted) {
        setState(() {
          _weightSuggestion = suggestion;
        });
      }
    } catch (e) {
      debugPrint('Error generating weight suggestion: $e');
    }
  }

  Future<WeightAdjustmentSuggestion> _analyzePerformanceAndSuggestWeight(
    ProgressService progressService,
    Exercise exercise,
    List<OfflineSetData> previousSets,
  ) async {
    // Get historical data for this exercise
    await progressService.getExerciseProgress(
      exercise.id,
      limit: 10,
    );

    // Analyze current session performance
    final currentSessionSets =
        previousSets.where((set) => set.exerciseId == exercise.id).toList();

    if (currentSessionSets.isEmpty) {
      return WeightAdjustmentSuggestion(
        suggestedWeight: widget.lastSetData?.weight ?? 0.0,
        adjustmentType: WeightAdjustmentType.maintain,
        reasoning: 'No previous sets to analyze',
        confidence: 0.5,
      );
    }

    // Calculate average difficulty rating for current session
    final ratingsWithValues = currentSessionSets
        .where((set) => set.difficultyRating != null)
        .map((set) => set.difficultyRating!)
        .toList();

    if (ratingsWithValues.isEmpty) {
      return WeightAdjustmentSuggestion(
        suggestedWeight: widget.lastSetData?.weight ?? 0.0,
        adjustmentType: WeightAdjustmentType.maintain,
        reasoning: 'No difficulty ratings available',
        confidence: 0.5,
      );
    }

    final avgDifficulty =
        ratingsWithValues.reduce((a, b) => a + b) / ratingsWithValues.length;
    final lastWeight = currentSessionSets.last.weight;

    // Determine adjustment based on difficulty
    WeightAdjustmentType adjustmentType;
    double suggestedWeight;
    String reasoning;
    double confidence = 0.8;

    if (avgDifficulty <= 2.0) {
      // Too easy - increase weight
      adjustmentType = WeightAdjustmentType.increase;
      suggestedWeight = lastWeight * 1.05; // 5% increase
      reasoning = 'Sets rated as easy - increase weight for better stimulus';
    } else if (avgDifficulty >= 4.5) {
      // Too hard - decrease weight
      adjustmentType = WeightAdjustmentType.decrease;
      suggestedWeight = lastWeight * 0.9; // 10% decrease
      reasoning = 'Sets rated as very hard - reduce weight for better form';
    } else if (avgDifficulty >= 4.0) {
      // Hard but manageable - slight decrease
      adjustmentType = WeightAdjustmentType.decrease;
      suggestedWeight = lastWeight * 0.95; // 5% decrease
      reasoning = 'Sets rated as hard - slight reduction for remaining sets';
    } else {
      // Good difficulty range - maintain
      adjustmentType = WeightAdjustmentType.maintain;
      suggestedWeight = lastWeight;
      reasoning = 'Good difficulty level - maintain current weight';
    }

    return WeightAdjustmentSuggestion(
      suggestedWeight: suggestedWeight,
      adjustmentType: adjustmentType,
      reasoning: reasoning,
      confidence: confidence,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildWeightAdjustmentTab(),
                _buildAlternativeExercisesTab(),
                _buildSkipExerciseTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.grey200),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Modify Exercise',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.currentExercise.name,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                ),
                Text(
                  'Set ${widget.currentSetNumber}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.grey500,
                      ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: widget.onClose,
            icon: const Icon(Icons.close),
            style: IconButton.styleFrom(
              backgroundColor: AppColors.grey100,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.grey200),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.grey500,
        indicatorColor: AppColors.primary,
        tabs: const [
          Tab(
            icon: Icon(Icons.tune),
            text: 'Adjust Weight',
          ),
          Tab(
            icon: Icon(Icons.swap_horiz),
            text: 'Alternatives',
          ),
          Tab(
            icon: Icon(Icons.skip_next),
            text: 'Skip Exercise',
          ),
        ],
      ),
    );
  }

  Widget _buildWeightAdjustmentTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_weightSuggestion != null) ...[
            _buildWeightSuggestionCard(),
            const SizedBox(height: 20),
          ],
          Text(
            'Manual Adjustment',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _weightController,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  decoration: InputDecoration(
                    labelText: 'Weight (lbs)',
                    prefixIcon: const Icon(Icons.fitness_center),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _repsController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  decoration: InputDecoration(
                    labelText: 'Target Reps',
                    prefixIcon: const Icon(Icons.repeat),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildQuickAdjustmentButtons(),
          const SizedBox(height: 30),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _applyWeightAdjustment,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Apply Weight Adjustment',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeightSuggestionCard() {
    final suggestion = _weightSuggestion!;

    Color cardColor;
    IconData icon;

    switch (suggestion.adjustmentType) {
      case WeightAdjustmentType.increase:
        cardColor = AppColors.success;
        icon = Icons.trending_up;
        break;
      case WeightAdjustmentType.decrease:
        cardColor = AppColors.warning;
        icon = Icons.trending_down;
        break;
      case WeightAdjustmentType.maintain:
        cardColor = AppColors.primary;
        icon = Icons.trending_flat;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: cardColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: cardColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'AI Suggestion',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: cardColor,
                      ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: cardColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${(suggestion.confidence * 100).round()}% confident',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: cardColor,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '${suggestion.suggestedWeight.toStringAsFixed(1)} lbs',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: cardColor,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            suggestion.reasoning,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.grey600,
                ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () {
                _weightController.text =
                    suggestion.suggestedWeight.toStringAsFixed(1);
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: cardColor,
                side: BorderSide(color: cardColor),
              ),
              child: const Text('Use This Suggestion'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAdjustmentButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Adjustments',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildQuickAdjustButton('-10%', -0.1),
            _buildQuickAdjustButton('-5%', -0.05),
            _buildQuickAdjustButton('+5%', 0.05),
            _buildQuickAdjustButton('+10%', 0.1),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickAdjustButton(String label, double multiplier) {
    return OutlinedButton(
      onPressed: () {
        final currentWeight = double.tryParse(_weightController.text) ?? 0.0;
        final newWeight = currentWeight * (1 + multiplier);
        _weightController.text = newWeight.toStringAsFixed(1);
      },
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primary,
        side: const BorderSide(color: AppColors.primary),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
      child: Text(label),
    );
  }

  Widget _buildAlternativeExercisesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Alternative Exercises',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose an alternative exercise that targets the same muscle group',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.grey600,
                ),
          ),
          const SizedBox(height: 20),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            )
          else if (_alternativeExercises.isEmpty)
            _buildNoAlternativesMessage()
          else
            ..._alternativeExercises.map(_buildAlternativeExerciseCard),
        ],
      ),
    );
  }

  Widget _buildNoAlternativesMessage() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.search_off,
            size: 48,
            color: AppColors.grey400,
          ),
          const SizedBox(height: 12),
          Text(
            'No alternatives found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try modifying the current exercise or skipping it for now',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.grey600,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAlternativeExerciseCard(Exercise exercise) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: () => _selectAlternativeExercise(exercise),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.fitness_center,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        exercise.name,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                      ),
                      const SizedBox(height: 4),
                      if (exercise.primaryMuscle != null)
                        Text(
                          exercise.primaryMuscle!,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                      if (exercise.equipment != null)
                        Text(
                          exercise.equipment!,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.grey600,
                                  ),
                        ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.grey400,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSkipExerciseTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Skip Exercise',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Skip this exercise and move to the next one. This will be logged for future reference.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.grey600,
                ),
          ),
          const SizedBox(height: 20),
          Text(
            'Reason for skipping (optional)',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 12),
          ..._buildSkipReasonOptions(),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border:
                  Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_amber,
                  color: AppColors.warning,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Skipping exercises regularly may affect your progress. Consider finding alternatives or adjusting the weight instead.',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.grey700,
                        ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 30),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _skipExercise,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Skip This Exercise',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildSkipReasonOptions() {
    final reasons = [
      'Equipment not available',
      'Injury or discomfort',
      'Too difficult',
      'Not enough time',
      'Don\'t like this exercise',
      'Other',
    ];

    return reasons.map((reason) {
      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        child: RadioListTile<String>(
          title: Text(reason),
          value: reason,
          groupValue: _skipReason,
          onChanged: (value) {
            setState(() {
              _skipReason = value;
            });
          },
          activeColor: AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }).toList();
  }

  void _applyWeightAdjustment() {
    final weight = double.tryParse(_weightController.text);
    final reps = int.tryParse(_repsController.text);

    if (weight == null || weight < 0) {
      _showError('Please enter a valid weight');
      return;
    }

    if (reps == null || reps <= 0) {
      _showError('Please enter valid reps');
      return;
    }

    final modification = ExerciseModification.weightAdjustment(
      exerciseId: widget.currentExercise.id,
      newWeight: weight,
      newReps: reps,
      reason: _weightSuggestion?.reasoning ?? 'Manual adjustment',
    );

    widget.onModificationApplied(modification);
    widget.onClose?.call();
  }

  void _selectAlternativeExercise(Exercise exercise) {
    final modification = ExerciseModification.exerciseSubstitution(
      originalExerciseId: widget.currentExercise.id,
      newExercise: exercise,
      reason: 'Alternative exercise selected',
    );

    widget.onModificationApplied(modification);
    widget.onClose?.call();
  }

  void _skipExercise() {
    final modification = ExerciseModification.exerciseSkip(
      exerciseId: widget.currentExercise.id,
      reason: _skipReason ?? 'No reason provided',
    );

    widget.onModificationApplied(modification);
    widget.onClose?.call();
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }
}

/// Represents different types of exercise modifications
class ExerciseModification {
  final ExerciseModificationType type;
  final String exerciseId;
  final String reason;
  final Map<String, dynamic> data;

  ExerciseModification._({
    required this.type,
    required this.exerciseId,
    required this.reason,
    required this.data,
  });

  factory ExerciseModification.weightAdjustment({
    required String exerciseId,
    required double newWeight,
    required int newReps,
    required String reason,
  }) {
    return ExerciseModification._(
      type: ExerciseModificationType.weightAdjustment,
      exerciseId: exerciseId,
      reason: reason,
      data: {
        'newWeight': newWeight,
        'newReps': newReps,
      },
    );
  }

  factory ExerciseModification.exerciseSubstitution({
    required String originalExerciseId,
    required Exercise newExercise,
    required String reason,
  }) {
    return ExerciseModification._(
      type: ExerciseModificationType.exerciseSubstitution,
      exerciseId: originalExerciseId,
      reason: reason,
      data: {
        'newExercise': newExercise,
      },
    );
  }

  factory ExerciseModification.exerciseSkip({
    required String exerciseId,
    required String reason,
  }) {
    return ExerciseModification._(
      type: ExerciseModificationType.exerciseSkip,
      exerciseId: exerciseId,
      reason: reason,
      data: {},
    );
  }

  // Getters for type-safe access to data
  double? get newWeight => data['newWeight'] as double?;
  int? get newReps => data['newReps'] as int?;
  Exercise? get newExercise => data['newExercise'] as Exercise?;
}

enum ExerciseModificationType {
  weightAdjustment,
  exerciseSubstitution,
  exerciseSkip,
}

/// Weight adjustment suggestion based on user feedback and performance
class WeightAdjustmentSuggestion {
  final double suggestedWeight;
  final WeightAdjustmentType adjustmentType;
  final String reasoning;
  final double confidence;

  WeightAdjustmentSuggestion({
    required this.suggestedWeight,
    required this.adjustmentType,
    required this.reasoning,
    required this.confidence,
  });
}

enum WeightAdjustmentType {
  increase,
  decrease,
  maintain,
}
