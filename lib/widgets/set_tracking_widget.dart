import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../design_system/design_system.dart';
import '../models/exercise.dart';
import '../models/offline_workout_session.dart';
import '../services/offline_service.dart';
import '../services/progress_service.dart';

/// A widget for tracking individual sets with weight/rep input, difficulty feedback,
/// and intelligent auto-suggestions based on previous performance and fatigue
class SetTrackingWidget extends StatefulWidget {
  final Exercise exercise;
  final int setNumber;
  final String sessionId;
  final OfflineSetData? previousSetData;
  final List<OfflineSetData> previousSetsInSession;
  final Function(OfflineSetData setData) onSetCompleted;
  final Function(String exerciseId, int setNumber)? onSetSkipped;
  final bool isEnabled;

  const SetTrackingWidget({
    super.key,
    required this.exercise,
    required this.setNumber,
    required this.sessionId,
    this.previousSetData,
    this.previousSetsInSession = const [],
    required this.onSetCompleted,
    this.onSetSkipped,
    this.isEnabled = true,
  });

  @override
  State<SetTrackingWidget> createState() => _SetTrackingWidgetState();
}

class _SetTrackingWidgetState extends State<SetTrackingWidget> {
  final _weightController = TextEditingController();
  final _repsController = TextEditingController();
  final _notesController = TextEditingController();
  final _weightFocusNode = FocusNode();
  final _repsFocusNode = FocusNode();

  int? _difficultyRating;
  bool _isLoading = false;
  bool _hasAutoSuggested = false;
  SetSuggestion? _currentSuggestion;

  @override
  void initState() {
    super.initState();
    _loadSuggestions();
  }

  @override
  void dispose() {
    _weightController.dispose();
    _repsController.dispose();
    _notesController.dispose();
    _weightFocusNode.dispose();
    _repsFocusNode.dispose();
    super.dispose();
  }

  Future<void> _loadSuggestions() async {
    if (!_hasAutoSuggested && widget.isEnabled) {
      setState(() => _isLoading = true);

      try {
        final progressService = context.read<ProgressService>();
        final suggestion = await _generateSetSuggestion(progressService);

        if (mounted && suggestion != null) {
          setState(() {
            _currentSuggestion = suggestion;
            _weightController.text = suggestion.suggestedWeight.toString();
            _repsController.text = suggestion.suggestedReps.toString();
            _hasAutoSuggested = true;
          });
        }
      } catch (e) {
        debugPrint('Error loading set suggestions: $e');
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  Future<SetSuggestion?> _generateSetSuggestion(
      ProgressService progressService) async {
    try {
      // Get historical data for this exercise
      final exerciseProgress = await progressService.getExerciseProgress(
        widget.exercise.id,
        limit: 10,
      );

      // Calculate fatigue based on previous sets in current session
      final fatigueLevel = _calculateFatigueLevel();

      // Get base suggestion from historical data
      double suggestedWeight = 0.0;
      int suggestedReps = 8; // Default reps

      if (exerciseProgress.isNotEmpty) {
        // Use most recent performance as baseline
        final recentPerformance = exerciseProgress.first;
        suggestedWeight = recentPerformance['weight']?.toDouble() ?? 0.0;
        suggestedReps = recentPerformance['reps'] ?? 8;

        // Adjust for progressive overload if this is the first set
        if (widget.setNumber == 1 && fatigueLevel < 0.3) {
          // Suggest slight increase for first set if not fatigued
          suggestedWeight *= 1.025; // 2.5% increase
        }
      } else if (widget.previousSetData != null) {
        // Use previous set data if no historical data
        suggestedWeight = widget.previousSetData!.weight;
        suggestedReps = widget.previousSetData!.reps;
      }

      // Adjust for fatigue and set number
      suggestedWeight = _adjustWeightForFatigue(suggestedWeight, fatigueLevel);
      suggestedReps = _adjustRepsForFatigue(suggestedReps, fatigueLevel);

      // Adjust based on previous sets in current session
      if (widget.previousSetsInSession.isNotEmpty) {
        final lastSet = widget.previousSetsInSession.last;

        // If last set was rated as very hard (5), reduce weight
        if (lastSet.difficultyRating == 5) {
          suggestedWeight *= 0.95; // 5% reduction
        }
        // If last set was rated as easy (1-2), increase weight slightly
        else if (lastSet.difficultyRating != null &&
            lastSet.difficultyRating! <= 2) {
          suggestedWeight *= 1.025; // 2.5% increase
        }
      }

      return SetSuggestion(
        suggestedWeight: suggestedWeight,
        suggestedReps: suggestedReps,
        confidence: _calculateConfidence(exerciseProgress.length, fatigueLevel),
        reasoning: _generateReasoning(fatigueLevel, exerciseProgress.length),
      );
    } catch (e) {
      debugPrint('Error generating set suggestion: $e');
      return null;
    }
  }

  double _calculateFatigueLevel() {
    if (widget.previousSetsInSession.isEmpty) return 0.0;

    // Calculate fatigue based on:
    // 1. Number of sets completed
    // 2. Average difficulty rating
    // 3. Time since session start

    final setCount = widget.previousSetsInSession.length;
    final avgDifficulty = widget.previousSetsInSession
            .where((set) => set.difficultyRating != null)
            .map((set) => set.difficultyRating!)
            .fold<double>(0.0, (sum, rating) => sum + rating) /
        widget.previousSetsInSession.length.clamp(1, double.infinity);

    // Base fatigue increases with set count
    double fatigue = (setCount * 0.1).clamp(0.0, 1.0);

    // Adjust based on difficulty ratings
    if (avgDifficulty > 3.5) {
      fatigue += 0.2; // High difficulty increases fatigue
    } else if (avgDifficulty < 2.5) {
      fatigue -= 0.1; // Low difficulty reduces fatigue
    }

    return fatigue.clamp(0.0, 1.0);
  }

  double _adjustWeightForFatigue(double baseWeight, double fatigueLevel) {
    if (baseWeight <= 0) return baseWeight;

    // Reduce weight based on fatigue level
    final reduction = fatigueLevel * 0.15; // Up to 15% reduction
    return baseWeight * (1.0 - reduction);
  }

  int _adjustRepsForFatigue(int baseReps, double fatigueLevel) {
    if (fatigueLevel > 0.5) {
      return (baseReps * 0.9).round(); // Reduce reps when fatigued
    }
    return baseReps;
  }

  double _calculateConfidence(int historicalDataPoints, double fatigueLevel) {
    double confidence = 0.5; // Base confidence

    // More historical data increases confidence
    confidence += (historicalDataPoints * 0.05).clamp(0.0, 0.4);

    // High fatigue reduces confidence in suggestions
    confidence -= fatigueLevel * 0.2;

    return confidence.clamp(0.0, 1.0);
  }

  String _generateReasoning(double fatigueLevel, int historicalDataPoints) {
    if (historicalDataPoints == 0) {
      return 'Based on general recommendations';
    }

    String reasoning = 'Based on your recent performance';

    if (fatigueLevel > 0.5) {
      reasoning += ' with fatigue adjustment';
    } else if (fatigueLevel < 0.2) {
      reasoning += ' with progressive overload';
    }

    return reasoning;
  }

  void _completeSet() async {
    if (!_validateInputs()) return;

    setState(() => _isLoading = true);

    try {
      final weight = double.tryParse(_weightController.text) ?? 0.0;
      final reps = int.tryParse(_repsController.text) ?? 0;

      final setData = OfflineSetData(
        setId: 'set_${DateTime.now().millisecondsSinceEpoch}',
        exerciseId: widget.exercise.id,
        exerciseName: widget.exercise.name,
        setNumber: widget.setNumber,
        weight: weight,
        reps: reps,
        completedAt: DateTime.now(),
        difficultyRating: _difficultyRating,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );

      // Save to offline storage
      final offlineService = context.read<OfflineService>();
      await offlineService.addSet(
        sessionId: widget.sessionId,
        exerciseId: widget.exercise.id,
        exerciseName: widget.exercise.name,
        setNumber: widget.setNumber,
        weight: weight,
        reps: reps,
        difficultyRating: _difficultyRating,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );

      // Notify parent widget
      widget.onSetCompleted(setData);

      // Clear form for next set
      _clearForm();
    } catch (e) {
      debugPrint('Error completing set: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving set: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  bool _validateInputs() {
    final weight = double.tryParse(_weightController.text);
    final reps = int.tryParse(_repsController.text);

    if (weight == null || weight < 0) {
      _showValidationError('Please enter a valid weight');
      _weightFocusNode.requestFocus();
      return false;
    }

    if (reps == null || reps <= 0) {
      _showValidationError('Please enter a valid number of reps');
      _repsFocusNode.requestFocus();
      return false;
    }

    return true;
  }

  void _showValidationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _clearForm() {
    _weightController.clear();
    _repsController.clear();
    _notesController.clear();
    setState(() {
      _difficultyRating = null;
      _hasAutoSuggested = false;
      _currentSuggestion = null;
    });
  }

  void _skipSet() {
    widget.onSetSkipped?.call(widget.exercise.id, widget.setNumber);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.exercise.name,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      Text(
                        'Set ${widget.setNumber}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColorsTheme.textSecondary(context),
                            ),
                      ),
                    ],
                  ),
                ),
                if (_currentSuggestion != null)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          size: 16,
                          color: AppColors.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'AI Suggested',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 16),

            // Suggestion display
            if (_currentSuggestion != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColorsTheme.surface(context),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.2)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.psychology,
                          size: 16,
                          color: AppColors.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Suggested: ${_currentSuggestion!.suggestedWeight.toStringAsFixed(1)} lbs × ${_currentSuggestion!.suggestedReps} reps',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _currentSuggestion!.reasoning,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColorsTheme.textSecondary(context),
                          ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Input fields
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _weightController,
                    focusNode: _weightFocusNode,
                    enabled: widget.isEnabled && !_isLoading,
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    decoration: InputDecoration(
                      labelText: 'Weight (lbs)',
                      hintText: '0.0',
                      prefixIcon: const Icon(Icons.fitness_center),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _repsController,
                    focusNode: _repsFocusNode,
                    enabled: widget.isEnabled && !_isLoading,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    decoration: InputDecoration(
                      labelText: 'Reps',
                      hintText: '0',
                      prefixIcon: const Icon(Icons.repeat),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Difficulty rating
            Text(
              'How difficult was this set?',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
            const SizedBox(height: 8),
            Row(
              children: List.generate(5, (index) {
                final rating = index + 1;
                final isSelected = _difficultyRating == rating;

                return Expanded(
                  child: GestureDetector(
                    onTap: widget.isEnabled && !_isLoading
                        ? () => setState(() => _difficultyRating = rating)
                        : null,
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppColors.primary
                            : AppColorsTheme.surface(context),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? AppColors.primary
                              : AppColorsTheme.border(context),
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            _getDifficultyIcon(rating),
                            color: isSelected
                                ? Colors.white
                                : AppColorsTheme.textSecondary(context),
                            size: 20,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            rating.toString(),
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall
                                ?.copyWith(
                                  color: isSelected
                                      ? Colors.white
                                      : AppColorsTheme.textSecondary(context),
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ),

            const SizedBox(height: 8),
            Text(
              '1 = Very Easy, 3 = Moderate, 5 = Very Hard',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
            ),

            const SizedBox(height: 16),

            // Notes field
            TextFormField(
              controller: _notesController,
              enabled: widget.isEnabled && !_isLoading,
              maxLines: 2,
              decoration: InputDecoration(
                labelText: 'Notes (optional)',
                hintText: 'Form notes, adjustments, etc.',
                prefixIcon: const Icon(Icons.note_alt_outlined),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Action buttons
            Row(
              children: [
                if (widget.onSetSkipped != null)
                  Expanded(
                    child: OutlinedButton(
                      onPressed:
                          widget.isEnabled && !_isLoading ? _skipSet : null,
                      child: const Text('Skip Set'),
                    ),
                  ),
                if (widget.onSetSkipped != null) const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed:
                        widget.isEnabled && !_isLoading ? _completeSet : null,
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Complete Set'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getDifficultyIcon(int rating) {
    switch (rating) {
      case 1:
        return Icons.sentiment_very_satisfied;
      case 2:
        return Icons.sentiment_satisfied;
      case 3:
        return Icons.sentiment_neutral;
      case 4:
        return Icons.sentiment_dissatisfied;
      case 5:
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.sentiment_neutral;
    }
  }
}

/// Data class for set suggestions
class SetSuggestion {
  final double suggestedWeight;
  final int suggestedReps;
  final double confidence;
  final String reasoning;

  SetSuggestion({
    required this.suggestedWeight,
    required this.suggestedReps,
    required this.confidence,
    required this.reasoning,
  });
}
