import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../design_system/design_system.dart';
import '../models/session_analytics.dart';

class VolumeAnalytics extends StatefulWidget {
  final List<SessionAnalytics> sessionData;
  final Duration timeRange;
  final VolumeAnalyticsType analyticsType;

  const VolumeAnalytics({
    super.key,
    required this.sessionData,
    this.timeRange = const Duration(days: 90),
    this.analyticsType = VolumeAnalyticsType.total,
  });

  @override
  State<VolumeAnalytics> createState() => _VolumeAnalyticsState();
}

class _VolumeAnalyticsState extends State<VolumeAnalytics> {
  VolumeAnalyticsType _selectedType = VolumeAnalyticsType.total;
  VolumeTimeframe _selectedTimeframe = VolumeTimeframe.weekly;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.analyticsType;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: AppSpacing.md),
            _buildControls(),
            const SizedBox(height: AppSpacing.md),
            SizedBox(
              height: 300,
              child: _buildVolumeChart(),
            ),
            const SizedBox(height: AppSpacing.md),
            _buildVolumeStats(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Volume Analytics',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                'Training volume trends and patterns',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
            ],
          ),
        ),
        _buildTimeRangeChip(),
      ],
    );
  }

  Widget _buildTimeRangeChip() {
    String timeRangeText;
    if (widget.timeRange.inDays >= 365) {
      timeRangeText = '${(widget.timeRange.inDays / 365).round()}Y';
    } else if (widget.timeRange.inDays >= 30) {
      timeRangeText = '${(widget.timeRange.inDays / 30).round()}M';
    } else {
      timeRangeText = '${widget.timeRange.inDays}D';
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      child: Text(
        timeRangeText,
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  Widget _buildControls() {
    return Column(
      children: [
        // Analytics type selector
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: VolumeAnalyticsType.values.map((type) {
              final isSelected = _selectedType == type;
              return Padding(
                padding: const EdgeInsets.only(right: AppSpacing.sm),
                child: FilterChip(
                  label: Text(type.displayName),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedType = type;
                      });
                    }
                  },
                  selectedColor: AppColors.primary.withValues(alpha: 0.2),
                  checkmarkColor: AppColors.primary,
                ),
              );
            }).toList(),
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        // Timeframe selector
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: VolumeTimeframe.values.map((timeframe) {
              final isSelected = _selectedTimeframe == timeframe;
              return Padding(
                padding: const EdgeInsets.only(right: AppSpacing.sm),
                child: FilterChip(
                  label: Text(timeframe.displayName),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedTimeframe = timeframe;
                      });
                    }
                  },
                  selectedColor: AppColors.secondary.withValues(alpha: 0.2),
                  checkmarkColor: AppColors.secondary,
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildVolumeChart() {
    final chartData = _getChartData();

    if (chartData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart_outlined,
              size: 48,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'No volume data available',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            Text(
              'Complete more workouts to see volume trends',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
          ],
        ),
      );
    }

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxY(chartData),
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final dataPoint = chartData[groupIndex];
              return BarTooltipItem(
                '${_selectedType.displayName}: ${_formatVolumeValue(dataPoint.value)}\n',
                TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                children: [
                  TextSpan(
                    text: dataPoint.label,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) =>
                  _buildBottomTitle(value, chartData),
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 50,
              getTitlesWidget: (value, meta) => _buildLeftTitle(value),
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: AppColors.border.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        barGroups: chartData.asMap().entries.map((entry) {
          final index = entry.key;
          final dataPoint = entry.value;

          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: dataPoint.value,
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withValues(alpha: 0.8),
                    AppColors.primary,
                  ],
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                ),
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          );
        }).toList(),
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: _getHorizontalInterval(chartData),
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: AppColors.border.withValues(alpha: 0.3),
              strokeWidth: 1,
            );
          },
        ),
      ),
    );
  }

  Widget _buildVolumeStats() {
    final chartData = _getChartData();

    if (chartData.isEmpty) return const SizedBox.shrink();

    final totalVolume =
        chartData.fold<double>(0, (sum, data) => sum + data.value);
    final averageVolume = totalVolume / chartData.length;
    final maxVolume =
        chartData.map((data) => data.value).reduce((a, b) => a > b ? a : b);
    final minVolume =
        chartData.map((data) => data.value).reduce((a, b) => a < b ? a : b);

    // Calculate trend
    final trend = _calculateTrend(chartData);
    final trendIcon = trend > 0
        ? Icons.trending_up
        : trend < 0
            ? Icons.trending_down
            : Icons.trending_flat;
    final trendColor = trend > 0
        ? AppColors.success
        : trend < 0
            ? AppColors.error
            : AppColors.textSecondary;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        border: Border.all(color: AppColors.border.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Total Volume',
                  _formatVolumeValue(totalVolume),
                  Icons.fitness_center_outlined,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: AppColors.border.withValues(alpha: 0.3),
              ),
              Expanded(
                child: _buildStatItem(
                  'Average',
                  _formatVolumeValue(averageVolume),
                  Icons.analytics_outlined,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: AppColors.border.withValues(alpha: 0.3),
              ),
              Expanded(
                child: _buildStatItem(
                  'Peak',
                  _formatVolumeValue(maxVolume),
                  Icons.star_outline,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.md),
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: trendColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  trendIcon,
                  size: 16,
                  color: trendColor,
                ),
                const SizedBox(width: AppSpacing.xs),
                Text(
                  _getTrendText(trend),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: trendColor,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.textSecondary,
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          label,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: AppColors.textSecondary,
              ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  List<VolumeDataPoint> _getChartData() {
    final cutoffDate = DateTime.now().subtract(widget.timeRange);
    final filteredSessions = widget.sessionData
        .where((session) => session.sessionDate.isAfter(cutoffDate))
        .toList()
      ..sort((a, b) => a.sessionDate.compareTo(b.sessionDate));

    if (filteredSessions.isEmpty) return [];

    switch (_selectedTimeframe) {
      case VolumeTimeframe.daily:
        return _getDailyData(filteredSessions);
      case VolumeTimeframe.weekly:
        return _getWeeklyData(filteredSessions);
      case VolumeTimeframe.monthly:
        return _getMonthlyData(filteredSessions);
    }
  }

  List<VolumeDataPoint> _getDailyData(List<SessionAnalytics> sessions) {
    final dataMap = <String, double>{};

    for (final session in sessions) {
      final dateKey = _formatDateKey(session.sessionDate);
      final value = _getSessionValue(session);
      dataMap[dateKey] = (dataMap[dateKey] ?? 0) + value;
    }

    return dataMap.entries.map((entry) {
      final date = DateTime.parse(entry.key);
      return VolumeDataPoint(
        value: entry.value,
        label: '${date.month}/${date.day}',
        date: date,
      );
    }).toList()
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  List<VolumeDataPoint> _getWeeklyData(List<SessionAnalytics> sessions) {
    final dataMap = <String, double>{};

    for (final session in sessions) {
      final weekKey = _getWeekKey(session.sessionDate);
      final value = _getSessionValue(session);
      dataMap[weekKey] = (dataMap[weekKey] ?? 0) + value;
    }

    return dataMap.entries.map((entry) {
      final parts = entry.key.split('-W');
      final year = int.parse(parts[0]);
      final week = int.parse(parts[1]);
      final date = _getDateFromWeek(year, week);

      return VolumeDataPoint(
        value: entry.value,
        label: 'W$week',
        date: date,
      );
    }).toList()
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  List<VolumeDataPoint> _getMonthlyData(List<SessionAnalytics> sessions) {
    final dataMap = <String, double>{};

    for (final session in sessions) {
      final monthKey = _getMonthKey(session.sessionDate);
      final value = _getSessionValue(session);
      dataMap[monthKey] = (dataMap[monthKey] ?? 0) + value;
    }

    return dataMap.entries.map((entry) {
      final parts = entry.key.split('-');
      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final date = DateTime(year, month);

      return VolumeDataPoint(
        value: entry.value,
        label: '${_getMonthName(month)} ${year.toString().substring(2)}',
        date: date,
      );
    }).toList()
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  double _getSessionValue(SessionAnalytics session) {
    switch (_selectedType) {
      case VolumeAnalyticsType.total:
        return session.totalVolume;
      case VolumeAnalyticsType.average:
        return session.setsCompleted > 0
            ? session.totalVolume / session.setsCompleted
            : 0;
      case VolumeAnalyticsType.sets:
        return session.setsCompleted.toDouble();
      case VolumeAnalyticsType.reps:
        return session.totalReps.toDouble();
    }
  }

  String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  String _getWeekKey(DateTime date) {
    final dayOfYear = date.difference(DateTime(date.year, 1, 1)).inDays;
    final week =
        ((dayOfYear + DateTime(date.year, 1, 1).weekday - 1) / 7).ceil();
    return '${date.year}-W$week';
  }

  String _getMonthKey(DateTime date) {
    return '${date.year}-${date.month}';
  }

  DateTime _getDateFromWeek(int year, int week) {
    final jan1 = DateTime(year, 1, 1);
    final daysToAdd = (week - 1) * 7 - jan1.weekday + 1;
    return jan1.add(Duration(days: daysToAdd));
  }

  String _getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }

  double _getMaxY(List<VolumeDataPoint> chartData) {
    if (chartData.isEmpty) return 100;

    final maxValue =
        chartData.map((data) => data.value).reduce((a, b) => a > b ? a : b);
    return (maxValue * 1.1).ceilToDouble();
  }

  double _getHorizontalInterval(List<VolumeDataPoint> chartData) {
    if (chartData.isEmpty) return 10;

    final maxValue = _getMaxY(chartData);
    return (maxValue / 5).ceilToDouble();
  }

  Widget _buildBottomTitle(double value, List<VolumeDataPoint> chartData) {
    final index = value.toInt();
    if (index >= 0 && index < chartData.length) {
      return Padding(
        padding: const EdgeInsets.only(top: 4),
        child: Text(
          chartData[index].label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
                fontSize: 10,
              ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildLeftTitle(double value) {
    return Text(
      _formatVolumeValueShort(value),
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
            fontSize: 10,
          ),
    );
  }

  String _formatVolumeValue(double value) {
    switch (_selectedType) {
      case VolumeAnalyticsType.total:
      case VolumeAnalyticsType.average:
        return '${value.toStringAsFixed(0)} lbs';
      case VolumeAnalyticsType.sets:
        return '${value.toInt()} sets';
      case VolumeAnalyticsType.reps:
        return '${value.toInt()} reps';
    }
  }

  String _formatVolumeValueShort(double value) {
    switch (_selectedType) {
      case VolumeAnalyticsType.total:
      case VolumeAnalyticsType.average:
        if (value >= 1000) {
          return '${(value / 1000).toStringAsFixed(1)}k';
        }
        return value.toInt().toString();
      case VolumeAnalyticsType.sets:
      case VolumeAnalyticsType.reps:
        return value.toInt().toString();
    }
  }

  double _calculateTrend(List<VolumeDataPoint> chartData) {
    if (chartData.length < 2) return 0;

    final firstHalf = chartData.take(chartData.length ~/ 2).toList();
    final secondHalf = chartData.skip(chartData.length ~/ 2).toList();

    final firstAverage =
        firstHalf.fold<double>(0, (sum, data) => sum + data.value) /
            firstHalf.length;
    final secondAverage =
        secondHalf.fold<double>(0, (sum, data) => sum + data.value) /
            secondHalf.length;

    return ((secondAverage - firstAverage) / firstAverage) * 100;
  }

  String _getTrendText(double trend) {
    if (trend > 5) {
      return 'Strong upward trend (+${trend.toStringAsFixed(1)}%)';
    } else if (trend > 0) {
      return 'Slight upward trend (+${trend.toStringAsFixed(1)}%)';
    } else if (trend < -5) {
      return 'Declining trend (${trend.toStringAsFixed(1)}%)';
    } else if (trend < 0) {
      return 'Slight decline (${trend.toStringAsFixed(1)}%)';
    } else {
      return 'Stable trend';
    }
  }
}

class VolumeDataPoint {
  final double value;
  final String label;
  final DateTime date;

  VolumeDataPoint({
    required this.value,
    required this.label,
    required this.date,
  });
}

enum VolumeAnalyticsType {
  total,
  average,
  sets,
  reps;

  String get displayName {
    switch (this) {
      case VolumeAnalyticsType.total:
        return 'Total Volume';
      case VolumeAnalyticsType.average:
        return 'Avg Volume';
      case VolumeAnalyticsType.sets:
        return 'Total Sets';
      case VolumeAnalyticsType.reps:
        return 'Total Reps';
    }
  }
}

enum VolumeTimeframe {
  daily,
  weekly,
  monthly;

  String get displayName {
    switch (this) {
      case VolumeTimeframe.daily:
        return 'Daily';
      case VolumeTimeframe.weekly:
        return 'Weekly';
      case VolumeTimeframe.monthly:
        return 'Monthly';
    }
  }
}
