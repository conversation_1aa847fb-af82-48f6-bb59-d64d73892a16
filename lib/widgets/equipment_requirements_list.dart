import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// A widget that displays required equipment with alternatives
class EquipmentRequirementsList extends StatelessWidget {
  final List<String> requiredEquipment;
  final Map<String, List<String>>? alternatives;
  final bool showAlternatives;
  final bool isCompact;

  const EquipmentRequirementsList({
    super.key,
    required this.requiredEquipment,
    this.alternatives,
    this.showAlternatives = true,
    this.isCompact = false,
  });

  // Default equipment alternatives
  static const Map<String, List<String>> _defaultAlternatives = {
    'Barbell': ['Dumbbells', 'Resistance Bands', 'Kettlebell'],
    'Dumbbells': ['Barbell', 'Resistance Bands', 'Water Bottles'],
    'Pull-up Bar': ['Resistance Bands', 'TRX Straps', 'Lat Pulldown Machine'],
    'Bench': ['Stability Ball', 'Floor', 'Chair'],
    'Cable Machine': ['Resistance Bands', 'Dumbbells', 'Barbell'],
    'Kettlebell': ['Dumbbells', 'Barbell', 'Resistance Bands'],
    'Resistance Bands': ['Dumbbells', 'Cable Machine', 'Bodyweight'],
    'Medicine Ball': ['Dumbbell', 'Kettlebell', 'Water Jug'],
    'Smith Machine': ['Barbell', 'Dumbbells', 'Bodyweight'],
    'Leg Press Machine': ['Squats', 'Dumbbells', 'Bodyweight'],
  };

  Map<String, List<String>> get _equipmentAlternatives {
    return alternatives ?? _defaultAlternatives;
  }

  @override
  Widget build(BuildContext context) {
    if (requiredEquipment.isEmpty) {
      return _buildNoEquipmentNeeded(context);
    }

    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context),

          const SizedBox(height: AppSpacing.md),

          // Equipment list
          ...requiredEquipment.asMap().entries.map((entry) {
            final index = entry.key;
            final equipment = entry.value;
            return Column(
              children: [
                _buildEquipmentItem(context, equipment),
                if (index < requiredEquipment.length - 1)
                  const SizedBox(height: AppSpacing.md),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.fitness_center,
          size: 24,
          color: AppColors.primary,
        ),
        const SizedBox(width: AppSpacing.sm),
        Text(
          'Equipment Needed',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColorsTheme.textPrimary(context),
              ),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.sm,
            vertical: AppSpacing.xs,
          ),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppSpacing.sm),
          ),
          child: Text(
            '${requiredEquipment.length} item${requiredEquipment.length != 1 ? 's' : ''}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildEquipmentItem(BuildContext context, String equipment) {
    final alternatives = _equipmentAlternatives[equipment] ?? [];

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColorsTheme.surfaceVariant(context),
        borderRadius: BorderRadius.circular(AppSpacing.sm),
        border: Border.all(
          color: AppColorsTheme.borderLight(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main equipment
          Row(
            children: [
              _buildEquipmentIcon(equipment),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      equipment,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColorsTheme.textPrimary(context),
                          ),
                    ),
                    Text(
                      _getEquipmentDescription(equipment),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColorsTheme.textSecondary(context),
                          ),
                    ),
                  ],
                ),
              ),
              _buildAvailabilityChip(context, true), // Assume available for now
            ],
          ),

          // Alternatives
          if (showAlternatives && alternatives.isNotEmpty && !isCompact) ...[
            const SizedBox(height: AppSpacing.md),
            _buildAlternatives(context, alternatives),
          ],
        ],
      ),
    );
  }

  Widget _buildEquipmentIcon(String equipment) {
    IconData icon;
    Color color;

    switch (equipment.toLowerCase()) {
      case 'barbell':
        icon = Icons.fitness_center;
        color = AppColors.primary;
        break;
      case 'dumbbells':
      case 'dumbbell':
        icon = Icons.fitness_center;
        color = AppColors.success;
        break;
      case 'bench':
        icon = Icons.weekend;
        color = AppColors.warning;
        break;
      case 'pull-up bar':
        icon = Icons.horizontal_rule;
        color = AppColors.primary;
        break;
      case 'resistance bands':
        icon = Icons.linear_scale;
        color = AppColors.success;
        break;
      case 'kettlebell':
        icon = Icons.sports_gymnastics;
        color = AppColors.warning;
        break;
      case 'medicine ball':
        icon = Icons.sports_basketball;
        color = AppColors.error;
        break;
      default:
        icon = Icons.fitness_center;
        color = AppColors.primary;
    }

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSpacing.sm),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Icon(
        icon,
        color: color,
        size: 24,
      ),
    );
  }

  Widget _buildAvailabilityChip(BuildContext context, bool isAvailable) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: isAvailable
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSpacing.sm),
        border: Border.all(
          color: isAvailable
              ? AppColors.success.withValues(alpha: 0.3)
              : AppColors.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isAvailable ? Icons.check_circle : Icons.cancel,
            size: 14,
            color: isAvailable ? AppColors.success : AppColors.error,
          ),
          const SizedBox(width: AppSpacing.xs),
          Text(
            isAvailable ? 'Available' : 'Missing',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: isAvailable ? AppColors.success : AppColors.error,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlternatives(BuildContext context, List<String> alternatives) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.swap_horiz,
              size: 16,
              color: AppColorsTheme.textSecondary(context),
            ),
            const SizedBox(width: AppSpacing.xs),
            Text(
              'Alternatives:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.sm),
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: alternatives
              .map((alternative) => _buildAlternativeChip(context, alternative))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildAlternativeChip(BuildContext context, String alternative) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        borderRadius: BorderRadius.circular(AppSpacing.sm),
        border: Border.all(
          color: AppColorsTheme.border(context),
          width: 1,
        ),
      ),
      child: Text(
        alternative,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
      ),
    );
  }

  Widget _buildNoEquipmentNeeded(BuildContext context) {
    return AppCard(
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppSpacing.sm),
              border: Border.all(
                color: AppColors.success.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.self_improvement,
              color: AppColors.success,
              size: 24,
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'No Equipment Needed',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColorsTheme.textPrimary(context),
                      ),
                ),
                Text(
                  'This exercise uses bodyweight only',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColorsTheme.textSecondary(context),
                      ),
                ),
              ],
            ),
          ),
          _buildAvailabilityChip(context, true),
        ],
      ),
    );
  }

  String _getEquipmentDescription(String equipment) {
    switch (equipment.toLowerCase()) {
      case 'barbell':
        return 'Standard Olympic barbell with weight plates';
      case 'dumbbells':
      case 'dumbbell':
        return 'Adjustable or fixed weight dumbbells';
      case 'bench':
        return 'Flat or adjustable workout bench';
      case 'pull-up bar':
        return 'Mounted or doorway pull-up bar';
      case 'resistance bands':
        return 'Elastic resistance bands with handles';
      case 'kettlebell':
        return 'Cast iron or steel kettlebell';
      case 'medicine ball':
        return 'Weighted medicine ball';
      case 'cable machine':
        return 'Cable pulley system with attachments';
      default:
        return 'Standard gym equipment';
    }
  }
}
