import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// Data model for performance metrics
class PerformanceMetrics {
  final int totalReps;
  final double totalWeight;
  final double totalVolume;
  final Duration duration;
  final int setsCompleted;
  final DateTime timestamp;
  final Map<String, ExercisePerformance> exercisePerformance;

  PerformanceMetrics({
    required this.totalReps,
    required this.totalWeight,
    required this.totalVolume,
    required this.duration,
    required this.setsCompleted,
    required this.timestamp,
    this.exercisePerformance = const {},
  });
}

/// Exercise-specific performance data
class ExercisePerformance {
  final String exerciseId;
  final String exerciseName;
  final int totalReps;
  final double totalWeight;
  final double totalVolume;
  final int setsCompleted;
  final double averageWeight;
  final double maxWeight;
  final List<SetPerformance> sets;

  ExercisePerformance({
    required this.exerciseId,
    required this.exerciseName,
    required this.totalReps,
    required this.totalWeight,
    required this.totalVolume,
    required this.setsCompleted,
    required this.averageWeight,
    required this.maxWeight,
    this.sets = const [],
  });
}

/// Individual set performance data
class SetPerformance {
  final int setNumber;
  final int reps;
  final double weight;
  final double volume;
  final DateTime timestamp;

  SetPerformance({
    required this.setNumber,
    required this.reps,
    required this.weight,
    required this.volume,
    required this.timestamp,
  });
}

/// A widget that compares current session performance with previous sessions
class PerformanceComparison extends StatefulWidget {
  final PerformanceMetrics? currentSession;
  final PerformanceMetrics? previousSession;
  final List<PerformanceMetrics> historicalSessions;
  final String? currentExerciseName;
  final bool showDetailedComparison;
  final VoidCallback? onViewHistory;

  const PerformanceComparison({
    super.key,
    this.currentSession,
    this.previousSession,
    this.historicalSessions = const [],
    this.currentExerciseName,
    this.showDetailedComparison = true,
    this.onViewHistory,
  });

  @override
  State<PerformanceComparison> createState() => _PerformanceComparisonState();
}

class _PerformanceComparisonState extends State<PerformanceComparison>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColorsTheme.surface(context),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColorsTheme.border(context),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: AppSpacing.lg),
          if (widget.showDetailedComparison) ...[
            _buildTabBar(),
            const SizedBox(height: AppSpacing.md),
            SizedBox(
              height: 300,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOverallComparison(),
                  _buildExerciseComparison(),
                ],
              ),
            ),
          ] else
            _buildQuickComparison(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.trending_up,
            color: AppColors.primary,
            size: 24,
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Performance',
                style: AppTypography.heading3.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                ),
              ),
              Text(
                _getComparisonSubtitle(),
                style: AppTypography.caption.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
              ),
            ],
          ),
        ),
        if (widget.onViewHistory != null)
          IconButton(
            onPressed: widget.onViewHistory,
            icon: Icon(
              Icons.history,
              color: AppColorsTheme.textSecondary(context),
            ),
            tooltip: 'View History',
          ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppColorsTheme.border(context).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(6),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: AppColorsTheme.textSecondary(context),
        labelStyle: AppTypography.caption.copyWith(
          fontWeight: AppTypography.semiBold,
        ),
        tabs: const [
          Tab(text: 'Overall'),
          Tab(text: 'Exercise'),
        ],
      ),
    );
  }

  Widget _buildOverallComparison() {
    if (widget.currentSession == null || widget.previousSession == null) {
      return _buildNoDataMessage('No previous session data available');
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          _buildMetricComparison(
            'Total Volume',
            '${widget.currentSession!.totalVolume.toStringAsFixed(0)} lbs',
            '${widget.previousSession!.totalVolume.toStringAsFixed(0)} lbs',
            _calculatePercentageChange(
              widget.currentSession!.totalVolume,
              widget.previousSession!.totalVolume,
            ),
            Icons.fitness_center,
          ),
          const SizedBox(height: AppSpacing.md),
          _buildMetricComparison(
            'Total Reps',
            '${widget.currentSession!.totalReps}',
            '${widget.previousSession!.totalReps}',
            _calculatePercentageChange(
              widget.currentSession!.totalReps.toDouble(),
              widget.previousSession!.totalReps.toDouble(),
            ),
            Icons.repeat,
          ),
          const SizedBox(height: AppSpacing.md),
          _buildMetricComparison(
            'Sets Completed',
            '${widget.currentSession!.setsCompleted}',
            '${widget.previousSession!.setsCompleted}',
            _calculatePercentageChange(
              widget.currentSession!.setsCompleted.toDouble(),
              widget.previousSession!.setsCompleted.toDouble(),
            ),
            Icons.check_circle,
          ),
          const SizedBox(height: AppSpacing.md),
          _buildMetricComparison(
            'Duration',
            _formatDuration(widget.currentSession!.duration),
            _formatDuration(widget.previousSession!.duration),
            _calculatePercentageChange(
              widget.currentSession!.duration.inSeconds.toDouble(),
              widget.previousSession!.duration.inSeconds.toDouble(),
            ),
            Icons.timer,
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseComparison() {
    if (widget.currentSession == null ||
        widget.currentExerciseName == null ||
        widget.currentSession!.exercisePerformance.isEmpty) {
      return _buildNoDataMessage('No exercise data available');
    }

    final currentExercise =
        widget.currentSession!.exercisePerformance[widget.currentExerciseName];
    final previousExercise =
        widget.previousSession?.exercisePerformance[widget.currentExerciseName];

    if (currentExercise == null) {
      return _buildNoDataMessage('No data for current exercise');
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            currentExercise.exerciseName,
            style: AppTypography.body1.copyWith(
              color: AppColorsTheme.textPrimary(context),
              fontWeight: AppTypography.semiBold,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          if (previousExercise != null) ...[
            _buildMetricComparison(
              'Max Weight',
              '${currentExercise.maxWeight.toStringAsFixed(0)} lbs',
              '${previousExercise.maxWeight.toStringAsFixed(0)} lbs',
              _calculatePercentageChange(
                currentExercise.maxWeight,
                previousExercise.maxWeight,
              ),
              Icons.trending_up,
            ),
            const SizedBox(height: AppSpacing.md),
            _buildMetricComparison(
              'Total Volume',
              '${currentExercise.totalVolume.toStringAsFixed(0)} lbs',
              '${previousExercise.totalVolume.toStringAsFixed(0)} lbs',
              _calculatePercentageChange(
                currentExercise.totalVolume,
                previousExercise.totalVolume,
              ),
              Icons.fitness_center,
            ),
            const SizedBox(height: AppSpacing.md),
            _buildMetricComparison(
              'Total Reps',
              '${currentExercise.totalReps}',
              '${previousExercise.totalReps}',
              _calculatePercentageChange(
                currentExercise.totalReps.toDouble(),
                previousExercise.totalReps.toDouble(),
              ),
              Icons.repeat,
            ),
          ] else
            _buildCurrentExerciseStats(currentExercise),
        ],
      ),
    );
  }

  Widget _buildCurrentExerciseStats(ExercisePerformance exercise) {
    return Column(
      children: [
        _buildStatCard(
          'Max Weight',
          '${exercise.maxWeight.toStringAsFixed(0)} lbs',
          Icons.trending_up,
        ),
        const SizedBox(height: AppSpacing.sm),
        _buildStatCard(
          'Total Volume',
          '${exercise.totalVolume.toStringAsFixed(0)} lbs',
          Icons.fitness_center,
        ),
        const SizedBox(height: AppSpacing.sm),
        _buildStatCard(
          'Total Reps',
          '${exercise.totalReps}',
          Icons.repeat,
        ),
        const SizedBox(height: AppSpacing.sm),
        _buildStatCard(
          'Sets Completed',
          '${exercise.setsCompleted}',
          Icons.check_circle,
        ),
      ],
    );
  }

  Widget _buildQuickComparison() {
    if (widget.currentSession == null || widget.previousSession == null) {
      return _buildNoDataMessage('No comparison data available');
    }

    final volumeChange = _calculatePercentageChange(
      widget.currentSession!.totalVolume,
      widget.previousSession!.totalVolume,
    );

    return Row(
      children: [
        Expanded(
          child: _buildQuickMetric(
            'Volume',
            '${widget.currentSession!.totalVolume.toStringAsFixed(0)} lbs',
            volumeChange,
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: _buildQuickMetric(
            'Reps',
            '${widget.currentSession!.totalReps}',
            _calculatePercentageChange(
              widget.currentSession!.totalReps.toDouble(),
              widget.previousSession!.totalReps.toDouble(),
            ),
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: _buildQuickMetric(
            'Sets',
            '${widget.currentSession!.setsCompleted}',
            _calculatePercentageChange(
              widget.currentSession!.setsCompleted.toDouble(),
              widget.previousSession!.setsCompleted.toDouble(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMetricComparison(
    String label,
    String currentValue,
    String previousValue,
    double percentageChange,
    IconData icon,
  ) {
    final isImprovement = percentageChange > 0;
    final changeColor = percentageChange == 0
        ? AppColorsTheme.textSecondary(context)
        : isImprovement
            ? Colors.green
            : Colors.red;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColorsTheme.surface(context),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColorsTheme.border(context),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 18,
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTypography.caption.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
                Row(
                  children: [
                    Text(
                      currentValue,
                      style: AppTypography.body1.copyWith(
                        color: AppColorsTheme.textPrimary(context),
                        fontWeight: AppTypography.semiBold,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Text(
                      '(was $previousValue)',
                      style: AppTypography.caption.copyWith(
                        color: AppColorsTheme.textSecondary(context),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.sm,
              vertical: AppSpacing.xs,
            ),
            decoration: BoxDecoration(
              color: changeColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  percentageChange > 0
                      ? Icons.trending_up
                      : percentageChange < 0
                          ? Icons.trending_down
                          : Icons.trending_flat,
                  color: changeColor,
                  size: 16,
                ),
                const SizedBox(width: AppSpacing.xs),
                Text(
                  '${percentageChange.abs().toStringAsFixed(1)}%',
                  style: AppTypography.caption.copyWith(
                    color: changeColor,
                    fontWeight: AppTypography.semiBold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickMetric(
      String label, String value, double percentageChange) {
    final isImprovement = percentageChange > 0;
    final changeColor = percentageChange == 0
        ? AppColorsTheme.textSecondary(context)
        : isImprovement
            ? Colors.green
            : Colors.red;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColorsTheme.surface(context),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColorsTheme.border(context),
        ),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: AppTypography.caption.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            value,
            style: AppTypography.body1.copyWith(
              color: AppColorsTheme.textPrimary(context),
              fontWeight: AppTypography.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                percentageChange > 0
                    ? Icons.trending_up
                    : percentageChange < 0
                        ? Icons.trending_down
                        : Icons.trending_flat,
                color: changeColor,
                size: 14,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                '${percentageChange.abs().toStringAsFixed(1)}%',
                style: AppTypography.caption.copyWith(
                  color: changeColor,
                  fontWeight: AppTypography.semiBold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColorsTheme.surface(context),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColorsTheme.border(context),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 18,
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTypography.caption.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
                Text(
                  value,
                  style: AppTypography.body1.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontWeight: AppTypography.semiBold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataMessage(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            color: AppColorsTheme.textSecondary(context),
            size: 48,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            message,
            style: AppTypography.body2.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getComparisonSubtitle() {
    if (widget.currentSession == null) {
      return 'No current session data';
    }
    if (widget.previousSession == null) {
      return 'No previous session to compare';
    }
    return 'vs. ${_formatDate(widget.previousSession!.timestamp)}';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.month}/${date.day}';
    }
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  double _calculatePercentageChange(double current, double previous) {
    if (previous == 0) return current > 0 ? 100.0 : 0.0;
    return ((current - previous) / previous) * 100;
  }
}
