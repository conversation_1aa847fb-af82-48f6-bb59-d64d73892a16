import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/design_system.dart';

/// A smart rest timer with customizable intervals and motivational content
class SmartRestTimer extends StatefulWidget {
  final int initialDurationSeconds;
  final String? currentExerciseName;
  final String? nextExerciseName;
  final int currentSet;
  final int totalSets;
  final VoidCallback onComplete;
  final VoidCallback? onSkip;
  final List<String>? motivationalMessages;
  final bool showMotivationalContent;

  const SmartRestTimer({
    super.key,
    this.initialDurationSeconds = 90,
    this.currentExerciseName,
    this.nextExerciseName,
    this.currentSet = 1,
    this.totalSets = 1,
    required this.onComplete,
    this.onSkip,
    this.motivationalMessages,
    this.showMotivationalContent = true,
  });

  @override
  State<SmartRestTimer> createState() => _SmartRestTimerState();
}

class _SmartRestTimerState extends State<SmartRestTimer>
    with TickerProviderStateMixin {
  late Timer _timer;
  late int _remainingSeconds;
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  bool _isPaused = false;
  bool _isCompleted = false;
  int _currentMessageIndex = 0;

  static const List<String> _defaultMotivationalMessages = [
    "You're crushing it! 💪",
    "Every rep counts! 🔥",
    "Stay strong, you've got this! 💯",
    "Push through the burn! 🚀",
    "Your future self will thank you! ⭐",
    "Consistency is key! 🎯",
    "Make every second count! ⚡",
    "You're stronger than you think! 💪",
  ];

  @override
  void initState() {
    super.initState();
    _remainingSeconds = widget.initialDurationSeconds;

    _progressController = AnimationController(
      duration: Duration(seconds: widget.initialDurationSeconds),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.linear,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _startTimer();
    _progressController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _timer.cancel();
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isPaused && !_isCompleted) {
        setState(() {
          if (_remainingSeconds > 0) {
            _remainingSeconds--;

            // Change motivational message every 15 seconds
            if (_remainingSeconds % 15 == 0 && widget.showMotivationalContent) {
              _currentMessageIndex = (_currentMessageIndex + 1) %
                  _getMotivationalMessages().length;
            }
          } else {
            _completeTimer();
          }
        });
      }
    });
  }

  void _completeTimer() {
    if (_isCompleted) return;

    setState(() {
      _isCompleted = true;
    });

    _timer.cancel();
    _progressController.stop();
    _pulseController.stop();

    HapticFeedback.mediumImpact();
    widget.onComplete();
  }

  void _skipTimer() {
    if (_isCompleted) return;

    setState(() {
      _isCompleted = true;
    });

    _timer.cancel();
    _progressController.stop();
    _pulseController.stop();

    HapticFeedback.lightImpact();

    if (widget.onSkip != null) {
      widget.onSkip!();
    } else {
      widget.onComplete();
    }
  }

  void _togglePause() {
    setState(() {
      _isPaused = !_isPaused;
    });

    if (_isPaused) {
      _progressController.stop();
      _pulseController.stop();
    } else {
      _progressController.forward();
      _pulseController.repeat(reverse: true);
    }

    HapticFeedback.selectionClick();
  }

  void _adjustTime(int seconds) {
    setState(() {
      _remainingSeconds =
          (_remainingSeconds + seconds).clamp(0, 300); // Max 5 minutes
    });

    // Adjust progress animation
    final newDuration = Duration(seconds: _remainingSeconds);
    _progressController.reset();
    _progressController.duration = newDuration;

    if (!_isPaused) {
      _progressController.forward();
    }

    HapticFeedback.selectionClick();
  }

  List<String> _getMotivationalMessages() {
    return widget.motivationalMessages ?? _defaultMotivationalMessages;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColorsTheme.surface(context),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColorsTheme.border(context),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          const SizedBox(height: AppSpacing.xl),
          _buildTimerDisplay(),
          const SizedBox(height: AppSpacing.xl),
          _buildTimeControls(),
          const SizedBox(height: AppSpacing.lg),
          _buildActionButtons(),
          if (widget.showMotivationalContent) ...[
            const SizedBox(height: AppSpacing.lg),
            _buildMotivationalContent(),
          ],
          if (widget.nextExerciseName != null) ...[
            const SizedBox(height: AppSpacing.lg),
            _buildNextExercisePreview(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _isPaused ? 1.0 : _pulseAnimation.value,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _isPaused ? Colors.orange : AppColors.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _isPaused ? Icons.pause : Icons.timer,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            );
          },
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Rest Timer',
                style: AppTypography.heading3.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                ),
              ),
              Text(
                _isPaused ? 'Paused' : 'Active',
                style: AppTypography.caption.copyWith(
                  color: _isPaused ? Colors.orange : AppColors.primary,
                  fontWeight: AppTypography.semiBold,
                ),
              ),
            ],
          ),
        ),
        if (widget.currentExerciseName != null)
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Set ${widget.currentSet}',
                style: AppTypography.body2.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                  fontWeight: AppTypography.semiBold,
                ),
              ),
              Text(
                'of ${widget.totalSets}',
                style: AppTypography.caption.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildTimerDisplay() {
    final minutes = _remainingSeconds ~/ 60;
    final seconds = _remainingSeconds % 60;
    final progress = 1.0 - (_remainingSeconds / widget.initialDurationSeconds);

    return SizedBox(
      width: 200,
      height: 200,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Circular progress indicator
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return CustomPaint(
                size: const Size(200, 200),
                painter: CircularProgressPainter(
                  progress: progress,
                  isPaused: _isPaused,
                  isCompleted: _isCompleted,
                ),
              );
            },
          ),
          // Timer text
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}',
                style: AppTypography.display1.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                  fontWeight: AppTypography.bold,
                  fontSize: 48,
                ),
              ),
              Text(
                'remaining',
                style: AppTypography.caption.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimeControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildTimeButton('-30s', -30),
        _buildTimeButton('-15s', -15),
        _buildPauseButton(),
        _buildTimeButton('+15s', 15),
        _buildTimeButton('+30s', 30),
      ],
    );
  }

  Widget _buildTimeButton(String label, int seconds) {
    return GestureDetector(
      onTap: () => _adjustTime(seconds),
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: AppColorsTheme.surface(context),
          shape: BoxShape.circle,
          border: Border.all(
            color: AppColorsTheme.border(context),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            label,
            style: AppTypography.caption.copyWith(
              color: AppColorsTheme.textPrimary(context),
              fontWeight: AppTypography.semiBold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPauseButton() {
    return GestureDetector(
      onTap: _togglePause,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: _isPaused ? Colors.orange : AppColors.primary,
          shape: BoxShape.circle,
        ),
        child: Icon(
          _isPaused ? Icons.play_arrow : Icons.pause,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _skipTimer,
            icon: const Icon(Icons.skip_next, size: 20),
            label: const Text(
              'Skip Rest',
              style: TextStyle(fontWeight: AppTypography.semiBold),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: AppBorderRadius.buttonRadius,
              ),
              padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
            ),
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _adjustTime(30),
            icon: const Icon(Icons.add, size: 20),
            label: const Text(
              'Add 30s',
              style: TextStyle(fontWeight: AppTypography.semiBold),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: AppBorderRadius.buttonRadius,
              ),
              padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMotivationalContent() {
    final messages = _getMotivationalMessages();
    if (messages.isEmpty) return const SizedBox.shrink();

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 500),
      child: Container(
        key: ValueKey(_currentMessageIndex),
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: AppBorderRadius.cardRadius,
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.psychology,
                color: Colors.white,
                size: 18,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Text(
                messages[_currentMessageIndex],
                style: AppTypography.body2.copyWith(
                  color: AppColors.primary,
                  fontWeight: AppTypography.semiBold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNextExercisePreview() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColorsTheme.surface(context),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColorsTheme.border(context),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColorsTheme.border(context),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.fitness_center,
              color: AppColorsTheme.textSecondary(context),
              size: 20,
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Next Exercise',
                  style: AppTypography.caption.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
                Text(
                  widget.nextExerciseName!,
                  style: AppTypography.body2.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontWeight: AppTypography.semiBold,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: AppColors.primary,
            size: 16,
          ),
        ],
      ),
    );
  }
}

/// Custom painter for circular progress indicator
class CircularProgressPainter extends CustomPainter {
  final double progress;
  final bool isPaused;
  final bool isCompleted;

  CircularProgressPainter({
    required this.progress,
    required this.isPaused,
    required this.isCompleted,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 10;
    final strokeWidth = 8.0;

    final paint = Paint()
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Background circle
    paint.color = Colors.grey.withValues(alpha: 0.2);
    canvas.drawCircle(center, radius, paint);

    // Progress arc
    if (isCompleted) {
      paint.color = Colors.green;
    } else if (isPaused) {
      paint.color = Colors.orange;
    } else {
      paint.color = AppColors.primary;
    }

    final sweepAngle = 2 * pi * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2, // Start from top
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.isPaused != isPaused ||
        oldDelegate.isCompleted != isCompleted;
  }
}
