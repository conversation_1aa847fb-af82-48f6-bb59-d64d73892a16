import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import '../models/exercise.dart';

/// A widget that displays step-by-step exercise instructions with form tips
class ExerciseInstructionCard extends StatefulWidget {
  final Exercise exercise;
  final bool showFormTips;
  final bool isExpanded;

  const ExerciseInstructionCard({
    super.key,
    required this.exercise,
    this.showFormTips = true,
    this.isExpanded = false,
  });

  @override
  State<ExerciseInstructionCard> createState() =>
      _ExerciseInstructionCardState();
}

class _ExerciseInstructionCardState extends State<ExerciseInstructionCard> {
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.isExpanded;
  }

  List<String> get _instructionSteps {
    if (widget.exercise.instructions == null ||
        widget.exercise.instructions!.isEmpty) {
      return [];
    }

    // Split instructions by common delimiters
    return widget.exercise.instructions!
        .split(RegExp(r'\d+\.\s*|\n\s*-\s*|\n\s*•\s*|\n\n'))
        .where((step) => step.trim().isNotEmpty)
        .map((step) => step.trim())
        .toList();
  }

  List<String> get _formTips {
    // Generate form tips based on exercise type and muscle groups
    List<String> tips = [];

    if (widget.exercise.primaryMuscle != null) {
      switch (widget.exercise.primaryMuscle!.toLowerCase()) {
        case 'chest':
          tips.addAll([
            'Keep your shoulder blades pulled back and down',
            'Maintain a slight arch in your lower back',
            'Control the weight on both the lifting and lowering phases',
          ]);
          break;
        case 'back':
          tips.addAll([
            'Focus on pulling with your back muscles, not your arms',
            'Keep your core engaged throughout the movement',
            'Squeeze your shoulder blades together at the top',
          ]);
          break;
        case 'shoulders':
          tips.addAll([
            'Keep your core tight to prevent arching your back',
            'Don\'t lift the weight above your head if you feel shoulder pain',
            'Control the movement - avoid using momentum',
          ]);
          break;
        case 'legs':
        case 'quadriceps':
        case 'hamstrings':
        case 'glutes':
          tips.addAll([
            'Keep your knees aligned with your toes',
            'Maintain proper posture throughout the movement',
            'Don\'t let your knees cave inward',
          ]);
          break;
        case 'arms':
        case 'biceps':
        case 'triceps':
          tips.addAll([
            'Keep your elbows stable and close to your body',
            'Focus on the muscle you\'re targeting',
            'Use a full range of motion',
          ]);
          break;
        default:
          tips.addAll([
            'Maintain proper form throughout the exercise',
            'Breathe consistently during the movement',
            'Start with lighter weight to master the technique',
          ]);
      }
    }

    // Add general safety tips
    tips.addAll([
      'Stop immediately if you feel sharp pain',
      'Warm up properly before starting',
      'Stay hydrated throughout your workout',
    ]);

    return tips.take(4).toList(); // Limit to 4 tips
  }

  @override
  Widget build(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context),

          const SizedBox(height: AppSpacing.md),

          // Exercise description
          if (widget.exercise.description != null &&
              widget.exercise.description!.isNotEmpty)
            _buildDescription(context),

          // Instructions
          if (_instructionSteps.isNotEmpty) _buildInstructions(context),

          // Form tips
          if (widget.showFormTips && _isExpanded) _buildFormTips(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        // Exercise info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Instructions',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColorsTheme.textPrimary(context),
                    ),
              ),
              const SizedBox(height: AppSpacing.xs),
              Row(
                children: [
                  if (widget.exercise.primaryMuscle != null) ...[
                    _buildInfoChip(
                      context,
                      widget.exercise.primaryMuscle!,
                      Icons.fitness_center,
                      AppColors.primary,
                    ),
                    const SizedBox(width: AppSpacing.sm),
                  ],
                  if (widget.exercise.category != null)
                    _buildInfoChip(
                      context,
                      widget.exercise.category!,
                      Icons.category_outlined,
                      AppColors.success,
                    ),
                ],
              ),
            ],
          ),
        ),

        // Expand/collapse button
        IconButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          icon: Icon(
            _isExpanded ? Icons.expand_less : Icons.expand_more,
            color: AppColorsTheme.textSecondary(context),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoChip(
      BuildContext context, String label, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSpacing.md),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: AppSpacing.xs),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            color: AppColorsTheme.surfaceVariant(context),
            borderRadius: BorderRadius.circular(AppSpacing.sm),
            border: Border.all(
              color: AppColorsTheme.borderLight(context),
              width: 1,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.info_outline,
                size: 20,
                color: AppColors.primary,
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Text(
                  widget.exercise.description!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColorsTheme.textPrimary(context),
                        height: 1.5,
                      ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.md),
      ],
    );
  }

  Widget _buildInstructions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Step-by-Step Guide',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColorsTheme.textPrimary(context),
              ),
        ),
        const SizedBox(height: AppSpacing.md),

        // Show first 2 steps or all if expanded
        ...(_isExpanded
                ? _instructionSteps
                : _instructionSteps.take(2).toList())
            .asMap()
            .entries
            .map((entry) =>
                _buildInstructionStep(context, entry.key + 1, entry.value)),

        // Show more button if there are more steps
        if (!_isExpanded && _instructionSteps.length > 2)
          _buildShowMoreButton(context),
      ],
    );
  }

  Widget _buildInstructionStep(
      BuildContext context, int stepNumber, String instruction) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.md),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Step number
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(14),
            ),
            child: Center(
              child: Text(
                stepNumber.toString(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ),
          ),

          const SizedBox(width: AppSpacing.md),

          // Instruction text
          Expanded(
            child: Text(
              instruction,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    height: 1.5,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShowMoreButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isExpanded = true;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppSpacing.sm),
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Show ${_instructionSteps.length - 2} more steps',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
            ),
            const SizedBox(width: AppSpacing.xs),
            Icon(
              Icons.expand_more,
              size: 18,
              color: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormTips(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: AppSpacing.lg),
        Text(
          'Form Tips',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColorsTheme.textPrimary(context),
              ),
        ),
        const SizedBox(height: AppSpacing.md),
        ..._formTips.map((tip) => _buildFormTip(context, tip)),
      ],
    );
  }

  Widget _buildFormTip(BuildContext context, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: AppColors.success,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Text(
              tip,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                    height: 1.4,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
