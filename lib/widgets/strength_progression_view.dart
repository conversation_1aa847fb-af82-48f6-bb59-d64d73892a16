import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../design_system/design_system.dart';
import '../models/session_analytics.dart';

class StrengthProgressionView extends StatefulWidget {
  final List<SessionAnalytics> sessionData;
  final String exerciseId;
  final String exerciseName;
  final Duration timeRange;

  const StrengthProgressionView({
    super.key,
    required this.sessionData,
    required this.exerciseId,
    required this.exerciseName,
    this.timeRange = const Duration(days: 90),
  });

  @override
  State<StrengthProgressionView> createState() =>
      _StrengthProgressionViewState();
}

class _StrengthProgressionViewState extends State<StrengthProgressionView> {
  StrengthMetric _selectedMetric = StrengthMetric.oneRepMax;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: AppSpacing.md),
            _buildMetricSelector(),
            const SizedBox(height: AppSpacing.md),
            SizedBox(
              height: 250,
              child: _buildProgressionChart(),
            ),
            const SizedBox(height: AppSpacing.md),
            _buildProgressionStats(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Strength Progression',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                widget.exerciseName,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
            ],
          ),
        ),
        _buildTimeRangeChip(),
      ],
    );
  }

  Widget _buildTimeRangeChip() {
    String timeRangeText;
    if (widget.timeRange.inDays >= 365) {
      timeRangeText = '${(widget.timeRange.inDays / 365).round()}Y';
    } else if (widget.timeRange.inDays >= 30) {
      timeRangeText = '${(widget.timeRange.inDays / 30).round()}M';
    } else {
      timeRangeText = '${widget.timeRange.inDays}D';
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      child: Text(
        timeRangeText,
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  Widget _buildMetricSelector() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: StrengthMetric.values.map((metric) {
          final isSelected = _selectedMetric == metric;
          return Padding(
            padding: const EdgeInsets.only(right: AppSpacing.sm),
            child: FilterChip(
              label: Text(metric.displayName),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedMetric = metric;
                  });
                }
              },
              selectedColor: AppColors.primary.withValues(alpha: 0.2),
              checkmarkColor: AppColors.primary,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildProgressionChart() {
    final exerciseData = _getFilteredExerciseData();

    if (exerciseData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.trending_up_outlined,
              size: 48,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'No progression data',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            Text(
              'Complete more workouts to track strength gains',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
          ],
        ),
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: _getHorizontalInterval(exerciseData),
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: AppColors.border.withValues(alpha: 0.3),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: _getBottomInterval(exerciseData),
              getTitlesWidget: (value, meta) =>
                  _buildBottomTitle(value, exerciseData),
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: _getHorizontalInterval(exerciseData),
              reservedSize: 50,
              getTitlesWidget: (value, meta) => _buildLeftTitle(value),
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: AppColors.border.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        minX: 0,
        maxX: (exerciseData.length - 1).toDouble(),
        minY: _getMinY(exerciseData),
        maxY: _getMaxY(exerciseData),
        lineBarsData: [
          LineChartBarData(
            spots: _getProgressionSpots(exerciseData),
            isCurved: true,
            gradient: LinearGradient(
              colors: [
                AppColors.success.withValues(alpha: 0.8),
                AppColors.success,
              ],
            ),
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: AppColors.success,
                  strokeWidth: 2,
                  strokeColor: Colors.white,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                colors: [
                  AppColors.success.withValues(alpha: 0.1),
                  AppColors.success.withValues(alpha: 0.05),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (touchedSpots) {
              return touchedSpots.map((spot) {
                final sessionIndex = spot.x.toInt();
                if (sessionIndex >= 0 && sessionIndex < exerciseData.length) {
                  final session = exerciseData[sessionIndex];
                  final performance =
                      session.exercisePerformance[widget.exerciseId]!;

                  return LineTooltipItem(
                    '${_selectedMetric.displayName}: ${_getMetricValue(performance)}\n',
                    TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    children: [
                      TextSpan(
                        text: _formatDate(session.sessionDate),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  );
                }
                return null;
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  Widget _buildProgressionStats() {
    final exerciseData = _getFilteredExerciseData();

    if (exerciseData.isEmpty) return const SizedBox.shrink();

    final firstSession = exerciseData.first;
    final lastSession = exerciseData.last;

    final firstPerformance =
        firstSession.exercisePerformance[widget.exerciseId]!;
    final lastPerformance = lastSession.exercisePerformance[widget.exerciseId]!;

    final improvement =
        _calculateImprovement(firstPerformance, lastPerformance);
    final improvementPercentage =
        _calculateImprovementPercentage(firstPerformance, lastPerformance);

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        border: Border.all(color: AppColors.border.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              'Starting ${_selectedMetric.displayName}',
              _getMetricValue(firstPerformance),
              Icons.play_arrow_outlined,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColors.border.withValues(alpha: 0.3),
          ),
          Expanded(
            child: _buildStatItem(
              'Current ${_selectedMetric.displayName}',
              _getMetricValue(lastPerformance),
              Icons.trending_up_outlined,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColors.border.withValues(alpha: 0.3),
          ),
          Expanded(
            child: _buildStatItem(
              'Improvement',
              '+${improvement.toStringAsFixed(1)} (${improvementPercentage.toStringAsFixed(1)}%)',
              Icons.celebration_outlined,
              valueColor: AppColors.success,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.textSecondary,
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          label,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: AppColors.textSecondary,
              ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: valueColor ?? AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  List<SessionAnalytics> _getFilteredExerciseData() {
    final cutoffDate = DateTime.now().subtract(widget.timeRange);
    return widget.sessionData
        .where((session) =>
            session.exercisePerformance.containsKey(widget.exerciseId) &&
            session.sessionDate.isAfter(cutoffDate))
        .toList()
      ..sort((a, b) => a.sessionDate.compareTo(b.sessionDate));
  }

  List<FlSpot> _getProgressionSpots(List<SessionAnalytics> exerciseData) {
    return exerciseData.asMap().entries.map((entry) {
      final index = entry.key;
      final session = entry.value;
      final performance = session.exercisePerformance[widget.exerciseId]!;

      final value = _getMetricValueDouble(performance);
      return FlSpot(index.toDouble(), value);
    }).toList();
  }

  double _getMetricValueDouble(ExercisePerformance performance) {
    switch (_selectedMetric) {
      case StrengthMetric.oneRepMax:
        return _calculateOneRepMax(performance.maxWeight, performance.maxReps);
      case StrengthMetric.maxWeight:
        return performance.maxWeight;
      case StrengthMetric.totalVolume:
        return performance.totalVolume;
      case StrengthMetric.averageWeight:
        return performance.averageWeightPerSet;
    }
  }

  String _getMetricValue(ExercisePerformance performance) {
    switch (_selectedMetric) {
      case StrengthMetric.oneRepMax:
        return '${_calculateOneRepMax(performance.maxWeight, performance.maxReps).toStringAsFixed(1)} lbs';
      case StrengthMetric.maxWeight:
        return '${performance.maxWeight.toStringAsFixed(1)} lbs';
      case StrengthMetric.totalVolume:
        return '${performance.totalVolume.toStringAsFixed(0)} lbs';
      case StrengthMetric.averageWeight:
        return '${performance.averageWeightPerSet.toStringAsFixed(1)} lbs';
    }
  }

  double _calculateOneRepMax(double weight, int reps) {
    if (reps <= 0 || weight <= 0) return 0;
    // Using Brzycki formula: 1RM = weight / (1.0278 - 0.0278 * reps)
    return weight / (1.0278 - 0.0278 * reps);
  }

  double _calculateImprovement(
      ExercisePerformance first, ExercisePerformance last) {
    return _getMetricValueDouble(last) - _getMetricValueDouble(first);
  }

  double _calculateImprovementPercentage(
      ExercisePerformance first, ExercisePerformance last) {
    final firstValue = _getMetricValueDouble(first);
    if (firstValue == 0) return 0;

    final improvement = _calculateImprovement(first, last);
    return (improvement / firstValue) * 100;
  }

  double _getMinY(List<SessionAnalytics> exerciseData) {
    final spots = _getProgressionSpots(exerciseData);
    if (spots.isEmpty) return 0;

    final minValue =
        spots.map((spot) => spot.y).reduce((a, b) => a < b ? a : b);
    return (minValue * 0.9).floorToDouble();
  }

  double _getMaxY(List<SessionAnalytics> exerciseData) {
    final spots = _getProgressionSpots(exerciseData);
    if (spots.isEmpty) return 100;

    final maxValue =
        spots.map((spot) => spot.y).reduce((a, b) => a > b ? a : b);
    return (maxValue * 1.1).ceilToDouble();
  }

  double _getHorizontalInterval(List<SessionAnalytics> exerciseData) {
    if (exerciseData.isEmpty) return 10;

    final range = _getMaxY(exerciseData) - _getMinY(exerciseData);
    return (range / 5).ceilToDouble();
  }

  double _getBottomInterval(List<SessionAnalytics> exerciseData) {
    if (exerciseData.length <= 5) return 1;
    return (exerciseData.length / 5).ceilToDouble();
  }

  Widget _buildBottomTitle(double value, List<SessionAnalytics> exerciseData) {
    final index = value.toInt();
    if (index >= 0 && index < exerciseData.length) {
      final session = exerciseData[index];
      return Padding(
        padding: const EdgeInsets.only(top: 4),
        child: Text(
          _formatDateShort(session.sessionDate),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
                fontSize: 10,
              ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildLeftTitle(double value) {
    return Text(
      value.toInt().toString(),
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
            fontSize: 10,
          ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }

  String _formatDateShort(DateTime date) {
    return '${date.month}/${date.day}';
  }
}

enum StrengthMetric {
  oneRepMax,
  maxWeight,
  totalVolume,
  averageWeight;

  String get displayName {
    switch (this) {
      case StrengthMetric.oneRepMax:
        return '1RM';
      case StrengthMetric.maxWeight:
        return 'Max Weight';
      case StrengthMetric.totalVolume:
        return 'Total Volume';
      case StrengthMetric.averageWeight:
        return 'Avg Weight';
    }
  }
}
