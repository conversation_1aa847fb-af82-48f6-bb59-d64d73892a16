import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import '../models/workout.dart';

/// A widget that tracks live workout session progress with real-time updates
class LiveSessionTracker extends StatefulWidget {
  final Workout workout;
  final int currentExerciseIndex;
  final int currentSet;
  final int elapsedSeconds;
  final VoidCallback? onPause;
  final VoidCallback? onResume;
  final bool isPaused;

  const LiveSessionTracker({
    super.key,
    required this.workout,
    required this.currentExerciseIndex,
    required this.currentSet,
    required this.elapsedSeconds,
    this.onPause,
    this.onResume,
    this.isPaused = false,
  });

  @override
  State<LiveSessionTracker> createState() => _LiveSessionTrackerState();
}

class _LiveSessionTrackerState extends State<LiveSessionTracker>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    if (!widget.isPaused) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(LiveSessionTracker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isPaused != oldWidget.isPaused) {
      if (widget.isPaused) {
        _pulseController.stop();
      } else {
        _pulseController.repeat(reverse: true);
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColorsTheme.surface(context),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColorsTheme.border(context),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: AppSpacing.lg),
          _buildProgressSection(),
          const SizedBox(height: AppSpacing.lg),
          _buildCurrentExerciseInfo(),
          const SizedBox(height: AppSpacing.md),
          _buildSessionControls(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: widget.isPaused ? 1.0 : _pulseAnimation.value,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: widget.isPaused ? Colors.orange : AppColors.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  widget.isPaused ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            );
          },
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Live Session',
                style: AppTypography.heading3.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                ),
              ),
              Text(
                widget.isPaused ? 'Paused' : 'In Progress',
                style: AppTypography.caption.copyWith(
                  color: widget.isPaused ? Colors.orange : AppColors.primary,
                  fontWeight: AppTypography.semiBold,
                ),
              ),
            ],
          ),
        ),
        _buildElapsedTime(),
      ],
    );
  }

  Widget _buildElapsedTime() {
    final elapsedTime = _formatElapsedTime(widget.elapsedSeconds);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          elapsedTime,
          style: AppTypography.heading2.copyWith(
            color: AppColorsTheme.textPrimary(context),
            fontWeight: AppTypography.bold,
          ),
        ),
        Text(
          'Elapsed',
          style: AppTypography.caption.copyWith(
            color: AppColorsTheme.textSecondary(context),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressSection() {
    final progress = _calculateProgress();
    final completedSets = _calculateCompletedSets();
    final totalSets = _calculateTotalSets();

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${(progress * 100).round()}%',
                  style: AppTypography.heading2.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontWeight: AppTypography.bold,
                  ),
                ),
                Text(
                  'Complete',
                  style: AppTypography.caption.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '$completedSets / $totalSets',
                  style: AppTypography.heading3.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontWeight: AppTypography.semiBold,
                  ),
                ),
                Text(
                  'Sets',
                  style: AppTypography.caption.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.md),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: AppColorsTheme.border(context),
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          minHeight: 6,
          borderRadius: BorderRadius.circular(3),
        ),
      ],
    );
  }

  Widget _buildCurrentExerciseInfo() {
    if (widget.currentExerciseIndex >= widget.workout.exercises.length) {
      return const SizedBox.shrink();
    }

    final currentExercise =
        widget.workout.exercises[widget.currentExerciseIndex];
    final totalSets = currentExercise.sets;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.fitness_center,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                'Current Exercise',
                style: AppTypography.caption.copyWith(
                  color: AppColors.primary,
                  fontWeight: AppTypography.semiBold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.xs),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  currentExercise.exercise.name,
                  style: AppTypography.body1.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontWeight: AppTypography.semiBold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.xs,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Set ${widget.currentSet} / $totalSets',
                  style: AppTypography.caption.copyWith(
                    color: Colors.white,
                    fontWeight: AppTypography.semiBold,
                  ),
                ),
              ),
            ],
          ),
          if (currentExercise.exercise.primaryMuscle != null) ...[
            const SizedBox(height: AppSpacing.xs),
            Row(
              children: [
                Icon(
                  Icons.my_location,
                  color: AppColorsTheme.textSecondary(context),
                  size: 16,
                ),
                const SizedBox(width: AppSpacing.xs),
                Text(
                  currentExercise.exercise.primaryMuscle!,
                  style: AppTypography.caption.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSessionControls() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: widget.isPaused ? widget.onResume : widget.onPause,
            icon: Icon(
              widget.isPaused ? Icons.play_arrow : Icons.pause,
              size: 18,
            ),
            label: Text(
              widget.isPaused ? 'Resume' : 'Pause',
              style: const TextStyle(fontWeight: AppTypography.semiBold),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor:
                  widget.isPaused ? AppColors.primary : Colors.orange,
              side: BorderSide(
                color: widget.isPaused ? AppColors.primary : Colors.orange,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: AppBorderRadius.buttonRadius,
              ),
              padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
            ),
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Container(
          decoration: BoxDecoration(
            color: AppColorsTheme.surface(context),
            borderRadius: AppBorderRadius.buttonRadius,
            border: Border.all(
              color: AppColorsTheme.border(context),
            ),
          ),
          child: IconButton(
            onPressed: () => _showWorkoutStats(context),
            icon: Icon(
              Icons.analytics_outlined,
              color: AppColorsTheme.textSecondary(context),
            ),
            tooltip: 'View Stats',
          ),
        ),
      ],
    );
  }

  String _formatElapsedTime(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }

  double _calculateProgress() {
    final totalSets = _calculateTotalSets();
    final completedSets = _calculateCompletedSets();

    if (totalSets == 0) return 0.0;
    return completedSets / totalSets;
  }

  int _calculateTotalSets() {
    return widget.workout.exercises.fold<int>(
      0,
      (sum, exercise) => sum + exercise.sets,
    );
  }

  int _calculateCompletedSets() {
    int completedSets = 0;

    for (int i = 0; i < widget.workout.exercises.length; i++) {
      final exercise = widget.workout.exercises[i];

      if (i < widget.currentExerciseIndex) {
        // All sets for previous exercises are completed
        completedSets += exercise.sets;
      } else if (i == widget.currentExerciseIndex) {
        // Current exercise: count completed sets (current set - 1)
        completedSets += (widget.currentSet - 1);
      }
      // Future exercises contribute 0 completed sets
    }

    return completedSets;
  }

  void _showWorkoutStats(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _WorkoutStatsModal(
        workout: widget.workout,
        currentExerciseIndex: widget.currentExerciseIndex,
        currentSet: widget.currentSet,
        elapsedSeconds: widget.elapsedSeconds,
      ),
    );
  }
}

class _WorkoutStatsModal extends StatelessWidget {
  final Workout workout;
  final int currentExerciseIndex;
  final int currentSet;
  final int elapsedSeconds;

  const _WorkoutStatsModal({
    required this.workout,
    required this.currentExerciseIndex,
    required this.currentSet,
    required this.elapsedSeconds,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColorsTheme.surface(context),
        borderRadius: AppBorderRadius.cardRadius,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(AppSpacing.lg),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColorsTheme.border(context),
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppColors.primary,
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  'Session Stats',
                  style: AppTypography.heading3.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontWeight: AppTypography.semiBold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Column(
              children: [
                _buildStatRow(
                  context,
                  'Workout',
                  workout.name,
                  Icons.fitness_center,
                ),
                const SizedBox(height: AppSpacing.md),
                _buildStatRow(
                  context,
                  'Exercises',
                  '${currentExerciseIndex + 1} / ${workout.exercises.length}',
                  Icons.list,
                ),
                const SizedBox(height: AppSpacing.md),
                _buildStatRow(
                  context,
                  'Current Set',
                  '$currentSet / ${workout.exercises.isNotEmpty ? workout.exercises[currentExerciseIndex].sets : 0}',
                  Icons.repeat,
                ),
                const SizedBox(height: AppSpacing.md),
                _buildStatRow(
                  context,
                  'Duration',
                  _formatElapsedTime(elapsedSeconds),
                  Icons.timer,
                ),
                const SizedBox(height: AppSpacing.md),
                _buildStatRow(
                  context,
                  'Estimated Calories',
                  '${(elapsedSeconds / 60 * 8).round()} cal',
                  Icons.local_fire_department,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(
      BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTypography.caption.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
              ),
              Text(
                value,
                style: AppTypography.body1.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                  fontWeight: AppTypography.semiBold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatElapsedTime(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m ${remainingSeconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${remainingSeconds}s';
    } else {
      return '${remainingSeconds}s';
    }
  }
}
