import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/workout.dart';
import '../design_system/design_system.dart';

class EnhancedWorkoutCard extends StatefulWidget {
  final Workout workout;
  final VoidCallback? onTap;
  final VoidCallback? onStart;
  final bool showProgress;
  final double? progressValue;
  final String? lastCompletedText;

  const EnhancedWorkoutCard({
    super.key,
    required this.workout,
    this.onTap,
    this.onStart,
    this.showProgress = false,
    this.progressValue,
    this.lastCompletedText,
  });

  @override
  State<EnhancedWorkoutCard> createState() => _EnhancedWorkoutCardState();
}

class _EnhancedWorkoutCardState extends State<EnhancedWorkoutCard> {
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _loadFavoriteStatus();
  }

  Future<void> _loadFavoriteStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final favorites = prefs.getStringList('favorite_workouts') ?? [];
    setState(() {
      _isFavorite = favorites.contains(widget.workout.id);
    });
  }

  Future<void> _toggleFavorite() async {
    final prefs = await SharedPreferences.getInstance();
    final favorites = prefs.getStringList('favorite_workouts') ?? [];

    if (_isFavorite) {
      favorites.remove(widget.workout.id);
    } else {
      favorites.add(widget.workout.id);
    }

    await prefs.setStringList('favorite_workouts', favorites);
    setState(() {
      _isFavorite = !_isFavorite;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: const EdgeInsets.only(bottom: AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with thumbnail, title, and favorite button
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Workout thumbnail
              _buildThumbnail(context),
              const SizedBox(width: AppSpacing.md),

              // Title and basic info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.workout.name,
                            style: AppTypography.heading3.copyWith(
                              color: AppColorsTheme.textPrimary(context),
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        // Favorite button
                        IconButton(
                          onPressed: _toggleFavorite,
                          icon: Icon(
                            _isFavorite
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color: _isFavorite
                                ? Colors.red
                                : AppColorsTheme.textSecondary(context),
                            size: 20,
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                          padding: EdgeInsets.zero,
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      widget.workout.workoutSummary,
                      style: AppTypography.body2.copyWith(
                        color: AppColorsTheme.textSecondary(context),
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    _buildWorkoutStats(context),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppSpacing.md),

          // Progress indicator (if applicable)
          if (widget.showProgress && widget.progressValue != null) ...[
            _buildProgressIndicator(context),
            const SizedBox(height: AppSpacing.md),
          ],

          // Last completed info
          if (widget.lastCompletedText != null) ...[
            _buildLastCompletedInfo(context),
            const SizedBox(height: AppSpacing.md),
          ],

          // AI Description (if available)
          if (widget.workout.aiDescription != null &&
              widget.workout.aiDescription!.isNotEmpty) ...[
            _buildAIDescription(context),
            const SizedBox(height: AppSpacing.md),
          ],

          // Exercise Preview
          if (widget.workout.exercises.isNotEmpty) ...[
            _buildExercisePreview(context),
            const SizedBox(height: AppSpacing.md),
          ],

          // Muscle Groups and Equipment Tags
          _buildInfoTags(context),

          const SizedBox(height: AppSpacing.lg),

          // Action Buttons
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildThumbnail(BuildContext context) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Stack(
        children: [
          // Workout type icon
          Center(
            child: Icon(
              _getWorkoutIcon(),
              size: 32,
              color: AppColors.primary,
            ),
          ),

          // Status badge
          Positioned(
            top: 4,
            right: 4,
            child: _buildStatusBadge(context),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    Color badgeColor;
    IconData badgeIcon;

    switch (widget.workout.status) {
      case 'Active':
        badgeColor = AppColors.success;
        badgeIcon = Icons.play_circle_filled;
        break;
      case 'Completed':
        badgeColor = AppColors.primary;
        badgeIcon = Icons.check_circle;
        break;
      default:
        badgeColor = AppColorsTheme.textSecondary(context);
        badgeIcon = Icons.circle_outlined;
    }

    return Container(
      width: 16,
      height: 16,
      decoration: BoxDecoration(
        color: badgeColor,
        shape: BoxShape.circle,
      ),
      child: Icon(
        badgeIcon,
        size: 12,
        color: Colors.white,
      ),
    );
  }

  Widget _buildWorkoutStats(BuildContext context) {
    return Row(
      children: [
        _buildStatChip(
          context,
          Icons.timer,
          _getEstimatedDuration(),
          AppColors.primary,
        ),
        const SizedBox(width: AppSpacing.sm),
        _buildStatChip(
          context,
          Icons.local_fire_department,
          _getEstimatedCalories(),
          AppColors.warning,
        ),
        const SizedBox(width: AppSpacing.sm),
        _buildStatChip(
          context,
          Icons.trending_up,
          _getDifficultyLevel(),
          AppColors.success,
        ),
      ],
    );
  }

  Widget _buildStatChip(
      BuildContext context, IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: AppBorderRadius.chipRadius,
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: AppSpacing.xs),
          Text(
            text,
            style: AppTypography.small.copyWith(
              color: color,
              fontWeight: AppTypography.medium,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.trending_up,
              size: 16,
              color: AppColors.success,
            ),
            const SizedBox(width: AppSpacing.xs),
            Text(
              'Progress',
              style: AppTypography.buttonSmall.copyWith(
                color: AppColors.success,
                fontWeight: AppTypography.semiBold,
              ),
            ),
            const Spacer(),
            Text(
              '${(widget.progressValue! * 100).toInt()}%',
              style: AppTypography.small.copyWith(
                color: AppColors.success,
                fontWeight: AppTypography.semiBold,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.xs),
        LinearProgressIndicator(
          value: widget.progressValue,
          backgroundColor: AppColors.success.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.success),
          minHeight: 4,
        ),
      ],
    );
  }

  Widget _buildLastCompletedInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: AppBorderRadius.chipRadius,
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.history,
            size: 16,
            color: AppColors.primary,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              widget.lastCompletedText!,
              style: AppTypography.small.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAIDescription(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: AppBorderRadius.chipRadius,
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.auto_awesome,
            size: 16,
            color: AppColors.primary,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              widget.workout.aiDescription!.length > 120
                  ? '${widget.workout.aiDescription!.substring(0, 120)}...'
                  : widget.workout.aiDescription!,
              style: AppTypography.small.copyWith(
                color: AppColors.primary,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExercisePreview(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Exercises Preview',
          style: AppTypography.buttonSmall.copyWith(
            color: AppColorsTheme.textSecondary(context),
            fontWeight: AppTypography.semiBold,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        ...widget.workout.exercises.take(3).map((exercise) => Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.xs),
              child: Row(
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Text(
                      '${exercise.exercise.name} • ${exercise.formattedReps}',
                      style: AppTypography.small.copyWith(
                        color: AppColorsTheme.textSecondary(context),
                      ),
                    ),
                  ),
                  if (exercise.exercise.equipment != null &&
                      exercise.exercise.equipment != 'None')
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.xs,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.darkBorder
                            : AppColors.grey200,
                        borderRadius: AppBorderRadius.chipRadius,
                      ),
                      child: Text(
                        exercise.exercise.equipment!,
                        style: AppTypography.small.copyWith(
                          color: AppColorsTheme.textSecondary(context),
                          fontSize: 10,
                        ),
                      ),
                    ),
                ],
              ),
            )),
        if (widget.workout.exercises.length > 3)
          Padding(
            padding: const EdgeInsets.only(top: AppSpacing.xs),
            child: Text(
              '+${widget.workout.exercises.length - 3} more exercises',
              style: AppTypography.small.copyWith(
                color: AppColorsTheme.textSecondary(context),
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildInfoTags(BuildContext context) {
    return Column(
      children: [
        if (widget.workout.primaryMuscles.isNotEmpty) ...[
          _buildInfoChips(
            context,
            'Target Muscles',
            widget.workout.primaryMuscles.take(4).toList(),
            Icons.fitness_center,
            AppColors.success,
          ),
        ],
        if (widget.workout.equipmentNeeded.isNotEmpty) ...[
          if (widget.workout.primaryMuscles.isNotEmpty)
            const SizedBox(height: AppSpacing.md),
          _buildInfoChips(
            context,
            'Equipment',
            widget.workout.equipmentNeeded.take(3).toList(),
            Icons.sports_gymnastics,
            AppColors.warning,
          ),
        ],
      ],
    );
  }

  Widget _buildInfoChips(
    BuildContext context,
    String label,
    List<String> items,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 14, color: color),
            const SizedBox(width: AppSpacing.xs),
            Text(
              label,
              style: AppTypography.small.copyWith(
                fontWeight: AppTypography.semiBold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.xs),
        Wrap(
          spacing: AppSpacing.xs,
          runSpacing: AppSpacing.xs,
          children: items.map((item) {
            final isDark = Theme.of(context).brightness == Brightness.dark;
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xs,
              ),
              decoration: BoxDecoration(
                color: isDark
                    ? color.withValues(alpha: 0.15)
                    : color.withValues(alpha: 0.1),
                borderRadius: AppBorderRadius.chipRadius,
                border: Border.all(
                  color: isDark
                      ? color.withValues(alpha: 0.4)
                      : color.withValues(alpha: 0.2),
                  width: isDark ? 1 : 0.5,
                ),
              ),
              child: Text(
                item,
                style: AppTypography.small.copyWith(
                  color: isDark ? color.withValues(alpha: 0.9) : color,
                  fontWeight: AppTypography.medium,
                  fontSize: 11,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: AppButton(
            text:
                widget.workout.isActive ? 'Continue Workout' : 'Start Workout',
            onPressed: widget.onStart,
            variant: widget.workout.isActive
                ? AppButtonVariant.success
                : AppButtonVariant.primary,
            size: AppButtonSize.medium,
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          flex: 1,
          child: AppButton(
            text: 'Details',
            onPressed: widget.onTap,
            variant: AppButtonVariant.secondary,
            size: AppButtonSize.medium,
          ),
        ),
      ],
    );
  }

  IconData _getWorkoutIcon() {
    final muscles =
        widget.workout.primaryMuscles.map((m) => m.toLowerCase()).toList();
    final hasEquipment = widget.workout.equipmentNeeded.isNotEmpty;

    if (!hasEquipment) {
      return Icons.accessibility_new; // Bodyweight
    } else if (muscles.any((m) => ['chest', 'arms', 'shoulders'].contains(m))) {
      return Icons.fitness_center; // Upper body
    } else if (muscles.any(
        (m) => ['legs', 'glutes', 'quadriceps', 'hamstrings'].contains(m))) {
      return Icons.directions_run; // Lower body
    } else {
      return Icons.sports_gymnastics; // General
    }
  }

  String _getEstimatedDuration() {
    // Simple estimation based on exercises and sets
    final totalSets =
        widget.workout.exercises.fold<int>(0, (sum, ex) => sum + ex.sets);
    final estimatedMinutes = (totalSets * 1.5).round(); // 1.5 minutes per set

    if (estimatedMinutes < 60) {
      return '${estimatedMinutes}m';
    } else {
      final hours = estimatedMinutes ~/ 60;
      final minutes = estimatedMinutes % 60;
      return minutes > 0 ? '${hours}h ${minutes}m' : '${hours}h';
    }
  }

  String _getEstimatedCalories() {
    // Simple estimation based on exercises and intensity
    final totalSets =
        widget.workout.exercises.fold<int>(0, (sum, ex) => sum + ex.sets);
    final estimatedCalories = (totalSets * 8).round(); // ~8 calories per set

    if (estimatedCalories < 1000) {
      return '${estimatedCalories}cal';
    } else {
      return '${(estimatedCalories / 1000).toStringAsFixed(1)}k cal';
    }
  }

  String _getDifficultyLevel() {
    // Simple heuristic based on exercise count and complexity
    final exerciseCount = widget.workout.exercises.length;
    final totalSets =
        widget.workout.exercises.fold<int>(0, (sum, ex) => sum + ex.sets);

    if (exerciseCount <= 3 && totalSets <= 9) {
      return 'Beginner';
    } else if (exerciseCount <= 6 && totalSets <= 18) {
      return 'Intermediate';
    } else {
      return 'Advanced';
    }
  }
}
