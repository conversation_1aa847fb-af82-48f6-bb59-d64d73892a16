import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../design_system/design_system.dart';
import '../models/session_analytics.dart';
import 'personal_record_display.dart';

class ProgressCharts extends StatefulWidget {
  final List<SessionAnalytics> sessionData;
  final String exerciseId;
  final String exerciseName;
  final ProgressChartType chartType;

  const ProgressCharts({
    super.key,
    required this.sessionData,
    required this.exerciseId,
    required this.exerciseName,
    this.chartType = ProgressChartType.weight,
  });

  @override
  State<ProgressCharts> createState() => _ProgressChartsState();
}

class _ProgressChartsState extends State<ProgressCharts> {
  ProgressChartType _selectedChartType = ProgressChartType.weight;

  @override
  void initState() {
    super.initState();
    _selectedChartType = widget.chartType;
  }

  @override
  Widget build(BuildContext context) {
    final personalRecords = _detectPersonalRecords(_getExerciseData());

    return Column(
      children: [
        // Show personal records if any exist
        if (personalRecords.isNotEmpty) ...[
          PersonalRecordDisplay(personalRecords: personalRecords),
          const SizedBox(height: AppSpacing.md),
        ],
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: AppSpacing.md),
                _buildChartTypeSelector(),
                const SizedBox(height: AppSpacing.md),
                SizedBox(
                  height: 300,
                  child: _buildChart(),
                ),
                const SizedBox(height: AppSpacing.sm),
                _buildChartLegend(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.exerciseName,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                'Progress Over Time',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
            ],
          ),
        ),
        _buildStatsCard(),
      ],
    );
  }

  Widget _buildStatsCard() {
    if (widget.sessionData.isEmpty) return const SizedBox.shrink();

    final exerciseData = widget.sessionData
        .where((session) =>
            session.exercisePerformance.containsKey(widget.exerciseId))
        .toList();

    if (exerciseData.isEmpty) return const SizedBox.shrink();

    final latestPerformance =
        exerciseData.last.exercisePerformance[widget.exerciseId]!;
    final firstPerformance =
        exerciseData.first.exercisePerformance[widget.exerciseId]!;

    final weightImprovement =
        latestPerformance.maxWeight - firstPerformance.maxWeight;
    final volumeImprovement =
        latestPerformance.totalVolume - firstPerformance.totalVolume;

    // Check for personal records
    final personalRecords = _detectPersonalRecords(exerciseData);
    final hasNewRecord = personalRecords.isNotEmpty;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: hasNewRecord
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        border: hasNewRecord
            ? Border.all(color: AppColors.success.withValues(alpha: 0.3))
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (hasNewRecord) ...[
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.emoji_events,
                  size: 16,
                  color: AppColors.success,
                ),
                const SizedBox(width: 4),
                Text(
                  'PR!',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 4),
          ],
          Text(
            hasNewRecord ? 'Personal Record' : 'Improvement',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          Text(
            '+${weightImprovement.toStringAsFixed(1)} lbs',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: hasNewRecord ? AppColors.success : AppColors.success,
                  fontWeight: FontWeight.bold,
                ),
          ),
          Text(
            '+${volumeImprovement.toStringAsFixed(0)} vol',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: hasNewRecord ? AppColors.success : AppColors.success,
                ),
          ),
        ],
      ),
    );
  }

  List<PersonalRecord> _detectPersonalRecords(
      List<SessionAnalytics> exerciseData) {
    final records = <PersonalRecord>[];

    if (exerciseData.length < 2) return records;

    // Get the latest session performance
    final latestSession = exerciseData.last;
    final latestPerformance =
        latestSession.exercisePerformance[widget.exerciseId]!;

    // Check if latest weight is a personal record
    final maxWeightBefore = exerciseData
        .take(exerciseData.length - 1)
        .map((s) => s.exercisePerformance[widget.exerciseId]?.maxWeight ?? 0.0)
        .fold<double>(0.0, (max, weight) => weight > max ? weight : max);

    if (latestPerformance.maxWeight > maxWeightBefore) {
      records.add(PersonalRecord(
        exerciseId: widget.exerciseId,
        exerciseName: widget.exerciseName,
        type: PersonalRecordType.maxWeight,
        value: latestPerformance.maxWeight,
        achievedDate: latestSession.sessionDate,
        previousValue: maxWeightBefore,
      ));
    }

    // Check if latest volume is a personal record
    final maxVolumeBefore = exerciseData
        .take(exerciseData.length - 1)
        .map(
            (s) => s.exercisePerformance[widget.exerciseId]?.totalVolume ?? 0.0)
        .fold<double>(0.0, (max, volume) => volume > max ? volume : max);

    if (latestPerformance.totalVolume > maxVolumeBefore) {
      records.add(PersonalRecord(
        exerciseId: widget.exerciseId,
        exerciseName: widget.exerciseName,
        type: PersonalRecordType.maxVolume,
        value: latestPerformance.totalVolume,
        achievedDate: latestSession.sessionDate,
        previousValue: maxVolumeBefore,
      ));
    }

    return records;
  }

  Widget _buildChartTypeSelector() {
    return Row(
      children: [
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: ProgressChartType.values.map((type) {
                final isSelected = _selectedChartType == type;
                return Padding(
                  padding: const EdgeInsets.only(right: AppSpacing.sm),
                  child: FilterChip(
                    label: Text(type.displayName),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedChartType = type;
                        });
                      }
                    },
                    selectedColor: AppColors.primary.withValues(alpha: 0.2),
                    checkmarkColor: AppColors.primary,
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  List<SessionAnalytics> _getExerciseData() {
    return widget.sessionData
        .where((session) =>
            session.exercisePerformance.containsKey(widget.exerciseId))
        .toList();
  }

  Widget _buildChart() {
    final exerciseData = _getExerciseData();

    if (exerciseData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 48,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'No data available',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            Text(
              'Complete more workouts to see progress',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
          ],
        ),
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: _getHorizontalInterval(),
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: AppColors.border.withValues(alpha: 0.3),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: _getBottomInterval(),
              getTitlesWidget: (value, meta) =>
                  _buildBottomTitle(value, exerciseData),
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: _getHorizontalInterval(),
              reservedSize: 50,
              getTitlesWidget: (value, meta) => _buildLeftTitle(value),
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: AppColors.border.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        minX: 0,
        maxX: (exerciseData.length - 1).toDouble(),
        minY: _getMinY(exerciseData),
        maxY: _getMaxY(exerciseData),
        lineBarsData: [
          LineChartBarData(
            spots: _getChartSpots(exerciseData),
            isCurved: true,
            gradient: LinearGradient(
              colors: [
                AppColors.primary.withValues(alpha: 0.8),
                AppColors.primary,
              ],
            ),
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: AppColors.primary,
                  strokeWidth: 2,
                  strokeColor: Colors.white,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withValues(alpha: 0.1),
                  AppColors.primary.withValues(alpha: 0.05),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (touchedSpots) {
              return touchedSpots.map((spot) {
                final sessionIndex = spot.x.toInt();
                if (sessionIndex >= 0 && sessionIndex < exerciseData.length) {
                  final session = exerciseData[sessionIndex];
                  final performance =
                      session.exercisePerformance[widget.exerciseId]!;

                  return LineTooltipItem(
                    '${_getTooltipLabel()}: ${_getTooltipValue(performance)}\n',
                    TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    children: [
                      TextSpan(
                        text: _formatDate(session.sessionDate),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  );
                }
                return null;
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  Widget _buildChartLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 12,
          height: 3,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: AppSpacing.xs),
        Text(
          _selectedChartType.displayName,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
        ),
      ],
    );
  }

  List<FlSpot> _getChartSpots(List<SessionAnalytics> exerciseData) {
    return exerciseData.asMap().entries.map((entry) {
      final index = entry.key;
      final session = entry.value;
      final performance = session.exercisePerformance[widget.exerciseId]!;

      double value;
      switch (_selectedChartType) {
        case ProgressChartType.weight:
          value = performance.maxWeight;
          break;
        case ProgressChartType.reps:
          value = performance.maxReps.toDouble();
          break;
        case ProgressChartType.volume:
          value = performance.totalVolume;
          break;
        case ProgressChartType.sets:
          value = performance.setsCompleted.toDouble();
          break;
      }

      return FlSpot(index.toDouble(), value);
    }).toList();
  }

  double _getMinY(List<SessionAnalytics> exerciseData) {
    final spots = _getChartSpots(exerciseData);
    if (spots.isEmpty) return 0;

    final minValue =
        spots.map((spot) => spot.y).reduce((a, b) => a < b ? a : b);
    return (minValue * 0.9).floorToDouble();
  }

  double _getMaxY(List<SessionAnalytics> exerciseData) {
    final spots = _getChartSpots(exerciseData);
    if (spots.isEmpty) return 100;

    final maxValue =
        spots.map((spot) => spot.y).reduce((a, b) => a > b ? a : b);
    return (maxValue * 1.1).ceilToDouble();
  }

  double _getHorizontalInterval() {
    final exerciseData = widget.sessionData
        .where((session) =>
            session.exercisePerformance.containsKey(widget.exerciseId))
        .toList();

    if (exerciseData.isEmpty) return 10;

    final range = _getMaxY(exerciseData) - _getMinY(exerciseData);
    return (range / 5).ceilToDouble();
  }

  double _getBottomInterval() {
    final exerciseData = widget.sessionData
        .where((session) =>
            session.exercisePerformance.containsKey(widget.exerciseId))
        .toList();

    if (exerciseData.length <= 5) return 1;
    return (exerciseData.length / 5).ceilToDouble();
  }

  Widget _buildBottomTitle(double value, List<SessionAnalytics> exerciseData) {
    final index = value.toInt();
    if (index >= 0 && index < exerciseData.length) {
      final session = exerciseData[index];
      return Padding(
        padding: const EdgeInsets.only(top: 4),
        child: Text(
          _formatDateShort(session.sessionDate),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
                fontSize: 10,
              ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildLeftTitle(double value) {
    return Text(
      _formatYAxisValue(value),
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
            fontSize: 10,
          ),
    );
  }

  String _formatYAxisValue(double value) {
    switch (_selectedChartType) {
      case ProgressChartType.weight:
        return '${value.toInt()}';
      case ProgressChartType.reps:
        return '${value.toInt()}';
      case ProgressChartType.volume:
        return '${(value / 1000).toStringAsFixed(1)}k';
      case ProgressChartType.sets:
        return '${value.toInt()}';
    }
  }

  String _getTooltipLabel() {
    switch (_selectedChartType) {
      case ProgressChartType.weight:
        return 'Max Weight';
      case ProgressChartType.reps:
        return 'Max Reps';
      case ProgressChartType.volume:
        return 'Total Volume';
      case ProgressChartType.sets:
        return 'Sets Completed';
    }
  }

  String _getTooltipValue(ExercisePerformance performance) {
    switch (_selectedChartType) {
      case ProgressChartType.weight:
        return '${performance.maxWeight.toStringAsFixed(1)} lbs';
      case ProgressChartType.reps:
        return '${performance.maxReps} reps';
      case ProgressChartType.volume:
        return '${performance.totalVolume.toStringAsFixed(0)} lbs';
      case ProgressChartType.sets:
        return '${performance.setsCompleted} sets';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }

  String _formatDateShort(DateTime date) {
    return '${date.month}/${date.day}';
  }
}

enum ProgressChartType {
  weight,
  reps,
  volume,
  sets;

  String get displayName {
    switch (this) {
      case ProgressChartType.weight:
        return 'Weight';
      case ProgressChartType.reps:
        return 'Reps';
      case ProgressChartType.volume:
        return 'Volume';
      case ProgressChartType.sets:
        return 'Sets';
    }
  }
}
