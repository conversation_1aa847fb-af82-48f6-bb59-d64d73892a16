import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

class StreakTracker extends StatelessWidget {
  final int currentStreak;
  final int longestStreak;
  final double consistencyScore;
  final List<DateTime> recentWorkoutDates;

  const StreakTracker({
    super.key,
    required this.currentStreak,
    required this.longestStreak,
    required this.consistencyScore,
    this.recentWorkoutDates = const [],
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: AppSpacing.md),
            _buildStreakStats(context),
            const SizedBox(height: AppSpacing.md),
            _buildWeeklyCalendar(context),
            const SizedBox(height: AppSpacing.sm),
            _buildConsistencyBar(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: _getStreakColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          child: Icon(
            _getStreakIcon(),
            color: _getStreakColor(),
            size: 20,
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Workout Streak',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                _getStreakMessage(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
            ],
          ),
        ),
        _buildStreakBadge(context),
      ],
    );
  }

  Widget _buildStreakBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: _getStreakColor(),
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.local_fire_department,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            '$currentStreak',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildStreakStats(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            context,
            'Current Streak',
            '$currentStreak days',
            Icons.local_fire_department,
            _getStreakColor(),
          ),
        ),
        Container(
          width: 1,
          height: 40,
          color: AppColors.border.withValues(alpha: 0.3),
        ),
        Expanded(
          child: _buildStatItem(
            context,
            'Best Streak',
            '$longestStreak days',
            Icons.emoji_events,
            AppColors.warning,
          ),
        ),
        Container(
          width: 1,
          height: 40,
          color: AppColors.border.withValues(alpha: 0.3),
        ),
        Expanded(
          child: _buildStatItem(
            context,
            'Consistency',
            '${consistencyScore.toInt()}%',
            Icons.trending_up,
            AppColors.success,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: color,
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: AppColors.textSecondary,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildWeeklyCalendar(BuildContext context) {
    final today = DateTime.now();
    final startOfWeek = today.subtract(Duration(days: today.weekday - 1));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'This Week',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Row(
          children: List.generate(7, (index) {
            final date = startOfWeek.add(Duration(days: index));
            final hasWorkout = recentWorkoutDates.any((workoutDate) =>
                workoutDate.year == date.year &&
                workoutDate.month == date.month &&
                workoutDate.day == date.day);
            final isToday = date.year == today.year &&
                date.month == today.month &&
                date.day == today.day;

            return Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                child: Column(
                  children: [
                    Text(
                      _getDayName(date.weekday),
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: hasWorkout
                            ? AppColors.success
                            : isToday
                                ? AppColors.primary.withValues(alpha: 0.2)
                                : AppColors.surface,
                        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
                        border: isToday
                            ? Border.all(color: AppColors.primary, width: 2)
                            : Border.all(
                                color: AppColors.border.withValues(alpha: 0.3)),
                      ),
                      child: Center(
                        child: hasWorkout
                            ? Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16,
                              )
                            : Text(
                                '${date.day}',
                                style: Theme.of(context)
                                    .textTheme
                                    .labelSmall
                                    ?.copyWith(
                                      color: isToday
                                          ? AppColors.primary
                                          : AppColors.textSecondary,
                                      fontWeight: isToday
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                    ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildConsistencyBar(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Consistency Score',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            Text(
              '${consistencyScore.toInt()}%',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          height: 6,
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(3),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: consistencyScore / 100,
            child: Container(
              decoration: BoxDecoration(
                color: _getConsistencyColor(),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getStreakColor() {
    if (currentStreak >= 30) return AppColors.success;
    if (currentStreak >= 7) return AppColors.warning;
    if (currentStreak >= 3) return AppColors.primary;
    return AppColors.textSecondary;
  }

  IconData _getStreakIcon() {
    if (currentStreak >= 30) return Icons.emoji_events;
    if (currentStreak >= 7) return Icons.local_fire_department;
    if (currentStreak >= 3) return Icons.trending_up;
    return Icons.fitness_center;
  }

  String _getStreakMessage() {
    if (currentStreak >= 30) return 'Amazing consistency! 🏆';
    if (currentStreak >= 7) return 'Great streak! Keep it up! 🔥';
    if (currentStreak >= 3) return 'Building momentum! 💪';
    if (currentStreak == 0) return 'Ready to start a new streak?';
    return 'Good start! Keep going! 👍';
  }

  Color _getConsistencyColor() {
    if (consistencyScore >= 80) return AppColors.success;
    if (consistencyScore >= 60) return AppColors.warning;
    return AppColors.error;
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'M';
      case 2:
        return 'T';
      case 3:
        return 'W';
      case 4:
        return 'T';
      case 5:
        return 'F';
      case 6:
        return 'S';
      case 7:
        return 'S';
      default:
        return '';
    }
  }
}
