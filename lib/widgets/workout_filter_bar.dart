import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import '../services/workout_filter_service.dart';

class WorkoutFilterBar extends StatefulWidget {
  final WorkoutFilterService filterService;
  final VoidCallback? onFiltersChanged;

  const WorkoutFilterBar({
    super.key,
    required this.filterService,
    this.onFiltersChanged,
  });

  @override
  State<WorkoutFilterBar> createState() => _WorkoutFilterBarState();
}

class _WorkoutFilterBarState extends State<WorkoutFilterBar> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _showSuggestions = false;
  List<String> _currentSuggestions = [];

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.filterService.criteria.searchQuery;
    _searchController.addListener(_onSearchChanged);
    _searchFocusNode.addListener(_onSearchFocusChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text;
    widget.filterService.updateSearchQuery(query);

    if (query.isNotEmpty) {
      setState(() {
        _currentSuggestions = widget.filterService.getSearchSuggestions(query);
        _showSuggestions =
            _currentSuggestions.isNotEmpty && _searchFocusNode.hasFocus;
      });
    } else {
      setState(() {
        _showSuggestions = false;
      });
    }

    widget.onFiltersChanged?.call();
  }

  void _onSearchFocusChanged() {
    setState(() {
      _showSuggestions = _searchFocusNode.hasFocus &&
          _currentSuggestions.isNotEmpty &&
          _searchController.text.isNotEmpty;
    });
  }

  void _selectSuggestion(String suggestion) {
    _searchController.text = suggestion;
    _searchFocusNode.unfocus();
    setState(() {
      _showSuggestions = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search Bar
        Container(
          margin:
              const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: AppColorsTheme.surface(context),
                  borderRadius: AppBorderRadius.cardRadius,
                  border: Border.all(
                    color: _searchFocusNode.hasFocus
                        ? AppColors.primary
                        : Theme.of(context).brightness == Brightness.dark
                            ? AppColors.darkBorder
                            : AppColors.grey300,
                  ),
                  boxShadow: _searchFocusNode.hasFocus
                      ? AppShadows.elevation2
                      : AppShadows.elevation1,
                ),
                child: TextField(
                  controller: _searchController,
                  focusNode: _searchFocusNode,
                  decoration: InputDecoration(
                    hintText: 'Search workouts, exercises, or muscle groups...',
                    hintStyle: TextStyle(
                      color: AppColorsTheme.textSecondary(context),
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: AppColorsTheme.textSecondary(context),
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            onPressed: () {
                              _searchController.clear();
                              _searchFocusNode.unfocus();
                            },
                            icon: Icon(
                              Icons.clear,
                              color: AppColorsTheme.textSecondary(context),
                            ),
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.md,
                      vertical: AppSpacing.md,
                    ),
                  ),
                  style: TextStyle(
                    color: AppColorsTheme.textPrimary(context),
                  ),
                ),
              ),

              // Search Suggestions
              if (_showSuggestions) ...[
                const SizedBox(height: AppSpacing.xs),
                Container(
                  decoration: BoxDecoration(
                    color: AppColorsTheme.surface(context),
                    borderRadius: AppBorderRadius.cardRadius,
                    border: Border.all(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppColors.darkBorder
                          : AppColors.grey300,
                    ),
                    boxShadow: AppShadows.elevation3,
                  ),
                  child: Column(
                    children: _currentSuggestions.map((suggestion) {
                      return InkWell(
                        onTap: () => _selectSuggestion(suggestion),
                        borderRadius: AppBorderRadius.cardRadius,
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpacing.md,
                            vertical: AppSpacing.sm,
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.search,
                                size: 16,
                                color: AppColorsTheme.textSecondary(context),
                              ),
                              const SizedBox(width: AppSpacing.sm),
                              Expanded(
                                child: Text(
                                  suggestion,
                                  style: AppTypography.body2.copyWith(
                                    color: AppColorsTheme.textPrimary(context),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ],
          ),
        ),

        const SizedBox(height: AppSpacing.md),

        // Filter Chips
        SizedBox(
          height: 50,
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.screenPadding),
            children: [
              // Filter Button
              _buildFilterButton(context),
              const SizedBox(width: AppSpacing.sm),

              // Active Filter Chips
              if (widget.filterService.criteria.hasActiveFilters) ...[
                ...widget.filterService.criteria.muscleGroups.map(
                  (muscle) => _buildActiveFilterChip(
                    context,
                    muscle,
                    Icons.fitness_center,
                    () => widget.filterService.toggleMuscleGroup(muscle),
                  ),
                ),
                ...widget.filterService.criteria.equipment.map(
                  (equipment) => _buildActiveFilterChip(
                    context,
                    equipment,
                    Icons.sports_gymnastics,
                    () => widget.filterService.toggleEquipment(equipment),
                  ),
                ),
                ...widget.filterService.criteria.difficulties.map(
                  (difficulty) => _buildActiveFilterChip(
                    context,
                    _difficultyToString(difficulty),
                    Icons.trending_up,
                    () => widget.filterService.toggleDifficulty(difficulty),
                  ),
                ),
                ...widget.filterService.criteria.categories.map(
                  (category) => _buildActiveFilterChip(
                    context,
                    _categoryToString(category),
                    Icons.category,
                    () => widget.filterService.toggleCategory(category),
                  ),
                ),

                // Clear All Button
                const SizedBox(width: AppSpacing.sm),
                _buildClearAllButton(context),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFilterButton(BuildContext context) {
    final hasActiveFilters = widget.filterService.criteria.hasActiveFilters;
    final activeCount = widget.filterService.criteria.activeFilterCount;

    return GestureDetector(
      onTap: () => _showFilterBottomSheet(context),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        decoration: BoxDecoration(
          color: hasActiveFilters
              ? AppColors.primary.withValues(alpha: 0.1)
              : AppColorsTheme.surface(context),
          borderRadius: AppBorderRadius.chipRadius,
          border: Border.all(
            color: hasActiveFilters
                ? AppColors.primary
                : Theme.of(context).brightness == Brightness.dark
                    ? AppColors.darkBorder
                    : AppColors.grey300,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.tune,
              size: 18,
              color: hasActiveFilters
                  ? AppColors.primary
                  : AppColorsTheme.textSecondary(context),
            ),
            const SizedBox(width: AppSpacing.xs),
            Text(
              hasActiveFilters ? 'Filters ($activeCount)' : 'Filters',
              style: AppTypography.buttonSmall.copyWith(
                color: hasActiveFilters
                    ? AppColors.primary
                    : AppColorsTheme.textSecondary(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveFilterChip(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onRemove,
  ) {
    return Container(
      margin: const EdgeInsets.only(right: AppSpacing.sm),
      child: Chip(
        avatar: Icon(
          icon,
          size: 16,
          color: AppColors.primary,
        ),
        label: Text(
          label,
          style: AppTypography.small.copyWith(
            color: AppColors.primary,
            fontWeight: AppTypography.medium,
          ),
        ),
        deleteIcon: const Icon(
          Icons.close,
          size: 16,
          color: AppColors.primary,
        ),
        onDeleted: () {
          onRemove();
          widget.onFiltersChanged?.call();
        },
        backgroundColor: AppColors.primary.withValues(alpha: 0.1),
        side: BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
        shape: RoundedRectangleBorder(
          borderRadius: AppBorderRadius.chipRadius,
        ),
      ),
    );
  }

  Widget _buildClearAllButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.filterService.clearFilters();
        widget.onFiltersChanged?.call();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.1),
          borderRadius: AppBorderRadius.chipRadius,
          border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.clear_all,
              size: 14,
              color: Colors.red,
            ),
            const SizedBox(width: AppSpacing.xs),
            Text(
              'Clear All',
              style: AppTypography.small.copyWith(
                color: Colors.red,
                fontWeight: AppTypography.medium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => WorkoutFilterBottomSheet(
        filterService: widget.filterService,
        onFiltersChanged: widget.onFiltersChanged,
      ),
    );
  }

  String _difficultyToString(WorkoutDifficulty difficulty) {
    switch (difficulty) {
      case WorkoutDifficulty.beginner:
        return 'Beginner';
      case WorkoutDifficulty.intermediate:
        return 'Intermediate';
      case WorkoutDifficulty.advanced:
        return 'Advanced';
    }
  }

  String _categoryToString(WorkoutCategory category) {
    switch (category) {
      case WorkoutCategory.strength:
        return 'Strength';
      case WorkoutCategory.cardio:
        return 'Cardio';
      case WorkoutCategory.flexibility:
        return 'Flexibility';
      case WorkoutCategory.hiit:
        return 'HIIT';
      case WorkoutCategory.bodyweight:
        return 'Bodyweight';
    }
  }
}

class WorkoutFilterBottomSheet extends StatefulWidget {
  final WorkoutFilterService filterService;
  final VoidCallback? onFiltersChanged;

  const WorkoutFilterBottomSheet({
    super.key,
    required this.filterService,
    this.onFiltersChanged,
  });

  @override
  State<WorkoutFilterBottomSheet> createState() =>
      _WorkoutFilterBottomSheetState();
}

class _WorkoutFilterBottomSheetState extends State<WorkoutFilterBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      builder: (context, scrollController) => Container(
        decoration: BoxDecoration(
          color: AppColorsTheme.surface(context),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: AppSpacing.md),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppColors.darkBorder
                    : Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: Row(
                children: [
                  Text(
                    'Filter Workouts',
                    style: AppTypography.heading2.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      widget.filterService.clearFilters();
                      widget.onFiltersChanged?.call();
                    },
                    child: Text(
                      'Clear All',
                      style: AppTypography.buttonSmall.copyWith(
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Filter Content
            Expanded(
              child: ListView(
                controller: scrollController,
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
                children: [
                  _buildMuscleGroupsSection(),
                  const SizedBox(height: AppSpacing.xl),
                  _buildEquipmentSection(),
                  const SizedBox(height: AppSpacing.xl),
                  _buildDifficultySection(),
                  const SizedBox(height: AppSpacing.xl),
                  _buildCategorySection(),
                  const SizedBox(height: AppSpacing.xl),
                  _buildDurationSection(),
                  const SizedBox(height: AppSpacing.xl),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMuscleGroupsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Muscle Groups',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: WorkoutFilterService.availableMuscleGroups.map((muscle) {
            final isSelected =
                widget.filterService.criteria.muscleGroups.contains(muscle);
            return _buildFilterChip(
              muscle,
              isSelected,
              () {
                widget.filterService.toggleMuscleGroup(muscle);
                widget.onFiltersChanged?.call();
                setState(() {});
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildEquipmentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Equipment',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: WorkoutFilterService.availableEquipment.map((equipment) {
            final isSelected =
                widget.filterService.criteria.equipment.contains(equipment);
            return _buildFilterChip(
              equipment,
              isSelected,
              () {
                widget.filterService.toggleEquipment(equipment);
                widget.onFiltersChanged?.call();
                setState(() {});
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDifficultySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Difficulty',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: WorkoutDifficulty.values.map((difficulty) {
            final isSelected =
                widget.filterService.criteria.difficulties.contains(difficulty);
            return _buildFilterChip(
              _difficultyToString(difficulty),
              isSelected,
              () {
                widget.filterService.toggleDifficulty(difficulty);
                widget.onFiltersChanged?.call();
                setState(() {});
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Category',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: WorkoutCategory.values.map((category) {
            final isSelected =
                widget.filterService.criteria.categories.contains(category);
            return _buildFilterChip(
              _categoryToString(category),
              isSelected,
              () {
                widget.filterService.toggleCategory(category);
                widget.onFiltersChanged?.call();
                setState(() {});
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDurationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Duration (minutes)',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: [
            _buildDurationChip('< 15 min', 0, 15),
            _buildDurationChip('15-30 min', 15, 30),
            _buildDurationChip('30-45 min', 30, 45),
            _buildDurationChip('45+ min', 45, null),
          ],
        ),
      ],
    );
  }

  Widget _buildDurationChip(String label, int? minDuration, int? maxDuration) {
    final currentMin = widget.filterService.criteria.minDuration;
    final currentMax = widget.filterService.criteria.maxDuration;
    final isSelected = currentMin == minDuration && currentMax == maxDuration;

    return _buildFilterChip(
      label,
      isSelected,
      () {
        if (isSelected) {
          widget.filterService.setDurationRange(null, null);
        } else {
          widget.filterService.setDurationRange(minDuration, maxDuration);
        }
        widget.onFiltersChanged?.call();
        setState(() {});
      },
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.1)
              : AppColorsTheme.surface(context),
          borderRadius: AppBorderRadius.chipRadius,
          border: Border.all(
            color: isSelected
                ? AppColors.primary
                : Theme.of(context).brightness == Brightness.dark
                    ? AppColors.darkBorder
                    : AppColors.grey300,
          ),
        ),
        child: Text(
          label,
          style: AppTypography.buttonSmall.copyWith(
            color: isSelected
                ? AppColors.primary
                : AppColorsTheme.textSecondary(context),
            fontWeight:
                isSelected ? AppTypography.medium : AppTypography.regular,
          ),
        ),
      ),
    );
  }

  String _difficultyToString(WorkoutDifficulty difficulty) {
    switch (difficulty) {
      case WorkoutDifficulty.beginner:
        return 'Beginner';
      case WorkoutDifficulty.intermediate:
        return 'Intermediate';
      case WorkoutDifficulty.advanced:
        return 'Advanced';
    }
  }

  String _categoryToString(WorkoutCategory category) {
    switch (category) {
      case WorkoutCategory.strength:
        return 'Strength';
      case WorkoutCategory.cardio:
        return 'Cardio';
      case WorkoutCategory.flexibility:
        return 'Flexibility';
      case WorkoutCategory.hiit:
        return 'HIIT';
      case WorkoutCategory.bodyweight:
        return 'Bodyweight';
    }
  }
}
