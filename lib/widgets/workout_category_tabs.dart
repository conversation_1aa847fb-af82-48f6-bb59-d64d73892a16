import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import '../services/workout_filter_service.dart';

class WorkoutCategoryTabs extends StatefulWidget {
  final WorkoutFilterService filterService;
  final Function(WorkoutCategory?)? onCategoryChanged;

  const WorkoutCategoryTabs({
    super.key,
    required this.filterService,
    this.onCategoryChanged,
  });

  @override
  State<WorkoutCategoryTabs> createState() => _WorkoutCategoryTabsState();
}

class _WorkoutCategoryTabsState extends State<WorkoutCategoryTabs> {
  WorkoutCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    // Initialize with the first selected category if any
    final selectedCategories = widget.filterService.criteria.categories;
    if (selectedCategories.isNotEmpty) {
      _selectedCategory = selectedCategories.first;
    }
  }

  void _selectCategory(WorkoutCategory? category) {
    setState(() {
      _selectedCategory = category;
    });

    // Clear existing category filters
    final currentCategories =
        Set<WorkoutCategory>.from(widget.filterService.criteria.categories);
    for (final cat in currentCategories) {
      widget.filterService.toggleCategory(cat);
    }

    // Apply new category filter if selected
    if (category != null) {
      widget.filterService.toggleCategory(category);
    }

    widget.onCategoryChanged?.call(category);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding:
            const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
        children: [
          // All Categories Tab
          _buildCategoryTab(
            context,
            'All',
            Icons.grid_view,
            null,
            _selectedCategory == null,
          ),
          const SizedBox(width: AppSpacing.sm),

          // Individual Category Tabs
          ...WorkoutCategory.values.map((category) => Padding(
                padding: const EdgeInsets.only(right: AppSpacing.sm),
                child: _buildCategoryTab(
                  context,
                  _getCategoryDisplayName(category),
                  _getCategoryIcon(category),
                  category,
                  _selectedCategory == category,
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildCategoryTab(
    BuildContext context,
    String label,
    IconData icon,
    WorkoutCategory? category,
    bool isSelected,
  ) {
    return GestureDetector(
      onTap: () => _selectCategory(category),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        decoration: BoxDecoration(
          color:
              isSelected ? AppColors.primary : AppColorsTheme.surface(context),
          borderRadius: AppBorderRadius.chipRadius,
          border: Border.all(
            color: isSelected
                ? AppColors.primary
                : Theme.of(context).brightness == Brightness.dark
                    ? AppColors.darkBorder
                    : AppColors.grey300,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected
                  ? Colors.white
                  : AppColorsTheme.textSecondary(context),
            ),
            const SizedBox(width: AppSpacing.xs),
            Text(
              label,
              style: AppTypography.buttonSmall.copyWith(
                color: isSelected
                    ? Colors.white
                    : AppColorsTheme.textSecondary(context),
                fontWeight:
                    isSelected ? AppTypography.semiBold : AppTypography.medium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getCategoryDisplayName(WorkoutCategory category) {
    switch (category) {
      case WorkoutCategory.strength:
        return 'Strength';
      case WorkoutCategory.cardio:
        return 'Cardio';
      case WorkoutCategory.flexibility:
        return 'Flexibility';
      case WorkoutCategory.hiit:
        return 'HIIT';
      case WorkoutCategory.bodyweight:
        return 'Bodyweight';
    }
  }

  IconData _getCategoryIcon(WorkoutCategory category) {
    switch (category) {
      case WorkoutCategory.strength:
        return Icons.fitness_center;
      case WorkoutCategory.cardio:
        return Icons.favorite;
      case WorkoutCategory.flexibility:
        return Icons.self_improvement;
      case WorkoutCategory.hiit:
        return Icons.flash_on;
      case WorkoutCategory.bodyweight:
        return Icons.accessibility_new;
    }
  }
}

class WorkoutCategorySection extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final bool isEmpty;

  const WorkoutCategorySection({
    super.key,
    required this.title,
    required this.children,
    this.isEmpty = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.screenPadding,
            vertical: AppSpacing.md,
          ),
          child: Text(
            title,
            style: AppTypography.heading3.copyWith(
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
        ),
        ...children,
        const SizedBox(height: AppSpacing.lg),
      ],
    );
  }
}
