import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/workout.dart';
import '../models/session_analytics.dart';
import '../services/workout_service.dart';
import '../services/progress_service.dart';
import '../services/auth_service.dart';
import '../design_system/design_system.dart';
import '../widgets/progress_charts.dart';
import '../widgets/offline_status_indicator.dart';

import 'workout_session_screen.dart';

class WorkoutDetailScreen extends StatefulWidget {
  final Workout workout;

  const WorkoutDetailScreen({
    super.key,
    required this.workout,
  });

  @override
  State<WorkoutDetailScreen> createState() => _WorkoutDetailScreenState();
}

class _WorkoutDetailScreenState extends State<WorkoutDetailScreen> {
  final WorkoutService _workoutService = WorkoutService();
  List<SessionAnalytics>? _sessionData;
  bool _isLoadingProgress = false;

  @override
  void initState() {
    super.initState();
    _loadProgressData();
  }

  Future<void> _loadProgressData() async {
    if (!mounted) return;

    setState(() {
      _isLoadingProgress = true;
    });

    try {
      final authService = context.read<AuthService>();
      final progressService = context.read<ProgressService>();
      final user = authService.currentUser;

      if (user == null) {
        if (mounted) {
          setState(() {
            _sessionData = null;
          });
        }
        return;
      }

      // Load session analytics for this workout
      final workoutProgress =
          await progressService.getWorkoutProgress(widget.workout.id);

      if (workoutProgress != null &&
          workoutProgress.exerciseProgress.isNotEmpty) {
        // Convert exercise progress to session analytics for chart display
        final sessionAnalytics = <SessionAnalytics>[];

        for (final exerciseProgress in workoutProgress.exerciseProgress) {
          for (final record in exerciseProgress.records) {
            // Find or create session analytics for this date
            var sessionAnalytic = sessionAnalytics.firstWhere(
              (s) =>
                  s.sessionDate.day == record.date.day &&
                  s.sessionDate.month == record.date.month &&
                  s.sessionDate.year == record.date.year,
              orElse: () {
                final newSession = SessionAnalytics(
                  sessionId: 'session_${record.date.millisecondsSinceEpoch}',
                  totalDuration:
                      const Duration(minutes: 45), // Default duration
                  setsCompleted: 0,
                  totalReps: 0,
                  totalVolume: 0,
                  estimatedCalories: 0,
                  exercisePerformance: {},
                  improvementScore: 0,
                  sessionDate: record.date,
                );
                sessionAnalytics.add(newSession);
                return newSession;
              },
            );

            // Update exercise performance
            sessionAnalytic.exercisePerformance[exerciseProgress.exerciseId] =
                ExercisePerformance(
              exerciseId: exerciseProgress.exerciseId,
              exerciseName: exerciseProgress.exerciseName,
              setsCompleted: 1, // Simplified for display
              totalReps: record.reps,
              totalVolume: record.volume,
              maxWeight: record.weight,
              maxReps: record.reps,
              timeSpent: const Duration(minutes: 5), // Default time
            );
          }
        }

        if (mounted) {
          setState(() {
            _sessionData = sessionAnalytics
              ..sort((a, b) => a.sessionDate.compareTo(b.sessionDate));
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _sessionData = [];
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading progress data: $e');

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load progress data: ${e.toString()}'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _loadProgressData,
            ),
          ),
        );

        setState(() {
          _sessionData = null;
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingProgress = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColorsTheme.background(context),
      body: SafeArea(
        child: Column(
          children: [
            AppWorkoutHeader(
              onClose: () => Navigator.pop(context),
            ),
            // Offline Status Indicator
            const OfflineStatusIndicator(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppSpacing.screenPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildWorkoutTitle(),
                    const SizedBox(height: AppSpacing.sectionSpacing),
                    _buildWorkoutStats(),
                    const SizedBox(height: AppSpacing.sectionSpacing),
                    _buildProgressSection(),
                    const SizedBox(height: AppSpacing.sectionSpacing),
                    _buildExerciseList(),
                    const SizedBox(height: AppSpacing.sectionSpacing),
                    _buildStartButton(),
                    const SizedBox(height: AppSpacing.xxxl),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkoutTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.workout.name,
          style: AppTypography.display1.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.textSpacing),
        Text(
          widget.workout.workoutSummary,
          style: AppTypography.body1.copyWith(
            color: AppColorsTheme.textSecondary(context),
          ),
        ),
        if (widget.workout.aiDescription != null) ...[
          const SizedBox(height: AppSpacing.lg),
          AppCard(
            backgroundColor: AppColors.primary.withValues(alpha: 0.1),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.2),
            ),
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: AppBorderRadius.chipRadius,
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    color: AppColors.textOnPrimary,
                    size: 18,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'AI Description',
                        style: AppTypography.buttonMedium.copyWith(
                          color: AppColors.primary,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Text(
                        widget.workout.aiDescription!,
                        style: AppTypography.body2.copyWith(
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildWorkoutStats() {
    final estimatedDuration = _calculateEstimatedDuration();
    final estimatedCalories = _calculateEstimatedCalories();

    return Row(
      children: [
        Expanded(
          child: AppStatCard(
            icon: Icons.schedule,
            iconColor: AppColors.primary,
            value: estimatedDuration,
            label: 'Duration',
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: AppStatCard(
            icon: Icons.local_fire_department,
            iconColor: AppColors.error,
            value: estimatedCalories.toString(),
            label: 'Calories',
            subtitle: 'kcal',
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: AppStatCard(
            icon: Icons.fitness_center,
            iconColor: AppColors.success,
            value: '${widget.workout.exerciseCount}',
            label: 'Exercises',
          ),
        ),
      ],
    );
  }

  Widget _buildProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Progress Analytics',
          style: AppTypography.heading2.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        _buildProgressContent(),
      ],
    );
  }

  Widget _buildProgressContent() {
    if (_isLoadingProgress) {
      return AppCard(
        child: SizedBox(
          height: 200,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
                const SizedBox(height: AppSpacing.md),
                Text(
                  'Loading progress data...',
                  style: AppTypography.body2.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_sessionData == null || _sessionData!.isEmpty) {
      return _buildEmptyProgressState();
    }

    // Show progress charts for exercises with data
    final exercisesWithData = _sessionData!
        .expand((session) => session.exercisePerformance.keys)
        .toSet()
        .toList();

    if (exercisesWithData.isEmpty) {
      return _buildEmptyProgressState();
    }

    return Column(
      children: [
        // Show progress chart for the first exercise with data
        ProgressCharts(
          sessionData: _sessionData!,
          exerciseId: exercisesWithData.first,
          exerciseName: _sessionData!
              .expand((s) => s.exercisePerformance.values)
              .firstWhere((e) => e.exerciseId == exercisesWithData.first)
              .exerciseName,
        ),
        if (exercisesWithData.length > 1) ...[
          const SizedBox(height: AppSpacing.md),
          _buildAdditionalExercisesInfo(exercisesWithData.length),
        ],
        const SizedBox(height: AppSpacing.md),
        _buildProgressActions(),
      ],
    );
  }

  Widget _buildEmptyProgressState() {
    return AppCard(
      child: SizedBox(
        height: 180,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.analytics_outlined,
                size: 48,
                color: AppColorsTheme.textSecondary(context),
              ),
              const SizedBox(height: AppSpacing.sm),
              Text(
                'No Progress Data Yet',
                style: AppTypography.heading3.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                ),
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(
                'Complete this workout to see your progress',
                style: AppTypography.body2.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppSpacing.md),
              AppButton(
                text: 'Start First Workout',
                icon: Icons.play_arrow,
                onPressed: _startWorkoutSession,
                size: AppButtonSize.small,
                variant: AppButtonVariant.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdditionalExercisesInfo(int totalExercises) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppColors.primary,
            size: 20,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              'View detailed progress for all $totalExercises exercises in your profile',
              style: AppTypography.body2.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: AppColors.primary,
            size: 16,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressActions() {
    return Row(
      children: [
        Expanded(
          child: AppButton(
            text: 'Refresh Data',
            icon: Icons.refresh,
            onPressed: _loadProgressData,
            size: AppButtonSize.small,
            variant: AppButtonVariant.secondary,
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: AppButton(
            text: 'View All Progress',
            icon: Icons.analytics,
            onPressed: () {
              // Navigate to profile screen
              Navigator.pushNamed(context, '/profile');
            },
            size: AppButtonSize.small,
            variant: AppButtonVariant.outline,
          ),
        ),
      ],
    );
  }

  Widget _buildExerciseList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Exercise List',
          style: AppTypography.heading2.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        ...widget.workout.exercises.asMap().entries.map((entry) {
          final index = entry.key;
          final exercise = entry.value;

          return Container(
            margin: const EdgeInsets.only(bottom: AppSpacing.md),
            child: AppCard(
              child: Row(
                children: [
                  // Exercise number and icon
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: AppBorderRadius.chipRadius,
                      border: Border.all(
                        color: AppColors.primary.withValues(alpha: 0.2),
                      ),
                    ),
                    child: exercise.exercise.videoUrl != null
                        ? ClipRRect(
                            borderRadius: AppBorderRadius.chipRadius,
                            child: Image.network(
                              _getExerciseThumbnail(
                                  exercise.exercise.videoUrl!),
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  const Icon(
                                Icons.play_circle_fill,
                                color: AppColors.primary,
                                size: 24,
                              ),
                            ),
                          )
                        : Stack(
                            children: [
                              const Center(
                                child: Icon(
                                  Icons.fitness_center,
                                  color: AppColors.primary,
                                  size: 20,
                                ),
                              ),
                              Positioned(
                                top: 2,
                                right: 2,
                                child: Container(
                                  width: 16,
                                  height: 16,
                                  decoration: const BoxDecoration(
                                    color: AppColors.primary,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Text(
                                      '${index + 1}',
                                      style: AppTypography.small.copyWith(
                                        color: AppColors.textOnPrimary,
                                        fontWeight: AppTypography.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                  ),
                  const SizedBox(width: AppSpacing.lg),
                  // Exercise details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          exercise.exercise.name,
                          style: AppTypography.heading3.copyWith(
                            fontSize: 16,
                            color: AppColorsTheme.textPrimary(context),
                          ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          exercise.formattedReps,
                          style: AppTypography.body2.copyWith(
                            color: AppColors.primary,
                            fontWeight: AppTypography.semiBold,
                          ),
                        ),
                        if (exercise.formattedRestTime.isNotEmpty) ...[
                          const SizedBox(height: AppSpacing.xs),
                          Text(
                            'Rest: ${exercise.formattedRestTime}',
                            style: AppTypography.caption.copyWith(
                              color: AppColorsTheme.textSecondary(context),
                            ),
                          ),
                        ],
                        if (exercise.exercise.primaryMuscle != null) ...[
                          const SizedBox(height: AppSpacing.xs),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSpacing.sm,
                              vertical: AppSpacing.xs,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.success.withValues(alpha: 0.1),
                              borderRadius: AppBorderRadius.chipRadius,
                            ),
                            child: Text(
                              exercise.exercise.primaryMuscle!,
                              style: AppTypography.small.copyWith(
                                color: AppColors.success,
                                fontWeight: AppTypography.medium,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildStartButton() {
    return AppButton(
      text: widget.workout.isActive ? 'Continue Workout' : 'Start Workout',
      icon: Icons.play_arrow,
      onPressed: _startWorkoutSession,
      size: AppButtonSize.large,
      variant: widget.workout.isActive
          ? AppButtonVariant.success
          : AppButtonVariant.primary,
      fullWidth: true,
    );
  }

  String _calculateEstimatedDuration() {
    // Calculate based on sets, reps, and rest intervals
    int totalSeconds = 0;

    for (final exercise in widget.workout.exercises) {
      // Estimate 3 seconds per rep
      final repsPerSet = exercise.reps.isNotEmpty ? exercise.reps.first : 10;
      final workingTime = exercise.sets * repsPerSet * 3;

      // Add rest time between sets
      final restTime = (exercise.sets - 1) * (exercise.restInterval ?? 60);

      totalSeconds += workingTime + restTime;
    }

    // Add transition time between exercises (30 seconds each)
    totalSeconds += (widget.workout.exercises.length - 1) * 30;

    final minutes = totalSeconds ~/ 60;
    return '$minutes min';
  }

  int _calculateEstimatedCalories() {
    // Rough estimation: 5-8 calories per minute depending on intensity
    final durationStr = _calculateEstimatedDuration();
    final minutes = int.tryParse(durationStr.replaceAll(' min', '')) ?? 30;

    // Use 6 calories per minute as average
    return minutes * 6;
  }

  String _getExerciseThumbnail(String videoUrl) {
    // For Vimeo URLs, generate thumbnail
    if (videoUrl.contains('vimeo.com')) {
      final videoId = videoUrl.split('/').last;
      return 'https://vumbnail.com/$videoId.jpg';
    }

    // Fallback to a fitness image
    return 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80';
  }

  void _startWorkoutSession() async {
    try {
      // Start the workout session in the database
      await _workoutService.startWorkout(widget.workout.id);

      if (mounted) {
        // Navigate to workout session screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => WorkoutSessionScreen(workout: widget.workout),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting workout: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
