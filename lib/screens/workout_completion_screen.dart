import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/workout.dart';
import '../models/session_analytics.dart';
import '../design_system/design_system.dart';
import '../widgets/session_summary_card.dart';
import '../widgets/workout_feedback_form.dart';
import '../services/workout_service.dart';

class WorkoutCompletionScreen extends StatefulWidget {
  final Workout workout;
  final int totalMinutes;
  final int totalSets;
  final List<Map<String, dynamic>> completedSets;
  final SessionAnalytics? sessionAnalytics;
  final SessionAnalytics? previousSession;

  const WorkoutCompletionScreen({
    super.key,
    required this.workout,
    required this.totalMinutes,
    required this.totalSets,
    required this.completedSets,
    this.sessionAnalytics,
    this.previousSession,
  });

  @override
  State<WorkoutCompletionScreen> createState() =>
      _WorkoutCompletionScreenState();
}

class _WorkoutCompletionScreenState extends State<WorkoutCompletionScreen>
    with TickerProviderStateMixin {
  late AnimationController _celebrationController;
  late AnimationController _statsController;
  late Animation<double> _celebrationAnimation;
  late Animation<double> _statsAnimation;

  final WorkoutService _workoutService = WorkoutService();
  bool _feedbackSubmitted = false;

  @override
  void initState() {
    super.initState();

    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _statsController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _celebrationAnimation = CurvedAnimation(
      parent: _celebrationController,
      curve: Curves.bounceOut,
    );

    _statsAnimation = CurvedAnimation(
      parent: _statsController,
      curve: Curves.easeOut,
    );

    // Start animations
    _celebrationController.forward();
    Future.delayed(const Duration(milliseconds: 400), () {
      _statsController.forward();
    });
  }

  @override
  void dispose() {
    _celebrationController.dispose();
    _statsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    _buildCelebration(),
                    const SizedBox(height: 32),
                    if (widget.sessionAnalytics != null) ...[
                      SessionSummaryCard(
                        sessionAnalytics: widget.sessionAnalytics!,
                        previousSession: widget.previousSession,
                      ),
                      const SizedBox(height: 24),
                    ] else ...[
                      _buildWorkoutStats(),
                      const SizedBox(height: 24),
                      _buildProgressUpdate(),
                      const SizedBox(height: 32),
                      _buildExerciseBreakdown(),
                      const SizedBox(height: 32),
                    ],
                    if (!_feedbackSubmitted) ...[
                      WorkoutFeedbackForm(
                        onFeedbackSubmitted: _handleFeedbackSubmission,
                        onSkip: _skipFeedback,
                      ),
                      const SizedBox(height: 24),
                    ],
                    _buildActionButtons(),
                    const SizedBox(height: 100), // Space for bottom nav
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentItem: AppNavigationItem.workouts,
        onItemTapped: (item) => navigateToItem(context, item),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.fitness_center,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'OpenFit',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                Text(
                  'by OpenThrive',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.help_outline,
            color: Colors.grey,
            size: 24,
          ),
        ],
      ),
    );
  }

  Widget _buildCelebration() {
    return ScaleTransition(
      scale: _celebrationAnimation,
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: const BoxDecoration(
              color: Color(0xFF10B981),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.emoji_events,
              color: Colors.white,
              size: 40,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Great Work!',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You crushed today\'s ${widget.workout.name.toLowerCase()} workout',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutStats() {
    return FadeTransition(
      opacity: _statsAnimation,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Workout Summary',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.access_time,
                  color: const Color(0xFF6366F1),
                  value: '${widget.totalMinutes}',
                  label: 'minutes',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.local_fire_department,
                  color: const Color(0xFFEF4444),
                  value: '${_calculateCalories()}',
                  label: 'kcal burned',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.refresh,
                  color: const Color(0xFF10B981),
                  value: '${widget.totalSets}',
                  label: 'sets completed',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required Color color,
    required String value,
    required String label,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressUpdate() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF10B981).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF10B981).withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.trending_up,
            color: Color(0xFF10B981),
            size: 24,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Progress Update',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF10B981),
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '2 more sets than last time! You\'re getting stronger.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF10B981),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseBreakdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Exercise Breakdown',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        ...widget.workout.exercises
            .map((exercise) => _buildExerciseItem(exercise)),
      ],
    );
  }

  Widget _buildExerciseItem(dynamic exercise) {
    // Mock improvement data - in real app this would come from comparing with previous workouts
    final improvements = {
      'Dumbbell Bench Press': '+2 reps',
      'Lat Pulldowns': '+5 lbs',
      'Seated Cable Rows': 'Same as last time',
      'Pull-ups': '+1 rep',
    };

    final colors = {
      'Dumbbell Bench Press': const Color(0xFF10B981),
      'Lat Pulldowns': const Color(0xFF10B981),
      'Seated Cable Rows': Colors.grey,
      'Pull-ups': const Color(0xFF10B981),
    };

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exercise.exercise.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${exercise.sets} sets x ${exercise.reps.first} reps',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                exercise.weight != null && exercise.weight!.isNotEmpty
                    ? '${exercise.weight!.first} lbs'
                    : 'Body weight',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                improvements[exercise.exercise.name] ?? 'New exercise',
                style: TextStyle(
                  fontSize: 12,
                  color: colors[exercise.exercise.name] ?? Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleFeedbackSubmission(int rating, String? notes) async {
    try {
      await _workoutService.saveWorkoutFeedback(
        workoutId: widget.workout.id,
        rating: rating,
        notes: notes,
        duration: widget.totalMinutes,
        sessionData: {
          'total_sets': widget.totalSets,
          'completed_sets_data': widget.completedSets,
        },
      );

      setState(() {
        _feedbackSubmitted = true;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Thank you for your feedback!'),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to save feedback. Please try again.'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _skipFeedback() {
    setState(() {
      _feedbackSubmitted = true;
    });
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _completeWorkout,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6366F1),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.check, size: 20),
                SizedBox(width: 8),
                Text(
                  'Done',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),
        TextButton(
          onPressed: _shareProgress,
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.share,
                color: Color(0xFF6366F1),
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Share Progress',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF6366F1),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  int _calculateCalories() {
    // Simple calculation: ~12 calories per minute of strength training
    return (widget.totalMinutes * 12.3).round();
  }

  void _completeWorkout() async {
    HapticFeedback.mediumImpact();

    try {
      // Mark workout as completed in the workouts table
      await _workoutService.completeWorkout(widget.workout.id);
    } catch (e) {
      // Handle error but don't block navigation
      debugPrint('Error completing workout: $e');
    }

    // Navigate back to main screen or home
    if (mounted) {
      Navigator.of(context).popUntil((route) => route.isFirst);
    }
  }

  void _shareProgress() {
    // Implement sharing functionality
    HapticFeedback.lightImpact();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sharing functionality coming soon!'),
        backgroundColor: Color(0xFF6366F1),
      ),
    );
  }
}
