import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/workout.dart';
import '../models/session_analytics.dart';
import '../design_system/design_system.dart';
import '../widgets/session_summary_card.dart';
import '../widgets/workout_feedback_form.dart';
import '../services/workout_service.dart';

class WorkoutCompletionScreen extends StatefulWidget {
  final Workout workout;
  final int totalMinutes;
  final int totalSets;
  final List<Map<String, dynamic>> completedSets;
  final SessionAnalytics? sessionAnalytics;
  final SessionAnalytics? previousSession;

  const WorkoutCompletionScreen({
    super.key,
    required this.workout,
    required this.totalMinutes,
    required this.totalSets,
    required this.completedSets,
    this.sessionAnalytics,
    this.previousSession,
  });

  @override
  State<WorkoutCompletionScreen> createState() =>
      _WorkoutCompletionScreenState();
}

class _WorkoutCompletionScreenState extends State<WorkoutCompletionScreen>
    with TickerProviderStateMixin {
  late AnimationController _celebrationController;
  late AnimationController _statsController;
  late Animation<double> _celebrationAnimation;
  late Animation<double> _statsAnimation;

  final WorkoutService _workoutService = WorkoutService();
  bool _feedbackSubmitted = false;

  @override
  void initState() {
    super.initState();

    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _statsController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _celebrationAnimation = CurvedAnimation(
      parent: _celebrationController,
      curve: Curves.bounceOut,
    );

    _statsAnimation = CurvedAnimation(
      parent: _statsController,
      curve: Curves.easeOut,
    );

    // Start animations
    _celebrationController.forward();
    Future.delayed(const Duration(milliseconds: 400), () {
      _statsController.forward();
    });
  }

  @override
  void dispose() {
    _celebrationController.dispose();
    _statsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColorsTheme.background(context),
      body: SafeArea(
        child: Column(
          children: [
            AppWorkoutHeader(
              trailing: Icon(
                Icons.help_outline,
                color: AppColorsTheme.textSecondary(context),
                size: 24,
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    _buildCelebration(),
                    const SizedBox(height: 32),
                    if (widget.sessionAnalytics != null) ...[
                      SessionSummaryCard(
                        sessionAnalytics: widget.sessionAnalytics!,
                        previousSession: widget.previousSession,
                      ),
                      const SizedBox(height: 24),
                    ] else ...[
                      _buildWorkoutStats(),
                      const SizedBox(height: 24),
                      _buildProgressUpdate(),
                      const SizedBox(height: 32),
                      _buildExerciseBreakdown(),
                      const SizedBox(height: 32),
                    ],
                    if (!_feedbackSubmitted) ...[
                      WorkoutFeedbackForm(
                        onFeedbackSubmitted: _handleFeedbackSubmission,
                        onSkip: _skipFeedback,
                      ),
                      const SizedBox(height: 24),
                    ],
                    _buildActionButtons(),
                    const SizedBox(height: 100), // Space for bottom nav
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentItem: AppNavigationItem.workouts,
        onItemTapped: (item) => navigateToItem(context, item),
      ),
    );
  }



  Widget _buildCelebration() {
    return ScaleTransition(
      scale: _celebrationAnimation,
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: const BoxDecoration(
              color: AppColors.success,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.emoji_events,
              color: AppColors.textOnPrimary,
              size: 40,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Great Work!',
            style: AppTypography.display1.copyWith(
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            'You crushed today\'s ${widget.workout.name.toLowerCase()} workout',
            style: AppTypography.body1.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutStats() {
    return FadeTransition(
      opacity: _statsAnimation,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Workout Summary',
            style: AppTypography.heading2.copyWith(
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.access_time,
                  color: AppColors.primary,
                  value: '${widget.totalMinutes}',
                  label: 'minutes',
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.local_fire_department,
                  color: AppColors.error,
                  value: '${_calculateCalories()}',
                  label: 'kcal burned',
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.refresh,
                  color: AppColors.success,
                  value: '${widget.totalSets}',
                  label: 'sets completed',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required Color color,
    required String value,
    required String label,
  }) {
    return AppCard(
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.textOnPrimary,
              size: 20,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            value,
            style: AppTypography.display2.copyWith(
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          Text(
            label,
            style: AppTypography.caption.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressUpdate() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        borderRadius: AppBorderRadius.cardRadius,
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.trending_up,
            color: AppColors.success,
            size: 24,
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Progress Update',
                  style: AppTypography.body2.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  '2 more sets than last time! You\'re getting stronger.',
                  style: AppTypography.body2.copyWith(
                    color: AppColors.success,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseBreakdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Exercise Breakdown',
          style: AppTypography.heading2.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: 16),
        ...widget.workout.exercises
            .map((exercise) => _buildExerciseItem(exercise)),
      ],
    );
  }

  Widget _buildExerciseItem(dynamic exercise) {
    // Mock improvement data - in real app this would come from comparing with previous workouts
    final improvements = {
      'Dumbbell Bench Press': '+2 reps',
      'Lat Pulldowns': '+5 lbs',
      'Seated Cable Rows': 'Same as last time',
      'Pull-ups': '+1 rep',
    };

    final colors = {
      'Dumbbell Bench Press': AppColors.success,
      'Lat Pulldowns': AppColors.success,
      'Seated Cable Rows': AppColorsTheme.textSecondary(context),
      'Pull-ups': AppColors.success,
    };

    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColorsTheme.surfaceVariant(context),
        borderRadius: AppBorderRadius.cardRadius,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exercise.exercise.name,
                  style: AppTypography.heading3.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  '${exercise.sets} sets x ${exercise.reps.first} reps',
                  style: AppTypography.body2.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                exercise.weight != null && exercise.weight!.isNotEmpty
                    ? '${exercise.weight!.first} lbs'
                    : 'Body weight',
                style: AppTypography.body2.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(
                improvements[exercise.exercise.name] ?? 'New exercise',
                style: AppTypography.caption.copyWith(
                  color: colors[exercise.exercise.name] ?? AppColorsTheme.textSecondary(context),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleFeedbackSubmission(int rating, String? notes) async {
    try {
      await _workoutService.saveWorkoutFeedback(
        workoutId: widget.workout.id,
        rating: rating,
        notes: notes,
        duration: widget.totalMinutes,
        sessionData: {
          'total_sets': widget.totalSets,
          'completed_sets_data': widget.completedSets,
        },
      );

      setState(() {
        _feedbackSubmitted = true;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Thank you for your feedback!'),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to save feedback. Please try again.'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _skipFeedback() {
    setState(() {
      _feedbackSubmitted = true;
    });
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        AppButton(
          onPressed: _completeWorkout,
          text: 'Done',
          icon: Icons.check,
          fullWidth: true,
        ),
        const SizedBox(height: 12),
        AppButton(
          onPressed: _shareProgress,
          text: 'Share Progress',
          icon: Icons.share,
          variant: AppButtonVariant.ghost,
          fullWidth: true,
        ),
      ],
    );
  }

  int _calculateCalories() {
    // Simple calculation: ~12 calories per minute of strength training
    return (widget.totalMinutes * 12.3).round();
  }

  void _completeWorkout() async {
    HapticFeedback.mediumImpact();

    try {
      // Mark workout as completed in the workouts table
      await _workoutService.completeWorkout(widget.workout.id);
    } catch (e) {
      // Handle error but don't block navigation
      debugPrint('Error completing workout: $e');
    }

    // Navigate back to main screen or home
    if (mounted) {
      Navigator.of(context).popUntil((route) => route.isFirst);
    }
  }

  void _shareProgress() {
    // Implement sharing functionality
    HapticFeedback.lightImpact();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Sharing functionality coming soon!'),
        backgroundColor: AppColors.primary,
      ),
    );
  }
}
