import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/workout.dart';
import '../services/workout_service.dart';
import '../design_system/design_system.dart';
import '../widgets/set_feedback_modal.dart';
import '../widgets/rest_timer_modal.dart';
import '../widgets/offline_status_indicator.dart';
import 'workout_completion_screen.dart';

class WorkoutSessionScreen extends StatefulWidget {
  final Workout workout;

  const WorkoutSessionScreen({
    super.key,
    required this.workout,
  });

  @override
  State<WorkoutSessionScreen> createState() => _WorkoutSessionScreenState();
}

class _WorkoutSessionScreenState extends State<WorkoutSessionScreen> {
  final WorkoutService _workoutService = WorkoutService();

  // Timer variables
  late Timer _timer;
  int _elapsedSeconds = 0;

  // Workout progress
  int _currentExerciseIndex = 0;
  int _currentSet = 1;
  List<List<Map<String, dynamic>>> _completedSets = [];

  // Input controllers
  final TextEditingController _repsController = TextEditingController();
  final TextEditingController _weightController = TextEditingController();

  // Feedback modal state
  bool _showingFeedbackModal = false;
  int? _pendingReps;
  int? _pendingWeight;

  // Rest timer state
  bool _showingRestTimer = false;

  @override
  void initState() {
    super.initState();
    _initializeWorkout();
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    _repsController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  void _initializeWorkout() {
    // Initialize completed sets tracking
    _completedSets = List.generate(
      widget.workout.exercises.length,
      (index) => <Map<String, dynamic>>[],
    );

    // Set initial values from workout plan
    final currentExercise = widget.workout.exercises[_currentExerciseIndex];
    if (currentExercise.reps.isNotEmpty) {
      _repsController.text = currentExercise.reps[_currentSet - 1].toString();
    }
    if (currentExercise.weight != null && currentExercise.weight!.isNotEmpty) {
      _weightController.text =
          currentExercise.weight![_currentSet - 1].toString();
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _elapsedSeconds++;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Main content
          SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                // Offline Status Indicator
                const OfflineStatusIndicator(compact: true),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        _buildTimerAndProgress(),
                        const SizedBox(height: 32),
                        _buildCurrentExercise(),
                        const SizedBox(height: 32),
                        _buildSetInputs(),
                        const SizedBox(height: 32),
                        _buildNextExercisePreview(),
                        const SizedBox(height: 100), // Space for bottom nav
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Feedback Modal Overlay with smooth fade transition
          if (_showingFeedbackModal &&
              _pendingReps != null &&
              _pendingWeight != null)
            Positioned.fill(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 500),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: Tween<double>(
                      begin: 0.0,
                      end: 1.0,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOutCubic,
                    )),
                    child: ScaleTransition(
                      scale: Tween<double>(
                        begin: 0.95,
                        end: 1.0,
                      ).animate(CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeInOutCubic,
                      )),
                      child: child,
                    ),
                  );
                },
                child: SetFeedbackModal(
                  key: const ValueKey('feedback_modal'),
                  completedReps: _pendingReps!,
                  completedWeight: _pendingWeight!,
                  exerciseName: widget
                      .workout.exercises[_currentExerciseIndex].exercise.name,
                  currentSet: _currentSet,
                  totalSets:
                      widget.workout.exercises[_currentExerciseIndex].sets,
                  onComplete: _onFeedbackComplete,
                  onFeedbackSubmitted: _onFeedbackSubmitted,
                ),
              ),
            ),
          // Rest Timer Modal Overlay with smooth fade transition
          if (_showingRestTimer)
            Positioned.fill(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 600),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: Tween<double>(
                      begin: 0.0,
                      end: 1.0,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOutCubic,
                    )),
                    child: ScaleTransition(
                      scale: Tween<double>(
                        begin: 0.95,
                        end: 1.0,
                      ).animate(CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeInOutCubic,
                      )),
                      child: child,
                    ),
                  );
                },
                child: RestTimerModal(
                  key: const ValueKey('rest_timer'),
                  currentExerciseName: widget
                      .workout.exercises[_currentExerciseIndex].exercise.name,
                  currentSet:
                      _currentSet - 1, // Previous set that was completed
                  totalSets:
                      widget.workout.exercises[_currentExerciseIndex].sets,
                  nextExerciseName: _getNextExerciseInfo(),
                  restDurationSeconds: _getRestDuration(),
                  onComplete: _onRestComplete,
                  onSkip: _onRestComplete,
                ),
              ),
            ),
        ],
      ),
      bottomNavigationBar: (_showingFeedbackModal || _showingRestTimer)
          ? null
          : AppBottomNavigation(
              currentItem: AppNavigationItem.workouts,
              onItemTapped: (item) => _handleNavigation(context, item),
            ),
    );
  }

  void _handleNavigation(BuildContext context, AppNavigationItem item) {
    // Don't navigate if we're currently in the workouts section
    if (item == AppNavigationItem.workouts) return;

    // Show confirmation dialog before leaving the active workout
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: AppBorderRadius.cardRadius,
        ),
        title: const Text('Leave Workout?'),
        content: const Text(
          'Your progress will be saved, but the workout will remain active. You can continue later.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              navigateToItem(context, item);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primary,
            ),
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.fitness_center,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'OpenFit',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                Text(
                  'by OpenThrive',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: _showExitDialog,
            child: const Icon(
              Icons.close,
              color: Color(0xFF6B7280),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimerAndProgress() {
    final progress = _calculateProgress();
    final elapsedTime = _formatElapsedTime(_elapsedSeconds);

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  elapsedTime,
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Text(
                  'Elapsed Time',
                  style: TextStyle(
                    color: Color(0xFF6B7280),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${(progress * 100).round()}%',
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Text(
                  'Complete',
                  style: TextStyle(
                    color: Color(0xFF6B7280),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: const Color(0xFFE5E7EB),
          valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF6366F1)),
          minHeight: 4,
        ),
      ],
    );
  }

  Widget _buildCurrentExercise() {
    final currentExercise = widget.workout.exercises[_currentExerciseIndex];
    final totalSets = currentExercise.sets;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFD1D5DB), width: 1.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Now:',
            style: TextStyle(
              color: Color(0xFF6B7280),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  currentExercise.exercise.name,
                  style: const TextStyle(
                    color: Color(0xFF6366F1),
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                'Set $_currentSet / $totalSets',
                style: const TextStyle(
                  color: Color(0xFF6B7280),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSetInputs() {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFFD1D5DB), width: 1.5),
            ),
            child: Column(
              children: [
                TextField(
                  controller: _repsController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    hintText: '0',
                    hintStyle: TextStyle(
                      color: Color(0xFF9CA3AF),
                      fontSize: 24,
                    ),
                  ),
                ),
                const Text(
                  'Reps',
                  style: TextStyle(
                    color: Color(0xFF6B7280),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFFD1D5DB), width: 1.5),
            ),
            child: Column(
              children: [
                TextField(
                  controller: _weightController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    hintText: '0',
                    hintStyle: TextStyle(
                      color: Color(0xFF9CA3AF),
                      fontSize: 24,
                    ),
                  ),
                ),
                const Text(
                  'lbs',
                  style: TextStyle(
                    color: Color(0xFF6B7280),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNextExercisePreview() {
    if (_currentExerciseIndex >= widget.workout.exercises.length - 1 &&
        _currentSet >= widget.workout.exercises[_currentExerciseIndex].sets) {
      // This is the last set of the last exercise
      return ElevatedButton(
        onPressed: _completeWorkout,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF10B981),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          minimumSize: const Size(double.infinity, 50),
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle, size: 20),
            SizedBox(width: 8),
            Text(
              'Complete Workout',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    }

    // Show next exercise or next set
    String nextText;
    String nextExerciseName;

    if (_currentSet < widget.workout.exercises[_currentExerciseIndex].sets) {
      // Next set of current exercise
      nextText = 'Next Set:';
      nextExerciseName =
          widget.workout.exercises[_currentExerciseIndex].exercise.name;
    } else if (_currentExerciseIndex < widget.workout.exercises.length - 1) {
      // Next exercise
      nextText = 'Next:';
      nextExerciseName =
          widget.workout.exercises[_currentExerciseIndex + 1].exercise.name;
    } else {
      // This shouldn't happen since we handle workout completion above
      nextText = 'Next:';
      nextExerciseName = 'Workout Complete';
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.fitness_center,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  nextText,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
                Text(
                  nextExerciseName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: _completeCurrentSet,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF6366F1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatElapsedTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  double _calculateProgress() {
    int totalSets = 0;
    int completedSets = 0;

    for (int i = 0; i < widget.workout.exercises.length; i++) {
      final exercise = widget.workout.exercises[i];
      totalSets += exercise.sets;

      if (i < _currentExerciseIndex) {
        // All sets for previous exercises are completed
        completedSets += exercise.sets;
      } else if (i == _currentExerciseIndex) {
        // Current exercise: count completed sets (current set - 1)
        completedSets += (_currentSet - 1);
      }
      // Future exercises contribute 0 completed sets
    }

    if (totalSets == 0) return 0.0;
    return completedSets / totalSets;
  }

  void _completeCurrentSet() {
    final reps = int.tryParse(_repsController.text) ?? 0;
    final weight = int.tryParse(_weightController.text) ?? 0;

    if (reps <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter valid reps'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Store pending values and show feedback modal
    setState(() {
      _pendingReps = reps;
      _pendingWeight = weight;
      _showingFeedbackModal = true;
    });

    // Show feedback
    HapticFeedback.lightImpact();
  }

  void _onFeedbackSubmitted(String difficulty, bool personalizeWeight) {
    // Store feedback data (you could save this to database)
    print('Difficulty: $difficulty, Personalize: $personalizeWeight');
  }

  void _onFeedbackComplete() {
    if (_pendingReps == null || _pendingWeight == null) return;

    // Record the completed set
    setState(() {
      final currentExercise = widget.workout.exercises[_currentExerciseIndex];

      // Record the completed set
      _completedSets[_currentExerciseIndex].add({
        'set': _currentSet,
        'reps': _pendingReps!,
        'weight': _pendingWeight!,
        'timestamp': DateTime.now(),
      });

      // Hide feedback modal
      _showingFeedbackModal = false;
      _pendingReps = null;
      _pendingWeight = null;

      // Check if this is the last set of the last exercise
      final isLastSet = _currentSet >= currentExercise.sets;
      final isLastExercise =
          _currentExerciseIndex >= widget.workout.exercises.length - 1;

      if (isLastSet && isLastExercise) {
        // Workout complete - no rest needed
        _proceedToNextSetOrExercise();
      } else {
        // Show rest timer before next set/exercise
        _showingRestTimer = true;
      }
    });
  }

  void _onRestComplete() {
    setState(() {
      _showingRestTimer = false;
    });
    _proceedToNextSetOrExercise();
  }

  void _proceedToNextSetOrExercise() {
    setState(() {
      final currentExercise = widget.workout.exercises[_currentExerciseIndex];

      if (_currentSet < currentExercise.sets) {
        // Move to next set of current exercise
        _currentSet++;

        // Update inputs with next set's planned values
        if (currentExercise.reps.length > _currentSet - 1) {
          _repsController.text =
              currentExercise.reps[_currentSet - 1].toString();
        }
        if (currentExercise.weight != null &&
            currentExercise.weight!.length > _currentSet - 1) {
          _weightController.text =
              currentExercise.weight![_currentSet - 1].toString();
        }
      } else if (_currentExerciseIndex < widget.workout.exercises.length - 1) {
        // Move to next exercise
        _currentExerciseIndex++;
        _currentSet = 1;

        // Update inputs with new exercise's planned values
        final nextExercise = widget.workout.exercises[_currentExerciseIndex];
        if (nextExercise.reps.isNotEmpty) {
          _repsController.text = nextExercise.reps[0].toString();
        } else {
          _repsController.clear();
        }
        if (nextExercise.weight != null && nextExercise.weight!.isNotEmpty) {
          _weightController.text = nextExercise.weight![0].toString();
        } else {
          _weightController.clear();
        }
      } else {
        // Workout complete - navigate to completion screen
        _navigateToCompletion();
      }
    });
  }

  void _navigateToCompletion() {
    // Calculate total workout time
    final totalMinutes = (_elapsedSeconds / 60).round();

    // Calculate total sets completed and flatten completed sets
    List<Map<String, dynamic>> allCompletedSets = [];
    for (var exerciseSets in _completedSets) {
      allCompletedSets.addAll(exerciseSets);
    }

    // Navigate to completion screen
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => WorkoutCompletionScreen(
          workout: widget.workout,
          totalMinutes: totalMinutes,
          totalSets: allCompletedSets.length,
          completedSets: allCompletedSets,
        ),
      ),
    );
  }

  String? _getNextExerciseInfo() {
    final currentExercise = widget.workout.exercises[_currentExerciseIndex];

    if (_currentSet < currentExercise.sets) {
      // Next set of same exercise
      return null; // Will show "Set X of Y" in rest timer
    } else if (_currentExerciseIndex < widget.workout.exercises.length - 1) {
      // Next exercise
      return widget.workout.exercises[_currentExerciseIndex + 1].exercise.name;
    }
    return null; // Last exercise, last set
  }

  int _getRestDuration() {
    final currentExercise = widget.workout.exercises[_currentExerciseIndex];
    return currentExercise.restInterval ?? 90; // Default 90 seconds
  }

  void _completeWorkout() async {
    try {
      await _workoutService.completeWorkout(widget.workout.id);
      _navigateToCompletion();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing workout: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _showExitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A2A),
        title: const Text(
          'Exit Workout?',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Your progress will be saved, but the workout will remain active.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Exit workout
            },
            child: const Text(
              'Exit',
              style: TextStyle(color: Color(0xFF6366F1)),
            ),
          ),
        ],
      ),
    );
  }
}
