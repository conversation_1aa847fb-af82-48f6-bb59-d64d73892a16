import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/profile_service.dart';
import '../services/progress_service.dart';
import '../design_system/design_system.dart';
import '../components/theme_toggle.dart';
import '../widgets/strength_progression_view.dart';
import '../widgets/streak_tracker.dart';
import '../models/session_analytics.dart';
import 'onboarding/onboarding_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  late Future<Map<String, dynamic>?> _profileFuture;
  List<SessionAnalytics>? _recentSessions;
  Map<String, dynamic>? _consistencyMetrics;
  bool _isLoadingProgress = false;

  @override
  void initState() {
    super.initState();
    _profileFuture = context.read<ProfileService>().getUserProfile();
    _loadProgressData();
  }

  Future<void> _loadProgressData() async {
    if (!mounted) return;

    setState(() {
      _isLoadingProgress = true;
    });

    try {
      final authService = context.read<AuthService>();
      final progressService = context.read<ProgressService>();
      final user = authService.currentUser;

      if (user == null) {
        if (mounted) {
          setState(() {
            _consistencyMetrics = null;
            _recentSessions = null;
          });
        }
        return;
      }

      // Load consistency metrics
      final metrics = await progressService.getConsistencyMetrics(user.id);

      // Load recent session data for strength progression
      // For demo purposes, we'll create some sample data
      final sampleSessions = _generateSampleSessionData();

      if (mounted) {
        setState(() {
          _consistencyMetrics = metrics;
          _recentSessions = sampleSessions;
        });
      }
    } catch (e) {
      debugPrint('Error loading progress data: $e');

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load progress data: ${e.toString()}'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _loadProgressData,
            ),
          ),
        );

        setState(() {
          _consistencyMetrics = null;
          _recentSessions = null;
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingProgress = false;
        });
      }
    }
  }

  List<SessionAnalytics> _generateSampleSessionData() {
    // Generate sample data for demonstration
    final sessions = <SessionAnalytics>[];
    final now = DateTime.now();

    for (int i = 0; i < 10; i++) {
      final sessionDate = now.subtract(Duration(days: i * 7));
      final baseWeight = 135.0 + (i * 2.5); // Progressive weight increase

      sessions.add(SessionAnalytics(
        sessionId: 'session_$i',
        totalDuration: Duration(minutes: 45 + (i % 3) * 5),
        setsCompleted: 12 + (i % 3),
        totalReps: 36 + (i % 5) * 2,
        totalVolume: baseWeight * (36 + (i % 5) * 2),
        estimatedCalories: 250 + (i % 4) * 25,
        exercisePerformance: {
          'bench_press': ExercisePerformance(
            exerciseId: 'bench_press',
            exerciseName: 'Bench Press',
            setsCompleted: 4,
            totalReps: 12,
            totalVolume: baseWeight * 12,
            maxWeight: baseWeight,
            maxReps: 12,
            timeSpent: const Duration(minutes: 15),
          ),
          'squat': ExercisePerformance(
            exerciseId: 'squat',
            exerciseName: 'Squat',
            setsCompleted: 4,
            totalReps: 12,
            totalVolume: (baseWeight + 20) * 12,
            maxWeight: baseWeight + 20,
            maxReps: 12,
            timeSpent: const Duration(minutes: 15),
          ),
        },
        improvementScore: 75.0 + (i % 4) * 5,
        sessionDate: sessionDate,
      ));
    }

    return sessions.reversed.toList(); // Chronological order
  }

  @override
  Widget build(BuildContext context) {
    final authService = context.watch<AuthService>();

    return Scaffold(
      backgroundColor: AppColorsTheme.background(context),
      body: SafeArea(
        child: FutureBuilder<Map<String, dynamic>?>(
          future: _profileFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              );
            }

            final profile = snapshot.data;
            final userName = profile?['name'] ??
                profile?['display_name'] ??
                (authService.currentUser?.email?.split('@').first) ??
                'User';

            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Profile',
                      style: AppTypography.display2.copyWith(
                        color: AppColorsTheme.textPrimary(context),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Manage your account and preferences',
                      style: AppTypography.body1.copyWith(
                        color: AppColorsTheme.textSecondary(context),
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Profile Card
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: AppColorsTheme.cardBackground(context),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: AppShadows.getElevation(context, 3),
                      ),
                      child: Column(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: const Color(0xFF6366F1)
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.person,
                              size: 40,
                              color: Color(0xFF6366F1),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            userName,
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: AppColorsTheme.textPrimary(context),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            authService.currentUser?.email ?? '',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColorsTheme.textSecondary(context),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Progress Overview
                    _buildProgressOverview(),

                    const SizedBox(height: 24),

                    // Strength Progression
                    _buildStrengthProgression(),

                    const SizedBox(height: 24),

                    // Streak Tracker
                    _buildStreakTracker(),

                    const SizedBox(height: 24),

                    // Settings Options
                    _buildSettingsSection(context, profile),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProgressOverview() {
    if (_isLoadingProgress) {
      return Container(
        height: 120,
        decoration: BoxDecoration(
          color: AppColorsTheme.cardBackground(context),
          borderRadius: BorderRadius.circular(20),
          boxShadow: AppShadows.getElevation(context, 3),
        ),
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ),
      );
    }

    if (_consistencyMetrics == null) {
      return const SizedBox.shrink();
    }

    final workoutsPerWeek = _consistencyMetrics!['workoutsPerWeek'] as double;
    final currentStreak = _consistencyMetrics!['currentStreak'] as int;
    final consistencyScore = _consistencyMetrics!['consistencyScore'] as double;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppShadows.getElevation(context, 3),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Progress Overview',
            style: AppTypography.heading2.copyWith(
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildProgressStat(
                  'Workouts/Week',
                  workoutsPerWeek.toStringAsFixed(1),
                  Icons.fitness_center,
                  AppColors.primary,
                ),
              ),
              Expanded(
                child: _buildProgressStat(
                  'Current Streak',
                  '$currentStreak days',
                  Icons.local_fire_department,
                  AppColors.error,
                ),
              ),
              Expanded(
                child: _buildProgressStat(
                  'Consistency',
                  '${consistencyScore.toInt()}%',
                  Icons.trending_up,
                  AppColors.success,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressStat(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTypography.caption.copyWith(
            color: AppColorsTheme.textSecondary(context),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStrengthProgression() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Strength Progression',
              style: AppTypography.heading2.copyWith(
                color: AppColorsTheme.textPrimary(context),
              ),
            ),
            if (!_isLoadingProgress && _recentSessions != null)
              IconButton(
                icon: Icon(
                  Icons.refresh,
                  color: AppColors.primary,
                ),
                onPressed: _loadProgressData,
                tooltip: 'Refresh data',
              ),
          ],
        ),
        const SizedBox(height: 16),
        _buildStrengthProgressionContent(),
      ],
    );
  }

  Widget _buildStrengthProgressionContent() {
    if (_isLoadingProgress) {
      return Container(
        height: 300,
        decoration: BoxDecoration(
          color: AppColorsTheme.cardBackground(context),
          borderRadius: BorderRadius.circular(20),
          boxShadow: AppShadows.getElevation(context, 3),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              const SizedBox(height: AppSpacing.md),
              Text(
                'Loading strength progression...',
                style: AppTypography.body2.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_recentSessions == null || _recentSessions!.isEmpty) {
      return _buildEmptyStrengthProgressionState();
    }

    // Show strength progression for bench press (if available)
    final exerciseId = 'bench_press';
    final hasExerciseData = _recentSessions!.any(
      (session) => session.exercisePerformance.containsKey(exerciseId),
    );

    if (!hasExerciseData) {
      return _buildNoExerciseDataState();
    }

    return Column(
      children: [
        StrengthProgressionView(
          sessionData: _recentSessions!,
          exerciseId: exerciseId,
          exerciseName: 'Bench Press',
          timeRange: const Duration(days: 90),
        ),
        const SizedBox(height: AppSpacing.md),
        _buildStrengthProgressionActions(),
      ],
    );
  }

  Widget _buildEmptyStrengthProgressionState() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppShadows.getElevation(context, 3),
      ),
      child: Column(
        children: [
          Icon(
            Icons.trending_up_outlined,
            size: 48,
            color: AppColorsTheme.textSecondary(context),
          ),
          const SizedBox(height: 12),
          Text(
            'No workout data yet',
            style: AppTypography.body1.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Complete workouts to see your strength progression',
            style: AppTypography.body2.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          AppButton(
            text: 'Start First Workout',
            icon: Icons.fitness_center,
            onPressed: () {
              // Navigate to workouts screen
              Navigator.pushNamed(context, '/workouts');
            },
            size: AppButtonSize.small,
            variant: AppButtonVariant.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildNoExerciseDataState() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppShadows.getElevation(context, 3),
      ),
      child: Column(
        children: [
          Icon(
            Icons.fitness_center_outlined,
            size: 48,
            color: AppColorsTheme.textSecondary(context),
          ),
          const SizedBox(height: 12),
          Text(
            'No strength exercise data',
            style: AppTypography.body1.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Complete strength training workouts to see progression charts',
            style: AppTypography.body2.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStrengthProgressionActions() {
    return Row(
      children: [
        Expanded(
          child: AppButton(
            text: 'View All Exercises',
            icon: Icons.list,
            onPressed: () {
              // TODO: Navigate to detailed progress view
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Detailed exercise progress view coming soon!'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            size: AppButtonSize.small,
            variant: AppButtonVariant.outline,
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: AppButton(
            text: 'Export Data',
            icon: Icons.download,
            onPressed: () {
              // TODO: Implement data export
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Data export feature coming soon!'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            size: AppButtonSize.small,
            variant: AppButtonVariant.secondary,
          ),
        ),
      ],
    );
  }

  Widget _buildStreakTracker() {
    if (_isLoadingProgress) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          color: AppColorsTheme.cardBackground(context),
          borderRadius: BorderRadius.circular(20),
          boxShadow: AppShadows.getElevation(context, 3),
        ),
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ),
      );
    }

    if (_consistencyMetrics == null) {
      return const SizedBox.shrink();
    }

    final currentStreak = _consistencyMetrics!['currentStreak'] as int;
    final longestStreak = _consistencyMetrics!['longestStreak'] as int;
    final consistencyScore = _consistencyMetrics!['consistencyScore'] as double;

    // Generate recent workout dates for demo
    final recentWorkoutDates = <DateTime>[];
    final now = DateTime.now();

    // Add some sample workout dates for the current week
    if (currentStreak > 0) {
      for (int i = 0; i < currentStreak && i < 7; i++) {
        recentWorkoutDates.add(now.subtract(Duration(days: i)));
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Workout Streak',
          style: AppTypography.heading2.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        const SizedBox(height: 16),
        StreakTracker(
          currentStreak: currentStreak,
          longestStreak: longestStreak,
          consistencyScore: consistencyScore,
          recentWorkoutDates: recentWorkoutDates,
        ),
      ],
    );
  }

  Widget _buildSettingsSection(
      BuildContext context, Map<String, dynamic>? profile) {
    return Column(
      children: [
        // Theme Toggle
        const ThemeToggle(),
        const SizedBox(height: 24),

        _buildSettingsItem(
          icon: Icons.edit,
          title: 'Edit Profile',
          subtitle: 'Update your fitness preferences',
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const OnboardingScreen(),
              ),
            );
          },
        ),
        const SizedBox(height: 12),
        _buildSettingsItem(
          icon: Icons.notifications,
          title: 'Notifications',
          subtitle: 'Manage workout reminders',
          onTap: () {
            // TODO: Navigate to notifications settings
          },
        ),
        const SizedBox(height: 12),
        _buildSettingsItem(
          icon: Icons.privacy_tip,
          title: 'Privacy',
          subtitle: 'Data and privacy settings',
          onTap: () {
            // TODO: Navigate to privacy settings
          },
        ),
        const SizedBox(height: 12),
        _buildSettingsItem(
          icon: Icons.help,
          title: 'Help & Support',
          subtitle: 'Get help and contact support',
          onTap: () {
            // TODO: Navigate to help
          },
        ),
        const SizedBox(height: 24),
        _buildSettingsItem(
          icon: Icons.logout,
          title: 'Sign Out',
          subtitle: 'Sign out of your account',
          isDestructive: true,
          onTap: () async {
            final authService = context.read<AuthService>();
            final confirm = await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                backgroundColor: AppColorsTheme.surface(context),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Text(
                  'Sign Out',
                  style: TextStyle(color: AppColorsTheme.textPrimary(context)),
                ),
                content: Text(
                  'Are you sure you want to sign out?',
                  style:
                      TextStyle(color: AppColorsTheme.textSecondary(context)),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                    child: const Text('Sign Out'),
                  ),
                ],
              ),
            );

            if (confirm == true) {
              await authService.signOut();
            }
          },
        ),
      ],
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Builder(
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: AppColorsTheme.cardBackground(context),
            borderRadius: BorderRadius.circular(16),
            boxShadow: AppShadows.getCardShadow(context),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(16),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: isDestructive
                            ? Colors.red.withValues(alpha: 0.1)
                            : const Color(0xFF6366F1).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        icon,
                        size: 24,
                        color: isDestructive
                            ? Colors.red
                            : const Color(0xFF6366F1),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: isDestructive
                                  ? Colors.red
                                  : AppColorsTheme.textPrimary(context),
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            subtitle,
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColorsTheme.textSecondary(context),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: AppColorsTheme.textSecondary(context),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
