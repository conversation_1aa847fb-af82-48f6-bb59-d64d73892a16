import 'package:flutter/material.dart';
import '../design_system/design_system.dart';
import '../models/workout.dart';
import '../models/exercise.dart';
import '../widgets/exercise_video_player.dart';
import 'workout_session_screen.dart';

/// Screen for workout preparation with equipment checklist and setup instructions
class WorkoutPreparationScreen extends StatefulWidget {
  final Workout workout;

  const WorkoutPreparationScreen({
    super.key,
    required this.workout,
  });

  @override
  State<WorkoutPreparationScreen> createState() =>
      _WorkoutPreparationScreenState();
}

class _WorkoutPreparationScreenState extends State<WorkoutPreparationScreen> {
  final Set<String> _checkedEquipment = {};
  final Set<String> _skippedExercises = {};
  final Set<String> _favoriteExercises = {};
  int _currentExerciseIndex = 0;

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: 'Workout Preparation',
      canPop: true,
      scrollable: false,
      body: Column(
        children: [
          // Progress indicator
          _buildProgressIndicator(context),

          // Main content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppSpacing.screenPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Workout overview
                  _buildWorkoutOverview(context),

                  const SizedBox(height: AppSpacing.sectionSpacing),

                  // Equipment checklist
                  _buildEquipmentChecklist(context),

                  const SizedBox(height: AppSpacing.sectionSpacing),

                  // Exercise preview
                  _buildExercisePreview(context),

                  const SizedBox(height: AppSpacing.sectionSpacing),

                  // Workout customization
                  _buildWorkoutCustomization(context),
                ],
              ),
            ),
          ),

          // Bottom action bar
          _buildBottomActionBar(context),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    final totalSteps = 4; // Overview, Equipment, Preview, Customization
    final completedSteps = _getCompletedSteps();

    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        border: Border(
          bottom: BorderSide(
            color: AppColorsTheme.border(context),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Preparation Progress',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColorsTheme.textPrimary(context),
                    ),
              ),
              Text(
                '$completedSteps/$totalSteps',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.sm),
          LinearProgressIndicator(
            value: completedSteps / totalSteps,
            backgroundColor: AppColorsTheme.borderLight(context),
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutOverview(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(
                'Workout Overview',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColorsTheme.textPrimary(context),
                    ),
              ),
            ],
          ),

          const SizedBox(height: AppSpacing.md),

          // Workout stats
          Row(
            children: [
              _buildStatChip(
                context,
                'Exercises',
                widget.workout.exercises.length.toString(),
                Icons.fitness_center,
                AppColors.primary,
              ),
              const SizedBox(width: AppSpacing.md),
              _buildStatChip(
                context,
                'Duration',
                widget.workout.formattedDuration,
                Icons.timer,
                AppColors.success,
              ),
              const SizedBox(width: AppSpacing.md),
              _buildStatChip(
                context,
                'Muscles',
                widget.workout.primaryMuscles.length.toString(),
                Icons.accessibility_new,
                AppColors.warning,
              ),
            ],
          ),

          const SizedBox(width: AppSpacing.md),

          // Second row of stats
          Row(
            children: [
              _buildStatChip(
                context,
                'Calories',
                '$_estimatedCalories',
                Icons.local_fire_department,
                AppColors.error,
              ),
              const SizedBox(width: AppSpacing.md),
              _buildStatChip(
                context,
                'Difficulty',
                _difficultyLevel,
                Icons.trending_up,
                AppColors.primary,
              ),
              const SizedBox(width: AppSpacing.md),
              _buildStatChip(
                context,
                'Equipment',
                widget.workout.equipmentNeeded.length.toString(),
                Icons.fitness_center,
                AppColors.grey600,
              ),
            ],
          ),

          const SizedBox(height: AppSpacing.md),

          // Description
          if (widget.workout.aiDescription != null)
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColorsTheme.surfaceVariant(context),
                borderRadius: BorderRadius.circular(AppSpacing.sm),
                border: Border.all(
                  color: AppColorsTheme.borderLight(context),
                  width: 1,
                ),
              ),
              child: Text(
                widget.workout.aiDescription!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                      height: 1.5,
                    ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatChip(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppSpacing.sm),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColorsTheme.textPrimary(context),
                  ),
            ),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEquipmentChecklist(BuildContext context) {
    final requiredEquipment = widget.workout.equipmentNeeded;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.checklist,
              color: AppColors.success,
              size: 24,
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              'Equipment Checklist',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColorsTheme.textPrimary(context),
                  ),
            ),
            const Spacer(),
            if (requiredEquipment.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.xs,
                ),
                decoration: BoxDecoration(
                  color: _allEquipmentChecked
                      ? AppColors.success.withValues(alpha: 0.1)
                      : AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSpacing.sm),
                ),
                child: Text(
                  '${_checkedEquipment.length}/${requiredEquipment.length}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _allEquipmentChecked
                            ? AppColors.success
                            : AppColors.warning,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
          ],
        ),
        const SizedBox(height: AppSpacing.md),
        if (requiredEquipment.isEmpty)
          AppCard(
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 32,
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'No Equipment Needed!',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: AppColorsTheme.textPrimary(context),
                                ),
                      ),
                      Text(
                        'This workout uses bodyweight exercises only',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColorsTheme.textSecondary(context),
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )
        else
          ...requiredEquipment
              .map((equipment) => _buildEquipmentCheckItem(context, equipment)),
      ],
    );
  }

  Widget _buildEquipmentCheckItem(BuildContext context, String equipment) {
    final isChecked = _checkedEquipment.contains(equipment);

    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: AppCard(
        child: Row(
          children: [
            Checkbox(
              value: isChecked,
              onChanged: (value) {
                setState(() {
                  if (value == true) {
                    _checkedEquipment.add(equipment);
                  } else {
                    _checkedEquipment.remove(equipment);
                  }
                });
              },
              activeColor: AppColors.success,
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    equipment,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColorsTheme.textPrimary(context),
                          decoration:
                              isChecked ? TextDecoration.lineThrough : null,
                        ),
                  ),
                  Text(
                    _getEquipmentDescription(equipment),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColorsTheme.textSecondary(context),
                        ),
                  ),
                  if (isChecked) ...[
                    const SizedBox(height: AppSpacing.xs),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.sm,
                        vertical: AppSpacing.xs,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppSpacing.xs),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 12,
                            color: AppColors.success,
                          ),
                          const SizedBox(width: AppSpacing.xs),
                          Text(
                            'Ready',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.success,
                                      fontSize: 11,
                                      fontWeight: FontWeight.w500,
                                    ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Icon(
              isChecked ? Icons.check_circle : Icons.radio_button_unchecked,
              color: isChecked
                  ? AppColors.success
                  : AppColorsTheme.textSecondary(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExercisePreview(BuildContext context) {
    final exercises = widget.workout.exercises;
    if (exercises.isEmpty) return const SizedBox.shrink();

    final currentExercise = exercises[_currentExerciseIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.preview,
              color: AppColors.primary,
              size: 24,
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              'Exercise Preview',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColorsTheme.textPrimary(context),
                  ),
            ),
            const Spacer(),
            Text(
              '${_currentExerciseIndex + 1} of ${exercises.length}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.md),
        AppCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Exercise name and info
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          currentExercise.exercise.name,
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColorsTheme.textPrimary(context),
                                  ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Row(
                          children: [
                            if (currentExercise.exercise.primaryMuscle !=
                                null) ...[
                              _buildInfoTag(
                                context,
                                currentExercise.exercise.primaryMuscle!,
                                AppColors.primary,
                              ),
                              const SizedBox(width: AppSpacing.sm),
                            ],
                            _buildInfoTag(
                              context,
                              '${currentExercise.sets} sets',
                              AppColors.success,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Favorite button
                  IconButton(
                    onPressed: () {
                      setState(() {
                        if (_favoriteExercises.contains(currentExercise.id)) {
                          _favoriteExercises.remove(currentExercise.id);
                        } else {
                          _favoriteExercises.add(currentExercise.id);
                        }
                      });
                    },
                    icon: Icon(
                      _favoriteExercises.contains(currentExercise.id)
                          ? Icons.favorite
                          : Icons.favorite_border,
                      color: _favoriteExercises.contains(currentExercise.id)
                          ? AppColors.error
                          : AppColorsTheme.textSecondary(context),
                    ),
                    tooltip: _favoriteExercises.contains(currentExercise.id)
                        ? 'Remove from favorites'
                        : 'Add to favorites',
                  ),
                ],
              ),

              const SizedBox(height: AppSpacing.md),

              // Video player
              ExerciseVideoPlayer(
                videoUrl: currentExercise.exercise.videoUrl,
                verticalVideoUrl: currentExercise.exercise.verticalVideo,
                exerciseName: currentExercise.exercise.name,
                showControls: true,
              ),

              const SizedBox(height: AppSpacing.md),

              // Navigation buttons
              Row(
                children: [
                  Expanded(
                    child: AppButton(
                      text: 'Previous',
                      onPressed: _currentExerciseIndex > 0
                          ? () => setState(() => _currentExerciseIndex--)
                          : null,
                      variant: AppButtonVariant.secondary,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: AppButton(
                      text: 'Next',
                      onPressed: _currentExerciseIndex < exercises.length - 1
                          ? () => setState(() => _currentExerciseIndex++)
                          : null,
                      variant: AppButtonVariant.secondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoTag(BuildContext context, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSpacing.sm),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
      ),
    );
  }

  Widget _buildWorkoutCustomization(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.tune,
              color: AppColors.warning,
              size: 24,
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              'Customize Workout',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColorsTheme.textPrimary(context),
                  ),
            ),
          ],
        ),

        const SizedBox(height: AppSpacing.md),

        // Skip exercises section
        AppCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Skip Exercises',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColorsTheme.textPrimary(context),
                    ),
              ),
              const SizedBox(height: AppSpacing.sm),
              Text(
                'Select exercises you want to skip during this workout',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                    ),
              ),
              const SizedBox(height: AppSpacing.md),
              ...widget.workout.exercises.map(
                  (exercise) => _buildExerciseSkipOption(context, exercise)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildExerciseSkipOption(
      BuildContext context, WorkoutExercise exercise) {
    final isSkipped = _skippedExercises.contains(exercise.id);

    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Row(
        children: [
          Checkbox(
            value: isSkipped,
            onChanged: (value) {
              setState(() {
                if (value == true) {
                  _skippedExercises.add(exercise.id);
                } else {
                  _skippedExercises.remove(exercise.id);
                }
              });
            },
            activeColor: AppColors.warning,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exercise.exercise.name,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColorsTheme.textPrimary(context),
                        decoration:
                            isSkipped ? TextDecoration.lineThrough : null,
                      ),
                ),
                Text(
                  '${exercise.sets} sets • ${exercise.formattedReps} reps',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColorsTheme.textSecondary(context),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar(BuildContext context) {
    final canStartWorkout = _isReadyToStart();

    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        border: Border(
          top: BorderSide(
            color: AppColorsTheme.border(context),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!canStartWorkout)
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                margin: const EdgeInsets.only(bottom: AppSpacing.md),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSpacing.sm),
                  border: Border.all(
                    color: AppColors.warning.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber,
                      color: AppColors.warning,
                      size: 20,
                    ),
                    const SizedBox(width: AppSpacing.sm),
                    Expanded(
                      child: Text(
                        'Complete equipment checklist to start workout',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.warning,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
            Row(
              children: [
                Expanded(
                  child: AppButton(
                    text: 'Cancel',
                    onPressed: () => Navigator.of(context).pop(),
                    variant: AppButtonVariant.secondary,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  flex: 2,
                  child: AppButton(
                    text: 'Start Workout',
                    onPressed: canStartWorkout ? _startWorkout : null,
                    variant: AppButtonVariant.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  bool get _allEquipmentChecked {
    final requiredEquipment = widget.workout.equipmentNeeded;
    return requiredEquipment.isEmpty ||
        requiredEquipment
            .every((equipment) => _checkedEquipment.contains(equipment));
  }

  int _getCompletedSteps() {
    int steps = 1; // Overview is always completed

    if (_allEquipmentChecked) steps++;
    if (_currentExerciseIndex >= 0) steps++; // Preview step
    steps++; // Customization is always available

    return steps;
  }

  bool _isReadyToStart() {
    return _allEquipmentChecked;
  }

  void _startWorkout() {
    // Create modified workout with skipped exercises
    final modifiedExercises = widget.workout.exercises
        .where((exercise) => !_skippedExercises.contains(exercise.id))
        .toList();

    final modifiedWorkout = Workout(
      id: widget.workout.id,
      userId: widget.workout.userId,
      name: widget.workout.name,
      aiDescription: widget.workout.aiDescription,
      createdAt: widget.workout.createdAt,
      exercises: modifiedExercises,
    );

    // Navigate to workout session screen
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => WorkoutSessionScreen(workout: modifiedWorkout),
      ),
    );
  }

  String _getEquipmentDescription(String equipment) {
    switch (equipment.toLowerCase()) {
      case 'barbell':
        return 'Standard Olympic barbell with weight plates';
      case 'dumbbells':
      case 'dumbbell':
        return 'Adjustable or fixed weight dumbbells';
      case 'bench':
        return 'Flat or adjustable workout bench';
      case 'pull-up bar':
        return 'Mounted or doorway pull-up bar';
      case 'resistance bands':
        return 'Elastic resistance bands with handles';
      case 'kettlebell':
        return 'Cast iron or steel kettlebell';
      case 'medicine ball':
        return 'Weighted medicine ball';
      case 'cable machine':
        return 'Cable pulley system with adjustable weights';
      case 'smith machine':
        return 'Guided barbell system with safety stops';
      case 'leg press':
        return 'Seated leg press machine';
      case 'lat pulldown':
        return 'Seated lat pulldown machine';
      case 'rowing machine':
        return 'Seated cable rowing machine';
      case 'treadmill':
        return 'Motorized running/walking machine';
      case 'stationary bike':
        return 'Indoor cycling exercise bike';
      case 'elliptical':
        return 'Low-impact cardio elliptical machine';
      case 'yoga mat':
        return 'Non-slip exercise mat for floor work';
      case 'foam roller':
        return 'Cylindrical tool for muscle recovery';
      case 'stability ball':
        return 'Large inflatable exercise ball';
      case 'bosu ball':
        return 'Half-dome balance training device';
      case 'trx':
        return 'Suspension trainer with straps';
      case 'battle ropes':
        return 'Heavy training ropes for cardio';
      case 'plyo box':
        return 'Sturdy box for jump training';
      case 'agility ladder':
        return 'Flat ladder for footwork drills';
      case 'none':
        return 'No equipment needed - bodyweight only';
      default:
        return 'Required for this workout';
    }
  }

  int get _estimatedCalories {
    // Estimate calories based on exercise count and duration
    final baseCaloriesPerExercise = 15;
    final totalSets = widget.workout.exercises.fold<int>(
      0,
      (sum, exercise) => sum + exercise.sets,
    );
    return (totalSets * baseCaloriesPerExercise).clamp(50, 800);
  }

  String get _difficultyLevel {
    final exerciseCount = widget.workout.exercises.length;
    final totalSets = widget.workout.exercises.fold<int>(
      0,
      (sum, exercise) => sum + exercise.sets,
    );
    final equipmentCount = widget.workout.equipmentNeeded.length;

    // Calculate difficulty based on various factors
    int difficultyScore = 0;
    difficultyScore += exerciseCount * 2;
    difficultyScore += totalSets;
    difficultyScore += equipmentCount * 3;

    if (difficultyScore <= 10) return 'Beginner';
    if (difficultyScore <= 20) return 'Intermediate';
    return 'Advanced';
  }
}
