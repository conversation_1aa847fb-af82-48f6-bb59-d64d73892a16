import 'package:flutter/material.dart';
import '../../../models/onboarding_data.dart';

class FitnessGoalsStep extends StatefulWidget {
  final OnboardingData onboardingData;
  final VoidCallback onDataChanged;

  const FitnessGoalsStep({
    super.key,
    required this.onboardingData,
    required this.onDataChanged,
  });

  @override
  State<FitnessGoalsStep> createState() => _FitnessGoalsStepState();
}

class _FitnessGoalsStepState extends State<FitnessGoalsStep> {
  final List<String> _selectedGoals = [];
  String? _primaryGoal;
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _updateFromData();
  }

  @override
  void didUpdateWidget(FitnessGoalsStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateFromData();
  }

  void _updateFromData() {
    _selectedGoals.clear();
    _selectedGoals.addAll(widget.onboardingData.fitnessGoals);
    _primaryGoal = widget.onboardingData.primaryGoal;
    _notesController.text = widget.onboardingData.additionalNotes ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  void _updateData() {
    if (_primaryGoal == null && _selectedGoals.isNotEmpty) {
      _primaryGoal = _selectedGoals.first;
    }

    widget.onboardingData.fitnessGoals = List.from(_selectedGoals);
    widget.onboardingData.primaryGoal = _primaryGoal;
    widget.onboardingData.additionalNotes = _notesController.text;
    widget.onDataChanged();
  }

  IconData _getGoalIcon(String goal) {
    switch (goal) {
      case 'Training for a specific sport':
        return Icons.sports;
      case 'Increase strength':
        return Icons.fitness_center;
      case 'Increase stamina':
        return Icons.directions_run;
      case 'Optimize Health and Fitness':
        return Icons.favorite;
      case 'Build muscle mass and size':
        return Icons.sports_gymnastics;
      case 'Weight loss':
        return Icons.trending_down;
      case 'Improve Flexibility':
        return Icons.self_improvement;
      case 'General Fitness':
        return Icons.health_and_safety;
      case 'Stress Relief':
        return Icons.spa;
      default:
        return Icons.star;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select all that apply',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 24),

        // Available goals to select from
        ...OnboardingConstants.fitnessGoals.map((goal) {
          final isSelected = _selectedGoals.contains(goal);

          return GestureDetector(
            onTap: () {
              setState(() {
                if (isSelected) {
                  _selectedGoals.remove(goal);
                  if (_primaryGoal == goal) {
                    _primaryGoal =
                        _selectedGoals.isNotEmpty ? _selectedGoals.first : null;
                  }
                } else {
                  _selectedGoals.add(goal);
                  if (_selectedGoals.length == 1) {
                    _primaryGoal = goal;
                  }
                }
                _updateData();
              });
            },
            child: Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isSelected
                    ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                    : Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color:
                      isSelected ? const Color(0xFF6366F1) : Colors.grey[200]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getGoalIcon(goal),
                    size: 24,
                    color:
                        isSelected ? const Color(0xFF6366F1) : Colors.grey[600],
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          goal,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isSelected
                                ? const Color(0xFF6366F1)
                                : Colors.black,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getGoalDescription(goal),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }),

        if (_selectedGoals.isNotEmpty) ...[
          const SizedBox(height: 32),
          const Text(
            'Sort them in your preferred order',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),

          // Selected goals with reorder handles
          ..._selectedGoals.map((goal) {
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFF6366F1)),
              ),
              child: Row(
                children: [
                  Icon(
                    _getGoalIcon(goal),
                    size: 20,
                    color: const Color(0xFF6366F1),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      goal,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const Icon(
                    Icons.drag_handle,
                    color: Colors.grey,
                    size: 20,
                  ),
                ],
              ),
            );
          }),

          const SizedBox(height: 32),

          // Health coach question
          const Text(
            'Is there anything else I should know as your personal health coach?',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _notesController,
            maxLines: 4,
            onChanged: (value) => _updateData(),
            decoration: InputDecoration(
              hintText:
                  'Share any specific needs, preferences, or considerations...',
              hintStyle: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[200]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[200]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF6366F1)),
              ),
              filled: true,
              fillColor: Colors.grey[50],
              contentPadding: const EdgeInsets.all(16),
            ),
          ),
        ],
      ],
    );
  }

  String _getGoalDescription(String goal) {
    switch (goal) {
      case 'Training for a specific sport':
        return 'Example: Marathon, Volleyball, Tactical Readiness';
      case 'Increase strength':
        return 'Low-rep, high-weight training with longer rest periods for maximum power';
      case 'Increase stamina':
        return 'Focus on building stamina and cardiovascular fitness through endurance exercises';
      case 'Optimize Health and Fitness':
        return 'Overall health and wellness improvement';
      case 'Build muscle mass and size':
        return 'Increase muscle mass and strength through resistance training';
      case 'Weight loss':
        return 'Burn calories and reduce body fat';
      case 'Improve Flexibility':
        return 'Enhance mobility and range of motion';
      case 'General Fitness':
        return 'Overall health and wellness';
      case 'Stress Relief':
        return 'Mental health and relaxation';
      default:
        return 'Improve your fitness journey';
    }
  }
}
