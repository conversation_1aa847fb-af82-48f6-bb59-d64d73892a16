import 'package:flutter/material.dart';
import '../../../models/onboarding_data.dart';

class HealthDietaryStep extends StatefulWidget {
  final OnboardingData onboardingData;
  final VoidCallback onDataChanged;

  const HealthDietaryStep({
    super.key,
    required this.onboardingData,
    required this.onDataChanged,
  });

  @override
  State<HealthDietaryStep> createState() => _HealthDietaryStepState();
}

class _HealthDietaryStepState extends State<HealthDietaryStep> {
  final List<String> _selectedHealthConditions = [];
  final List<String> _selectedDietaryRestrictions = [];
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _updateFromData();
  }

  @override
  void didUpdateWidget(HealthDietaryStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateFromData();
  }

  void _updateFromData() {
    _selectedHealthConditions.clear();
    _selectedHealthConditions.addAll(widget.onboardingData.healthConditions);
    _selectedDietaryRestrictions.clear();
    _selectedDietaryRestrictions
        .addAll(widget.onboardingData.dietaryRestrictions);
    _notesController.text = widget.onboardingData.additionalNotes ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  void _updateData() {
    widget.onboardingData.healthConditions =
        List.from(_selectedHealthConditions);
    widget.onboardingData.dietaryRestrictions =
        List.from(_selectedDietaryRestrictions);
    widget.onboardingData.additionalNotes = _notesController.text;
    widget.onDataChanged();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Share any additional information that might help us personalize your experience better.',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 32),

        // Health Conditions Section
        const Text(
          'Any health conditions we should know about?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),

        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: OnboardingConstants.healthConditions.map((condition) {
            final isSelected = _selectedHealthConditions.contains(condition);
            return FilterChip(
              label: Text(condition),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (condition == 'None') {
                    _selectedHealthConditions.clear();
                    if (selected) _selectedHealthConditions.add(condition);
                  } else {
                    _selectedHealthConditions.remove('None');
                    if (selected) {
                      _selectedHealthConditions.add(condition);
                    } else {
                      _selectedHealthConditions.remove(condition);
                    }
                  }
                });
                _updateData();
              },
              selectedColor: const Color(0xFF6366F1).withValues(alpha: 0.2),
              checkmarkColor: const Color(0xFF6366F1),
              side: BorderSide(
                color: isSelected ? const Color(0xFF6366F1) : Colors.grey[300]!,
              ),
              backgroundColor: Colors.grey[50],
            );
          }).toList(),
        ),

        const SizedBox(height: 32),

        // Dietary Restrictions Section
        const Text(
          'Any dietary restrictions or preferences?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),

        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: OnboardingConstants.dietaryOptions.map((diet) {
            final isSelected = _selectedDietaryRestrictions.contains(diet);
            return FilterChip(
              label: Text(diet),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (diet == 'No restrictions') {
                    _selectedDietaryRestrictions.clear();
                    if (selected) _selectedDietaryRestrictions.add(diet);
                  } else {
                    _selectedDietaryRestrictions.remove('No restrictions');
                    if (selected) {
                      _selectedDietaryRestrictions.add(diet);
                    } else {
                      _selectedDietaryRestrictions.remove(diet);
                    }
                  }
                });
                _updateData();
              },
              selectedColor: const Color(0xFF6366F1).withValues(alpha: 0.2),
              checkmarkColor: const Color(0xFF6366F1),
              side: BorderSide(
                color: isSelected ? const Color(0xFF6366F1) : Colors.grey[300]!,
              ),
              backgroundColor: Colors.grey[50],
            );
          }).toList(),
        ),

        const SizedBox(height: 32),

        // Additional Notes Section
        const Text(
          'Anything else we should know?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Previous injuries, medications, specific goals, etc.',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 16),

        TextFormField(
          controller: _notesController,
          maxLines: 4,
          onChanged: (value) => _updateData(),
          decoration: InputDecoration(
            hintText:
                'Share any specific needs, preferences, or considerations...',
            hintStyle: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF6366F1)),
            ),
            filled: true,
            fillColor: Colors.grey[50],
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }
}
