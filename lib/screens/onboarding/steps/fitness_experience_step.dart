import 'package:flutter/material.dart';
import '../../../models/onboarding_data.dart';

class FitnessExperienceStep extends StatefulWidget {
  final OnboardingData onboardingData;
  final VoidCallback onDataChanged;

  const FitnessExperienceStep({
    super.key,
    required this.onboardingData,
    required this.onDataChanged,
  });

  @override
  State<FitnessExperienceStep> createState() => _FitnessExperienceStepState();
}

class _FitnessExperienceStepState extends State<FitnessExperienceStep> {
  String? _cardioLevel;
  String? _weightliftingLevel;
  final TextEditingController _commentsController = TextEditingController();

  final List<String> _fitnessLevels = [
    'Low',
    'Beginner',
    'Moderate',
    'Advanced',
    'Elite'
  ];

  @override
  void initState() {
    super.initState();
    _updateFromData();
  }

  @override
  void didUpdateWidget(FitnessExperienceStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateFromData();
  }

  void _updateFromData() {
    _cardioLevel = _mapLevelToString(widget.onboardingData.cardioLevel);
    _weightliftingLevel =
        _mapLevelToString(widget.onboardingData.weightliftingLevel);
    _commentsController.text = widget.onboardingData.additionalNotes ?? '';
  }

  @override
  void dispose() {
    _commentsController.dispose();
    super.dispose();
  }

  String _mapLevelToString(int? level) {
    if (level == null) return 'Low';
    if (level <= 2) return 'Low';
    if (level <= 4) return 'Beginner';
    if (level <= 6) return 'Moderate';
    if (level <= 8) return 'Advanced';
    return 'Elite';
  }

  int _mapStringToLevel(String level) {
    switch (level) {
      case 'Low':
        return 1;
      case 'Beginner':
        return 3;
      case 'Moderate':
        return 5;
      case 'Advanced':
        return 7;
      case 'Elite':
        return 9;
      default:
        return 1;
    }
  }

  void _updateData() {
    widget.onboardingData.cardioLevel =
        _cardioLevel != null ? _mapStringToLevel(_cardioLevel!) : null;
    widget.onboardingData.weightliftingLevel = _weightliftingLevel != null
        ? _mapStringToLevel(_weightliftingLevel!)
        : null;
    widget.onboardingData.additionalNotes = _commentsController.text;
    widget.onDataChanged();
  }

  String _getCardioDescription() {
    switch (_cardioLevel) {
      case 'Low':
        return 'I get winded easily during light activities.';
      case 'Beginner':
        return 'I can do light cardio for short periods.';
      case 'Moderate':
        return 'I can maintain a moderate pace for 20-30 minutes.';
      case 'Advanced':
        return 'I have good endurance and can run 5+ miles.';
      case 'Elite':
        return 'I run long distance (6 miles/10km or more).';
      default:
        return 'I get winded easily during light activities.';
    }
  }

  String _getWeightliftingDescription() {
    switch (_weightliftingLevel) {
      case 'Low':
        return 'I have never lifted weights or rarely go to the gym.';
      case 'Beginner':
        return 'I am a beginner and have not regularly been to the gym.';
      case 'Moderate':
        return 'I am comfortable with basic exercises and gym equipment.';
      case 'Advanced':
        return 'I have been lifting consistently and know proper form.';
      case 'Elite':
        return 'I am an experienced lifter with advanced techniques.';
      default:
        return 'I have never lifted weights or rarely go to the gym.';
    }
  }

  Widget _buildLevelSelector(String title, String description,
      String? selectedLevel, Function(String?) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          description,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 16),

        // Radio button row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: _fitnessLevels.map((level) {
            final isSelected = selectedLevel == level;
            return Expanded(
              child: GestureDetector(
                onTap: () => onChanged(level),
                child: Column(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected
                              ? const Color(0xFF6366F1)
                              : Colors.grey[400]!,
                          width: 2,
                        ),
                        color: isSelected
                            ? const Color(0xFF6366F1)
                            : Colors.transparent,
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.circle,
                              size: 12,
                              color: Colors.white,
                            )
                          : null,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      level,
                      style: TextStyle(
                        fontSize: 12,
                        color: isSelected
                            ? const Color(0xFF6366F1)
                            : Colors.grey[600],
                        fontWeight:
                            isSelected ? FontWeight.w500 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Help us understand where you are in your fitness journey.',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 32),

        // Cardio Level
        _buildLevelSelector(
          'Cardio Level',
          _getCardioDescription(),
          _cardioLevel,
          (level) {
            setState(() {
              _cardioLevel = level;
            });
            _updateData();
          },
        ),

        const SizedBox(height: 40),

        // Weightlifting Level
        _buildLevelSelector(
          'Weightlifting Level',
          _getWeightliftingDescription(),
          _weightliftingLevel,
          (level) {
            setState(() {
              _weightliftingLevel = level;
            });
            _updateData();
          },
        ),

        const SizedBox(height: 40),

        // Additional Comments
        const Text(
          'Additional comments about your fitness',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),

        TextFormField(
          controller: _commentsController,
          maxLines: 4,
          onChanged: (value) => _updateData(),
          decoration: InputDecoration(
            hintText:
                'Any specific areas you\'d like to focus on?\nPrevious injuries or limitations?',
            hintStyle: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF6366F1)),
            ),
            filled: true,
            fillColor: Colors.grey[50],
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }
}
