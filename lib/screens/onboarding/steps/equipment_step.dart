import 'package:flutter/material.dart';
import '../../../models/onboarding_data.dart';

class EquipmentStep extends StatefulWidget {
  final OnboardingData onboardingData;
  final VoidCallback onDataChanged;

  const EquipmentStep({
    super.key,
    required this.onboardingData,
    required this.onDataChanged,
  });

  @override
  State<EquipmentStep> createState() => _EquipmentStepState();
}

class _EquipmentStepState extends State<EquipmentStep> {
  final List<String> _selectedEquipment = [];

  @override
  void initState() {
    super.initState();
    _updateFromData();
  }

  @override
  void didUpdateWidget(EquipmentStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateFromData();
  }

  void _updateFromData() {
    _selectedEquipment.clear();
    _selectedEquipment.addAll(widget.onboardingData.equipment);
  }

  void _updateData() {
    widget.onboardingData.equipment = List.from(_selectedEquipment);
    widget.onDataChanged();
  }

  IconData _getEquipmentIcon(String equipment) {
    switch (equipment) {
      case 'No Equipment':
        return Icons.sports_martial_arts;
      case 'Dumbbells':
        return Icons.fitness_center;
      case 'Barbell':
        return Icons.hardware;
      case 'Resistance Bands':
        return Icons.circle_outlined;
      case 'Pull-up Bar':
        return Icons.horizontal_rule;
      case 'Kettlebells':
        return Icons.sports_gymnastics;
      case 'Medicine Ball':
        return Icons.sports_volleyball;
      case 'Jump Rope':
        return Icons.toys;
      case 'Yoga Mat':
        return Icons.airline_seat_flat;
      case 'Foam Roller':
        return Icons.roller_skating;
      case 'Bench':
        return Icons.chair;
      case 'Cable Machine':
        return Icons.settings_input_hdmi;
      case 'Cardio Machines':
        return Icons.directions_run;
      default:
        return Icons.fitness_center;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select all equipment you have access to',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 24),

        // Equipment Selection Dropdown
        GestureDetector(
          onTap: () {
            _showEquipmentModal(context);
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
              color: Colors.white,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _selectedEquipment.isEmpty
                        ? '0 equipments selected'
                        : '${_selectedEquipment.length} equipment${_selectedEquipment.length != 1 ? 's' : ''} selected',
                    style: TextStyle(
                      color: _selectedEquipment.isEmpty
                          ? Colors.grey[600]
                          : Colors.black,
                      fontSize: 16,
                    ),
                  ),
                ),
                Icon(Icons.keyboard_arrow_down, color: Colors.grey[600]),
              ],
            ),
          ),
        ),

        const SizedBox(height: 32),

        // Info Banner
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Your AI trainer will adapt workouts based on your available environments and equipment',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showEquipmentModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  const Expanded(
                    child: Text(
                      'Select Equipment',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Icon(Icons.close, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),

            const Divider(height: 1),

            // Equipment List
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(20),
                itemCount: OnboardingConstants.equipmentOptions.length,
                itemBuilder: (context, index) {
                  final equipment = OnboardingConstants.equipmentOptions[index];
                  final isSelected = _selectedEquipment.contains(equipment);

                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: CheckboxListTile(
                      value: isSelected,
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            _selectedEquipment.add(equipment);
                          } else {
                            _selectedEquipment.remove(equipment);
                          }
                        });
                        _updateData();
                      },
                      title: Row(
                        children: [
                          Icon(
                            _getEquipmentIcon(equipment),
                            size: 20,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 12),
                          Text(
                            equipment,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      activeColor: const Color(0xFF6366F1),
                      contentPadding: EdgeInsets.zero,
                      controlAffinity: ListTileControlAffinity.leading,
                    ),
                  );
                },
              ),
            ),

            // Done Button
            Padding(
              padding: const EdgeInsets.all(20),
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366F1),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Done',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
